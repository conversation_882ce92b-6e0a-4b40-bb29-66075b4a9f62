<script setup lang="ts">
import type { OpenPacketProps } from '@pet/25cny.packet/components/FlipPacket.vue';
import FlipPacket from '@pet/25cny.packet/components/FlipPacket.vue';
import type { ClickPosition, PopupBtnClick } from '@pet/25cny.packet/type';
import { useOpenPage } from '@pet/yau.yoda';
import { ref } from 'vue-demi';

import { useABTestModel } from '@/models/abTestModel';
import { useLocalCalendarModel } from '@/models/localCalendarModel';
import { useTaskModel } from '@/models/taskModel';
import type { LuckRushSudokuView } from '@/services/open-api-docs/home/<USER>/schemas';
const openPage = useOpenPage();

// 扩展后的接口
export type DoubleLLrewdProps = OpenPacketProps & { backPop?: LuckRushSudokuView } & {
    btnClick?: PopupBtnClick;
    openType?: 'none' | 'click' | 'count';
};
const props = defineProps<DoubleLLrewdProps>();

const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'end', val: { event: string; data: OpenPacketProps }): void;
    (e: 'countdown-callback'): void;
}>();

const position = ref<ClickPosition>('close');
const { clickedFlag, checkAndAddCalendar } = useLocalCalendarModel();
const { isCalendarTask, isInpushTask } = useABTestModel();
const { inpushTaskInfo, doTask, calendarTaskInfo } = useTaskModel();

const triggerBtnClick = () => {
    if (props.btnClick) {
        props.btnClick({
            position: position.value,
            destroy: () => {
                emits('close');
            },
        });
    } else {
        emits('close');
    }
};

const isClicking = ref(false);
const onMainClick = async () => {
    if (isClicking.value) {
        return;
    }
    isClicking.value = true;
    position.value = 'mainClick';
    if (isCalendarTask.value) {
        if (calendarTaskInfo.value?.taskInfo && !calendarTaskInfo.value?.finished) {
            doTask(calendarTaskInfo.value?.taskInfo);
            clickedFlag.value = true;
        } else {
            const r = await checkAndAddCalendar({ needRefreshHome: false, needRefreshTask: true });
            if (r) {
                triggerBtnClick();
                clickedFlag.value = true;
            }
        }
        isClicking.value = false;
        return;
    }
    if (isInpushTask.value) {
        doTask(inpushTaskInfo.value?.taskInfo!);
        clickedFlag.value = true;
    }
    isClicking.value = false;
    triggerBtnClick();
};

const mainButtonInfo = computed(() => {
    if (isCalendarTask.value) {
        return {
            linkText: '提醒我明天来',
            task_id: `${calendarTaskInfo.value?.taskInfo?.id ?? ''}`,
        };
    }
    if (isInpushTask.value) {
        return {
            linkText: '提醒我明天来',
            task_id: `${inpushTaskInfo.value?.taskInfo?.id ?? ''}`,
        };
    }
    return {
        linkText: '我知道了',
        task_id: '',
    };
});

const onSubClick = () => {
    position.value = 'subClick';
    triggerBtnClick();
};

const onBottomClick = () => {
    const bottomButton = props.backPop?.bottomButton;
    if (bottomButton?.linkUrl) {
        openPage(bottomButton.linkUrl);
    }
    position.value = 'bottomClick';
    triggerBtnClick();
};

const onAfterLeave = () => {
    emits('end', {
        event: position.value,
        data: props,
    });
};
const onCloseClick = () => {
    position.value = 'close';
    triggerBtnClick();
};
</script>

<template>
    <FlipPacket
        v-bind="props"
        open-type="none"
        :title-context="titleContext"
        :main-button-info="mainButtonInfo"
        @after-leave="onAfterLeave"
        @close="onCloseClick"
        @main-click="onMainClick"
        @sub-click="onSubClick"
        @bottom-click="onBottomClick"
    >
    </FlipPacket>
</template>

<style lang="scss" scoped></style>
