<pet-info lang="json">
{
    "title": "浮点处理",
    "description": "处理浮点等逻辑"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const numberValueData = ref(100);
const numberValueStringData = ref('10');

function numberChange() {
    const num = 1;
    numberValueData.value += num * Math.random() * 3 * Math.random() * 7;
    console.log(numberValueData.value);
}

function numberChangeString() {
    numberValueStringData.value = '9.01';
}
</script>

<template>
    <div class="demo">
        <h5>原始数值{{ numberValueData }}</h5>
        <div class="container">
            <h6>原始未处理to-fixed数据</h6>
            <NumberScroll :number-value="numberValueData" />
            <h6>:to-fixed="2" math="floor"</h6>
            <NumberScroll :number-value="numberValueData" :to-fixed="2" math="floor" />
            <h6>:to-fixed="2" math="round"</h6>
            <NumberScroll :number-value="numberValueData" :to-fixed="2" math="round" />
        </div>
        <button @click="numberChange">增加数值</button>
        <div>
            <h5>原始数值为string{{ numberValueStringData }}</h5>
            <NumberScroll :number-value="numberValueStringData" />
            <button @click="numberChangeString">更新数值</button>
        </div>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 48px 0 24px;
}
</style>
