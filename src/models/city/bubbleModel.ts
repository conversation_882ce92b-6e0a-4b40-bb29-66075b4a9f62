import { createModel, useModel } from '@gundam/model';
import useCaptureDebugLog from '@pet/yau.logger';

import { useCityHomeModel } from './homeModel';

/**
 * 按钮气泡 model
 */
export const bubbleModel = createModel(() => {
    const { chessboard } = useCityHomeModel();

    const logName = 'progressBubble';
    const { log } = useCaptureDebugLog(logName);

    // 是否展示进度条红包气泡，不设置初始值
    const showBubbleType = ref();

    const setBubbleType = (signed: boolean) => {
        showBubbleType.value = signed ? 'final' : 'progress';
    };

    /**
     * 根据主接口下发的绕圈状态，判断展示气泡逻辑
     * 依赖向前冲逻辑在展示完成抽奖弹窗后再请求主接口，如提前请求，会导致气泡切换动效提前执行
     */
    watch(
        () => chessboard.value?.progress?.signed,
        (newVal, oldVal) => {
            // log(`signed oldVal: ${String(oldVal)}, newVal: ${String(newVal)}`);

            if (newVal !== oldVal) {
                // 城市绕圈完成状态发生变化
                if (showBubbleType.value) {
                    log('目前已在展示气泡，等待 800ms 后，切换按钮气泡状态', !!newVal);
                    setTimeout(() => {
                        setBubbleType(!!newVal);
                    }, 800);
                } else {
                    log('目前未展示气泡，直接设置初始值', !!newVal);
                    setBubbleType(!!newVal);
                }
                return;
            }
        },
        { immediate: true },
    );

    return {
        showBubbleType,
    };
});

export const useBubbleModel = () => useModel(bubbleModel);
