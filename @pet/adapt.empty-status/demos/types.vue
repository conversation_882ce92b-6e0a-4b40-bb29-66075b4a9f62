<pet-info lang="json">
{ "title": "展示尺寸", "description": "分为在模块内部展示与页面内部展示", "priority": 88 }
</pet-info>
<script lang="ts" setup>
import EmptyStatus from '../index.vue';
function handleClick() {}
</script>

<template>
    <div class="panel-box">
        <h4>在模块中，如中部弹窗、底部弹窗</h4>
        <EmptyStatus class="icon-color-blue" icon="network" button button-text="刷新" @btn-click="handleClick">
            网络似乎断开了，请刷新重试
        </EmptyStatus>
    </div>
    <div class="panel-box">
        <h4>在页面中</h4>
        <EmptyStatus
            class="icon-color-red"
            icon="network"
            :in-component="false"
            button
            button-text="按钮文本"
            @btn-click="handleClick"
        >
            此处为兜底文案，此处为兜底文案
            <template #subMessage> 此处为副标题此处为副标题此处为副标题此处为副标题，此处为副标题 </template>
        </EmptyStatus>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style scoped lang="scss">
.panel-box {
    font-size: 16px;
}

.icon-color-blue {
    --adapt-empty-status-icon-color: #061429;
    --adapt-empty-status-icon-opacity: 0.8;
}

.icon-color-red {
    --adapt-empty-status-icon-color: #6c120c;
    --adapt-empty-status-icon-opacity: 0.8;
}
</style>
