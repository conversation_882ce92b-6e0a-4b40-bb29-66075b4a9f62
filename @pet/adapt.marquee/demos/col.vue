<pet-info lang="json">
{ "title": "纵向", "description": "纵向滚动" }
</pet-info>

<script setup lang="ts">
import Marquee from '../index.vue';
</script>

<template>
    <Marquee class="marquee-box-col" direction="col" :mask="false">
        <div class="item">滚动文案滚动文案滚动文案1</div>
        <div class="item">滚动文案滚动文案滚动文案2</div>
        <div class="item">滚动文案滚动文案滚动文案3</div>
    </Marquee>
</template>

<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.marquee-box-col {
    height: 100px;
    background: #000;
    color: #fff;
    padding: 0 10px;
}
.marquee-box-col .item {
    margin-bottom: 10px;
}
</style>
