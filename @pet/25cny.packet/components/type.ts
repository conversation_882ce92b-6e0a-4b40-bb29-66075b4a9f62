import type { PictureParams } from './Picture/index.vue';
import type { AD_PictureParams } from './AD_Picture/index.vue';
import type { LuckPictureParams } from './LuckPicture/index.vue';
import type { AmountParams } from './Amount/index.vue';
import type { MainPictureParams } from './MainPicture/index.vue';
import type { CouponParams } from './Coupon/index.vue';
import type { VideoParams } from './Video/index.vue';
export interface UserInfo {
    avatar: string;
    name: string;
    userId?: string;
    amount?: string;
    unit?: string;
}

export interface TitleContext {
    desc: string;
    userList?: UserInfo[];
}

export interface Button {
    linkText?: string | null;
    linkSubText?: string | null;
    linkUrl?: string | null;
    linkType?: string | null;
    icon?: string | null;
    guide?: boolean | null;
}

export interface BottomInfo {
    bottomDesc?: string;
    bottomButton?: Button;
}

export type Amount = AmountParams;

// 新增展示模块的类型
export type PrizeDetail = (
    | AmountParams
    | PictureParams
    | AD_PictureParams
    | MainPictureParams
    | LuckPictureParams
    | CouponParams
    | VideoParams
)[];
