import mockjs from 'mockjs';
export default mockjs.mock({
    result: '@integer(1, 100)',
    message: '@string',
    data: {
        inpushList: [
            {
                inpushType: {
                    data: '@string',
                },
                title: '@title',
                content: '@string',
                assistedUserInfo: [
                    {
                        userName: '@string',
                        headUrl: '@url(https)',
                    },
                    {
                        userName: '@string',
                        headUrl: '@url(https)',
                    },
                ],
                iconUrl: '@url(https)',
                buttonView: {
                    linkType: {
                        data: '@string',
                    },
                    linkText: '@string',
                    linkSubText: '@string',
                    linkUrl: '@url(https)',
                    icon: '@string',
                    iconText: '@string',
                    anchorId: '@integer(1, 100000)',
                    sudokuStartTime: '@dateTime',
                },
            },
        ],
        nextTimeMills: 60000,
        needRefreshPopup: '@boolean',
    },
    timestamp: '@integer(1, 100)',
    hostname: '@string',
    error_msg: '@string',
});

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
