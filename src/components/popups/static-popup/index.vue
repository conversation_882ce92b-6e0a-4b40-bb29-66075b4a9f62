<script setup lang="ts">
import type { Props as PilotProps } from '@pet/adapt.bezier-path-fly/index.vue';
import type { LightType } from '@pet/adapt.popup/usePopupLight';
import type { AdaptTransitionName } from '@pet/adapt.transition/types';
import type { AudioItem } from '@yoda/audio';

import CommonModal from '@/components/common-modals/CommonModal.vue';
import { useLogger } from '@/init/logger';
import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';
import { POPUP_ACTION } from '@/utils/log/type';
import type { ClickPosition, PopupBtnClick } from '@/utils/popupTransform/types';

const props = withDefaults(
    defineProps<{
        popupType?: PopupType;
        type?: 'dialog' | 'layer';
        show?: boolean;
        showClose?: boolean;
        hideMask?: boolean;
        sponsorLogo?: string;
        title?: string;
        subTitle?: string;
        desc?: string;
        icon?: string;
        message?: string;
        mainButton?: string;
        subButton?: string;
        btnClick?: PopupBtnClick;
        flyToTarget?: string;
        cloneFlyToTarget?: string;
        notFlyToTarget?: Ref<boolean>;
        cloneTargetSelector?: string;
        clonePilotProps?: PilotProps;
        lightType?: LightType;
        sponsorText?: string;
        bottomText?: string;
        playPopupShowSound?: AudioItem;
        aniType?: AdaptTransitionName;
        btnLoading?: MaybeRef<boolean>;
    }>(),
    {
        showClose: true,
        hideMask: false,
        lightType: 'normal',
    },
);

const getCustomStyle = computed(() => {
    return {
        '--adapt-layer-main-margin-top': '0px',
        '--adapt-layer-image-size': 'auto',
        '--adapt-layer-main-button-primary-background-image':
            'linear-gradient(to right, #FF7001 0%, #FC2D39 49%, #F31906 100%)',
    };
});

const { sendShow, sendClick } = useLogger();
watch(
    () => props.show,
    async (val) => {
        if (val) {
            sendShow(POPUP_ACTION.CORE_POP, {
                popup_type: props.popupType,
                brand_name: props.sponsorText ?? '',
                title: props.title ?? '',
                button_name: props.mainButton ?? '',
            });
        }
    },
    { immediate: true },
);

const btnClick = (params: { position: ClickPosition; destroy: () => void }) => {
    if (params.position === 'mainClick') {
        sendClick(POPUP_ACTION.CORE_POP, {
            popup_type: props.popupType,
            brand_name: props.sponsorText ?? '',
            title: props.title ?? '',
            button_name: props.mainButton ?? '',
        });
    }
    if (props.btnClick) {
        props.btnClick(params);
    }
};
</script>

<template>
    <CommonModal v-bind="{ ...props, btnClick }" :custom-style="getCustomStyle">
        <!-- <slot name="bottomDesc">
            <div class="content-img">{{ bottomDesc }}</div>
        </slot> -->
    </CommonModal>
</template>

<style scoped lang="scss">
.content-img {
    position: relative;
    top: 10px;
}
</style>
