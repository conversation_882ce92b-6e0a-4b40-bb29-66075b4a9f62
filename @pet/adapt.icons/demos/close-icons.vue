<pet-info lang="json">
{ "title": "关闭", "description": "所有关闭操作", "priority": 88 }
</pet-info>

<script setup lang="ts">
import CloseBigSimpleIcon from '../close-big-simple-svg.vue';
import CloseBigSolidIcon from '../close-big-solid-svg.vue';
import CloseBigLineIcon from '../close-big-line-svg.vue';
import CloseSmallSimpleIcon from '../close-small-simple-svg.vue';
import CloseSmallSolidIcon from '../close-small-solid-svg.vue';
import CloseSmallLineIcon from '../close-small-line-svg.vue';
</script>

<template>
    <div>
        <h3>大: 弹窗关闭</h3>
        <div class="item">
            <CloseBigSimpleIcon class="icon icon-big-1" />
            <h4>
                <p>close-big-simple-svg.vue</p>
            </h4>
        </div>
        <div class="item">
            <CloseBigSolidIcon class="icon icon-big-2" />
            <h4>
                <p>close-big-solid-svg.vue</p>
            </h4>
        </div>
        <div class="item">
            <CloseBigLineIcon class="icon icon-big-2" />
            <h4>
                <p>close-big-line-svg.vue</p>
            </h4>
        </div>
        <h3>小：</h3>
        <div class="item">
            <CloseSmallSimpleIcon class="icon icon-big-1" />
            <h4>
                <p>close-small-simple-svg.vue</p>
            </h4>
        </div>
        <div class="item">
            <CloseSmallSolidIcon class="icon icon-big-1" />
            <h4>
                <p>close-small-solid-svg.vue</p>
            </h4>
        </div>
        <div class="item">
            <CloseSmallLineIcon class="icon icon-big-1" />
            <h4>
                <p>close-small-line-svg.vue</p>
            </h4>
        </div>
    </div>
</template>

<style src="../assets/css/demo.scss" lang="scss" scoped></style>

<style lang="scss" scoped>
.icon-big-1 {
    width: 16px;
    height: 16px;
}

.icon-big-2 {
    width: 40px;
    height: 40px;
}
</style>
