import { createGlobalState } from '@vueuse/core';
import { computed, ref, type ComputedRef } from 'vue';

/**
 * cdn 降级等级 P0最高，NONE 不降级
 */
export type CDNLevelType = 'P0' | 'P1' | 'P2' | 'NONE';

/**
 * 兜底类型
 */
export type DowngradeType =
    /** 兜底灰色 */
    | 'default'
    /** 兜底不显示 */
    | 'hidden';

export const useCdnLevel = createGlobalState(() => {
    /** 当前降级等级 */
    const downgradeLevel = ref<CDNLevelType>('NONE');

    /** 对应降级等级是否生效 */
    const downgradeState = computed(() => {
        const levels: CDNLevelType[] = ['P0', 'P1', 'P2', 'NONE'];
        const currentLevelIndex = levels.indexOf(downgradeLevel.value);
        return levels.reduce(
            (acc, level, index) => {
                acc[level] = !(level === 'NONE') && index >= currentLevelIndex;
                return acc;
            },
            {} as Record<CDNLevelType, boolean>,
        );
    });

    const switchCdnLevel = (state: CDNLevelType) => {
        downgradeLevel.value = state;
    };

    return {
        downgradeLevel,
        downgradeState,
        switchCdnLevel,
    };
});
