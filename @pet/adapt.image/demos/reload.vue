<script setup lang="ts">
import { ref } from 'vue-demi';

import XImage from '../index.vue';

const imgSrc = ref('https://memeprod.sgp1.digitaloceanspaces.com/meme/314c19b6b3d8a7ee7e07265cd5d27a64.png');
const imgSrcErr = ref('');

const imgSrcList = ref([
    'https://ali2.a.yximgs.com/kos/nlav10395/growth/level/dark/1.png',
    'https://ali2.a.yximgs.com/kos/nlav10395/growth/level/dark/2.png',
    'https://ali2.a.yximgs.com/kos/nlav10395/growth/level/dark/3.png',
]);

const fallBackSrc = 'https://icons.iconarchive.com/icons/paomedia/small-n-flat/256/sign-error-icon.png';

const imgSrcErrLoad = ref('');

function handleError() {
    imgSrcErrLoad.value = '加载失败';
}
</script>

<template>
    <div class="demo">
        <h5>:retry-times=3 加载状态@error: {{ imgSrcErrLoad }}</h5>
        <div class="container">
            <XImage
                v-for="imgSrcItem in imgSrcList"
                :key="imgSrcItem"
                :src="imgSrcItem"
                class="image-item"
                @error="handleError"
            />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
}
.image-item {
    width: 100px;
    height: 100px;
}
div {
    font-size: 16px;
}
</style>
