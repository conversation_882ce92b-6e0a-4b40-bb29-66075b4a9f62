export interface UserInfo {
    avatar: string;
    name: string;
    userId?: string;
    amount?: string;
    unit?: string;
}

export type InfoType = 'group' | 'normal' | 'invite' | 'more';

export interface GroupInfo {
    type: 'group';
    userList: UserInfo[];
}

export interface NormalInfo {
    type: 'normal';
    userInfo: UserInfo;
}

export interface InviteInfo {
    type: 'invite';
}

export interface MoreInfo {
    type: 'more';
    count: number;
}

export type RelationItemInfo = GroupInfo | NormalInfo | InviteInfo | MoreInfo;

export interface AssistInfo {
    desc: string;
    userList: UserInfo[];
}

export interface ProgressInfo {
    startText?: string;
    endText?: string;
    percent: number;
}

export interface RelationInfo {
    desc: string;
    totalCount?: number;
    userList: UserInfo[];
}

export interface Button {
    linkText: string;
}

interface PacketStyle {
    packetType?: 'OPEN' | 'CLOSE' | 'ACTIVITY' | string;
    theme?: 'RED' | 'GOLD' | string;
    cheerLottie?: boolean;
    midBtnType?: 'UNLOCK' | 'LOCK' | string;
    openPacketGapSize?: 'SMALL' | 'MIDDLE' | 'LARGE' | string;
}

export interface BottomDesc {
    desc: string;
    linkText: string;
}

export interface MessageCard {
    title: string;
    desc: string;
}

interface PacketDetails {
    assistInfo?: AssistInfo;
    amount?: string;
    unit?: string;
    amountDesc?: string;
    progressInfo?: ProgressInfo;
    relationInfo?: RelationInfo;
    style?: PacketStyle;
    bottomDesc?: BottomDesc;
    messageCard?: MessageCard;
    midPic?: string;
}

export interface PacketProps {
    // 弹窗配置
    show?: boolean;
    flyTarget?: string;
    innerScroll?: boolean;
    needTabSpace?: boolean;
    // 弹窗数据
    popupType?: string;
    popupName?: string;
    headerTitle?: string;
    headerSubTitle?: string;
    assistTitle?: string;
    title?: string;
    mainBtn?: Button;
    subBtn?: Button;
    details?: PacketDetails;
}

export type PilotType = {
    boot: () => void;
};
