import { createModel, useModel } from '@gundam/model';
import { useRestMutationWithType } from '@gundam/model/utils/apiConfig';
import { toast } from '@pet/adapt.toast';
import { sleep } from '@pet/yau.core';
import { sendEvent } from '@pet/yau.radar';
import { injectVisibility } from '@pet/yau.yoda/event';
import { whenever } from '@vueuse/core';
import { invoke } from '@yoda/bridge';

import {
    summer2025WishTravelTeamControllerAddPushSwitch,
    summer2025WishTravelTeamControllerSendRemindPush,
} from '@/services/open-api-docs/home/<USER>/vue-apollo-model-client';
import { TeamPushSwitchStatus } from '@/services/open-api-docs/home/<USER>/schemas';
import { reportKeyActionEnd, reportKeyActionStart } from '@/utils/log/keyActionLog';

import { useTeamDataModel } from './team/teamData.model';
import { getDataIfNotError, getErrorMessage } from './utils/util';

export const TeamPushModel = createModel(() => {
    // 发送组队push
    const { mutate: sendPush, info: sendRemindPushInfo } = useRestMutationWithType(
        summer2025WishTravelTeamControllerSendRemindPush,
    )();
    const sendPushLoading = computed(() => sendRemindPushInfo.loading);
    // 记录push开关状态
    const { mutate: addPushSwitch, info: pushSwitchInfo } = useRestMutationWithType<
        typeof summer2025WishTravelTeamControllerAddPushSwitch
    >(summer2025WishTravelTeamControllerAddPushSwitch)();
    const pushSwitchLoading = computed(() => pushSwitchInfo.loading);

    // 组队面板接口下发的push开关状态
    const { pushSwitchStatus, teamPushSwitch } = useTeamDataModel();
    // 推送开关状态
    const selected = ref(false);
    const pushPermissionResolver = ref<((value: boolean) => void) | null>(null);
    const visibilityUtils = injectVisibility();
    const { useVisibilityChange } = visibilityUtils ?? {};

    useVisibilityChange?.(async (visible) => {
        if (visible) {
            if (pushPermissionResolver.value !== null) {
                try {
                    const { permitted } = await invoke('system.getPushPermission');
                    if (permitted) {
                        // toast('您已成功打开推送');
                        // await sleep(1000);
                        pushPermissionResolver.value(true);
                    } else {
                        pushPermissionResolver.value(false);
                    }
                } catch (e) {
                    sendEvent({
                        name: 'getPushPermission-error',
                        message: '打开推送开关失败',
                        extra_info: JSON.stringify(e),
                    });
                    pushPermissionResolver.value(false);
                }
            }
        }
    });
    /**
     * 1. 如果下发default，根据机型展示默认
     * 2. 否则下发什么展示什么
     */
    whenever(
        pushSwitchStatus,
        async (status) => {
            try {
                // 【注意】服务端根据机型展示开关状态。这里的处理主要是为了放量兼容
                if (status === TeamPushSwitchStatus.DEFAULT) {
                    const { mod } = await invoke('system.getDeviceInfo');
                    if (!/oppo|reno/i.test(mod)) {
                        selected.value = false;
                    } else {
                        selected.value = true;
                    }
                } else {
                    selected.value = status === TeamPushSwitchStatus.OPEN;
                }
            } catch (e) {
                sendEvent({
                    name: 'getDeviceInfo-error',
                    message: '获取设备信息失败',
                    extra_info: JSON.stringify({ e, status }),
                });
            }
        },
        {
            immediate: true,
        },
    );

    const showPushEnsureDialog = async () => {
        const { target } = await invoke('ui.showDialog', {
            title: '打开推送通知',
            content: '打开推送通知，及时接收重要消息提醒',
            confirmButtonText: '去打开',
            cancelButtonText: '忽略',
        });
        return target;
    };
    /**
     * push开关切换
     * 1. 默认为非勾选态
        a. 如用户点击开启，则判断当前用户push开关状态
            i. 如系统开关为打开则开启开关，toast反馈
            ii. 如系统开关为关闭则调起系统开启push权限，如用户开启则该开关开启，如用户拒绝则该开关保持关闭
        b. 如用户点击关闭，点击按钮变为未选择态时，toast反馈“您已成功取消订阅”
     */
    const selectedChange = async () => {
        if (pushSwitchLoading.value) {
            return;
        }
        const newStatus = !selected.value;
        // 如果用户想打开push开关
        try {
            if (newStatus) {
                const { permitted } = await invoke('system.getPushPermission');
                if (permitted) {
                    // 1. 用户已经开启通知权限
                    await addPushSwitch({
                        opened: true,
                    });
                } else {
                    // 2. 用户没有打开系统权限
                    const action = await showPushEnsureDialog();
                    if (action === 'confirm') {
                        reportKeyActionStart({
                            name: 'openPushPermission',
                        });
                        await invoke('system.openPushPermission');
                        const open = await new Promise((resolve) => {
                            pushPermissionResolver.value = resolve;
                        });
                        reportKeyActionEnd({
                            name: 'openPushPermission',
                            extra_info: {
                                open,
                            },
                        });
                        pushPermissionResolver.value = null;
                        if (open) {
                            await addPushSwitch({
                                opened: true,
                            });
                        } else {
                            console.log('您没打开推送通知');
                            return;
                        }
                    }
                }
            } else {
                // 关闭
                await addPushSwitch({
                    opened: false,
                });
            }
        } catch (e) {}
    };

    watch(
        () => pushSwitchLoading.value,
        (loading) => {
            console.log('🎈pushSwitchInfo', pushSwitchInfo, 'loading', loading);
            if (!loading) {
                if (!pushSwitchInfo.error) {
                    if (selected.value) {
                        toast('您已成功取消订阅');
                    } else {
                        toast('您已成功订阅');
                    }
                    selected.value = !selected.value;
                } else {
                    toast(getErrorMessage(pushSwitchInfo));
                }
            }
        },
    );

    /**
     * 发送组队push，弹toast
     */
    const sendRemindPush = async () => {
        if (sendPushLoading.value) {
            return;
        }
        sendPush({});
    };

    watch(
        () => sendPushLoading.value,
        (loading) => {
            console.log('🎈sendRemindPushInfo', sendRemindPushInfo, 'loading', loading);
            if (!loading) {
                if (sendRemindPushInfo.data) {
                    toast(getDataIfNotError(sendRemindPushInfo));
                } else {
                    toast(getErrorMessage(sendRemindPushInfo));
                }
            }
        },
    );
    return { selected, pushSwitchStatus, teamPushSwitch, selectedChange, sendRemindPush, sendPushLoading };
});
export const useTeamPushModel = () => useModel(TeamPushModel);
