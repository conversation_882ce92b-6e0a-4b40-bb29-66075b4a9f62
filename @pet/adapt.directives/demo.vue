<script setup lang="ts">
import { ref, computed } from 'vue-demi';
import { vClickOutside } from '@pet/adapt.directives/click-outside';

const out = ref(false);

function clickOutside() {
    out.value = !out.value;
}

const changColor = computed(() => (out.value ? { backgroundColor: 'orange' } : {}));
</script>

<template>
    <div>
        <h4>指令:v-click-outside</h4>
        <div v-click-outside="clickOutside" class="box" :style="changColor">点击盒子外部，盒子变色</div>
    </div>
</template>

<style>
body {
    font-size: 32px;
}
</style>

<style lang="scss" scoped>
.box {
    width: 200px;
    height: 200px;
    background: #ccc;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
