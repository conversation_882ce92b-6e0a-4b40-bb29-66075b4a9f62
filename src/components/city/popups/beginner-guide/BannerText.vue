<script setup lang="ts">
import StrokeText from '@pet/adapt.stroke-text/index.vue';
import { onMounted } from 'vue';

defineProps<{
    text: string;
    subText?: string;
    type?: string;
}>();
const emit = defineEmits<{
    (e: 'end'): void;
}>();

const titleEnd = () => {
    emit('end');
};

const handleAnimationEnd = (e: AnimationEvent) => {
    if (e.animationName.includes('hash2e495ed4_0_keyframe_2')) {
        titleEnd();
    }
};
</script>

<template>
    <div class="ready-banner" :style="{ bottom: type === 'challenge' ? '381px' : '317px' }" @click.stop="() => {}">
        <div class="text-wrapper start-anim" @animationend="handleAnimationEnd">
            <StrokeText class="guide-text" :text="text" />
            <StrokeText
                v-if="subText"
                :class="['guide-text', `guide-text-${type === 'challenge' ? 3 : 2}`]"
                :text="subText"
            />
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import '@pet/adapt.fonts/style/kuaiyuanhui.css';
.ready-banner {
    --adapt-stroke-text-font-family: kuaiyuanhui;
    /* 文字字重 */
    --adapt-stroke-text-font-weight: 400;
    /* 文字字号 */
    --adapt-stroke-text-font-size: 32px;
    /* 文字行高 */
    --adapt-stroke-text-line-height: 36px;
    /* 文字描边宽度 */
    --adapt-stroke-text-stroke-width: 6px;
    /* 文字颜色 */
    --adapt-stroke-text-color: linear-gradient(92.68deg, #ff6b00 7.48%, #ff0f00 83.91%);
    /* 文字描边色 */
    --adapt-stroke-text-stroke-color: #fff;
    /* 文字间距 */
    --adapt-stroke-text-letter-spacing: 0px;
    --adapt-stroke-text-skew: skewX(-20deg);

    position: fixed;
    // bottom: 317px;
    width: 414px;
    height: 200px;
    display: flex;
    align-items: center;
    overflow: hidden;
    z-index: 999;

    .guide-text {
        z-index: 1;
        position: relative;
        vertical-align: top;
        margin-top: 0;
    }

    .guide-text-2 {
        transform: translateX(20px) skewX(-20deg);
    }
    .guide-text-3 {
        transform: skewX(-20deg);
    }

    .text-wrapper {
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        width: 100%;
        height: 100%;
    }

    .start-anim {
        opacity: 0.2;
        transform: scale(2.6);
        animation:
            hash2e495ed4_0_keyframe_0 200ms 0s cubic-bezier(0.33, 0, 0.67, 1) forwards,
            hash2e495ed4_0_keyframe_1 333ms 0s cubic-bezier(0.33, 0, 0.67, 1) forwards,
            hash2e495ed4_0_keyframe_2 233ms 2000ms cubic-bezier(0.17, 0, 0.83, 1) forwards;
    }
}

@keyframes hash2e495ed4_0_keyframe_0 {
    0% {
        opacity: 0.2;
    }
    100% {
        opacity: 1;
    }
}

@keyframes hash2e495ed4_0_keyframe_1 {
    0% {
        transform: scale(2.6, 2.6);
    }
    39.9% {
        transform: scale(0.9, 0.9);
    }
    69.97% {
        transform: scale(1.3, 1.3);
    }
    100% {
        transform: scale(1.2, 1.2);
    }
}

@keyframes hash2e495ed4_0_keyframe_2 {
    0% {
        transform: scale(1.2, 1.2);
    }
    57.08% {
        transform: scale(1.4, 1.4);
    }
    100% {
        transform: scale(0.2, 0.2);
    }
}
</style>
