import { createUseModel } from '@gundam/model';

import { SoundType, useAudioModel } from './audioModel';
import { QUEUE_TAGS_TYPE, TaskType, usePopupModel } from './popup.model';

export const useBannerModel = createUseModel(() => {
    const { openPopup } = usePopupModel();
    const { playSound } = useAudioModel();

    /**
     * 组队成功
     */
    const openTeamTextPopup = () => {
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerText.vue'),
            data: {
                text: '你已完成组队',
                subText: '记得叫队友打卡哦',
                type: 'challenge',
            },
        });
        return task.end;
    };

    /**
     * 打卡成功
     */
    const openBannerTextPopup = () => {
        playSound(SoundType.SIGNED);
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerText.vue'),
            data: {
                text: '太厉害了!',
                subText: '打卡成功',
            },
        });
        return task.end;
    };

    /**
     * 0714迭代3-组队成功横幅变为花字
     */
    const openTeamSuccessPopup = () => {
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerText.vue'),
            data: {
                text: '真棒!你完成了组队',
                subText: '挑战成功',
                type: 'challenge',
            },
        });
        return task.end;
    };

    /**
     * 挑战成功花字
     */
    const openChallengeSuccessBanner = () => {
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerText.vue'),
            data: {
                text: '真棒!',
                subText: '挑战成功',
                type: 'challenge',
            },
        });
        return task.end;
    };
    /**
     * 挑战任务下线花字
     */
    const openChallengeNullBanner = async () => {
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerText.vue'),
            data: {
                text: '太幸运了',
                subText: '本次挑战直接成功',
                type: 'challenge',
            },
        });
        await task.end;
    };

    const openBannerPopup = () => {
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerPopup.vue'),
            data: {
                rightText: '太棒了\n今日直达当日站点！',
            },
            options: {
                name: TaskType.ACTIVE_POPUP,
                queueTags: [QUEUE_TAGS_TYPE.POPUP],
            },
        });

        return task;
    };

    return {
        openBannerTextPopup,
        openChallengeSuccessBanner,
        openChallengeNullBanner,
        openBannerPopup,
        openTeamSuccessPopup,
        openTeamTextPopup,
    };
});
