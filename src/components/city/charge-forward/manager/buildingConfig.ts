/**
 * 建筑：
 * 1. 服务端会下发一部分商业化类型的建筑
 * 2. 前端维护每张地图的主视觉特殊动效
 */

import type { BuildingInfoView } from '@/services/open-api-docs/home/<USER>/schemas';

import { MapKeyType } from './constant';

const DragonLoader = () => import('../assets/effects/dragon/index.vue');
const WheelLoader = () => import('../assets/effects/wheel/index.vue');
const WindMillLoader = () => import('../assets/effects/windmill/index.vue');
export interface BuildingItemConfig extends BuildingInfoView {
    loader?: () => Promise<any>;
    interval?: number;

    /**
     * 展示段位
     * 展示坑位为 [pos - showSegment[0], pos + showSegment[1]]
     */
    showSegment?: [number, number] | null | undefined;
}

enum BuildingTypeEnum {
    Decoration = 'decoration',
    Guide = 'guide',
}

const DragonBuilding: BuildingItemConfig = {
    type: BuildingTypeEnum.Decoration,
    name: 'dragon',
    pos: [7],
    loader: DragonLoader,
    interval: 24,
    // 坑位3到7展示
    showSegment: [4, 0],
};
const WheelBuilding: BuildingItemConfig = {
    type: BuildingTypeEnum.Decoration,
    name: 'wheel',
    pos: [7],
    loader: WheelLoader,
    interval: 24,
};
const WindMillBuilding: BuildingItemConfig = {
    type: BuildingTypeEnum.Decoration,
    name: 'windmill',
    pos: [7],
    loader: WindMillLoader,
    interval: 24,
};
// export const MainBuildingConfig = {
//     [MapKeyType.JingHua]: DragonBuilding,
//     [MapKeyType.XiLe]: WheelBuilding,
//     [MapKeyType.FuRao]: WindMillBuilding,
//     // [MapKeyType.Default]: JingHuaMapConfig,
// };
