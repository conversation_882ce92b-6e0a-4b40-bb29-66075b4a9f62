import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

import { POPUP_ACTION, type BasePopupLog, type LogTransformFn } from './type';
import { isPopup, type SummerPopup } from '../popupTransform/types';

export const recruitPopupType = [
    PopupType.LS_RECRUIT_QUICK_CARD_TRIAL,
    PopupType.LS_RECRUIT_QUICK_CARD_DISCOUNT,
    PopupType.LS_RECRUIT_AI_BOOST_PACK_TRAIL,
    PopupType.LS_IDEAT_BOOM_ORDER_CARD_TRIAL,
    PopupType.LS_IDEAT_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT,
    PopupType.LS_DREAM_BUILDER_BOOM_ORDER_CARD_TRIAL,
    PopupType.LS_DREAM_BUILDER_BOOM_ORDER_CARD_TIME_LIMIT_DISCOUNT,
] satisfies PopupType[];

export interface GuidePopupLogValue extends BasePopupLog {
    popup_type: string;
    button_name: string;
}

export const recruitPopupLogTransform: LogTransformFn<GuidePopupLogValue> = (popupData: SummerPopup) => {
    if (isPopup(popupData, recruitPopupType)) {
        return {
            show: POPUP_ACTION.CORE_POP,
            click: POPUP_ACTION.CORE_POP,
            close: POPUP_ACTION.CORE_POP,
            // forward: hasForward ? POPUP_ACTION.GUILD_FORWARD : undefined,
            logValue: {
                popup_type: popupData.popupType,
                brand_name: popupData.sponsorText ?? '',
                title: popupData.title ?? '',
                good_id: '',
                coupon_id: '',
                button_name: popupData.mainButton?.linkText ?? '',
            },
        };
    }
};
