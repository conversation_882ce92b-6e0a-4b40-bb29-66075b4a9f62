<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Picture from "@pet/quantum.PictureImg";
import TVPlayer from '@pet/summer.tvplayer/index.vue';

import { useAnimationPause } from '@/models/animationVisibility';

import deerMov from './assets/deer.mov?url';
import deerMp4 from './assets/deer.mp4?url';
import deerWebm from './assets/deer.webm?url';
import deerPng from './assets/fallback.png';
import fallback from './assets/fallback.png?preset=modern';

const { effectShowStatus } = useDowngradeLevel();
const { isAnimationPause } = useAnimationPause()
const emit = defineEmits<{
    (e: 'ended'): void;
}>();

const urls = {
    mp4: deerMp4,
    mov: deerMov,
    webm: deerWebm,
}
</script>
<template>
    <TVPlayer v-if="effectShowStatus.L1" id="8248" class="tv-wrapper" :urls="urls" :autoplay="true" :loop="true" :placeholder="deerPng" :play="!isAnimationPause" @ended="emit('ended')"/>
    <Picture v-else class="tv-wrapper" :src="fallback" />
</template>
<style lang="scss" scoped>
.tv-wrapper {
    position: relative;
    width: 94px;
    height: 400px;
    left: -24px;
    top: 10px;
}
</style>
