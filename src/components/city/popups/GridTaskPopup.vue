<!-- 关红包格子组件封装 -->
<script setup lang="ts">
import ClosePacket from '@pet/25cny.packet/components/ClosePacket.vue';
import type { ClosePacketProps } from '@pet/25cny.packet/components/ClosePacket.vue';
import type { ClickPosition, PopupBtnClick } from '@pet/25cny.packet/type';
import useCaptureDebugLog from '@pet/yau.logger';
import { ref, computed } from 'vue-demi';

import { useCityHomeModel } from '@/models/city/homeModel';
import type { LuckRushSudokuView } from '@/services/open-api-docs/home/<USER>/schemas';

import { getTransformData } from '../../../utils/popupTransform/gridCommonTaskTransform';

type FullProps = ClosePacketProps & {
    btnClick?: PopupBtnClick;
};

const props = defineProps<FullProps>();

// 处理业务逻辑
const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'end', val: { event: string; data: ClosePacketProps }): void;
}>();

const { log } = useCaptureDebugLog('GridTaskPopup');

const position = ref<ClickPosition>('close');

const triggerBtnClick = () => {
    if (props.btnClick) {
        props.btnClick({
            position: position.value,
            destroy: () => {
                emits('close');
            },
        });
    } else {
        emits('close');
    }
};

const onCloseClick = () => {
    position.value = 'close';
    triggerBtnClick();
};

const onMainClick = () => {
    position.value = 'mainClick';
    triggerBtnClick();
};

const onMidClick = () => {
    if (!props.iconDisabled) {
        position.value = 'mainClick';
        triggerBtnClick();
    }
};

const onAfterLeave = () => {
    emits('end', {
        event: position.value,
        data: props,
    });
};

// 处理进度任务的 UI 更新
const { homeGridTaskPopUp } = useCityHomeModel();

const homeGridTaskPopUpData = computed(() => {
    if (!homeGridTaskPopUp.value) {
        return null;
    }
    return getTransformData(homeGridTaskPopUp.value as LuckRushSudokuView);
});

type DynamicData = {
    title: string;
    subTitle?: string;
};

const showData = ref<DynamicData>({
    title: '',
    subTitle: '',
});

const lastShowData = ref<DynamicData>({
    title: '',
    subTitle: '',
});

watch(
    () => homeGridTaskPopUpData.value,
    () => {
        if (homeGridTaskPopUpData.value) {
            // 主接口下发任务弹窗时，以主接口信息为准
            const { title, subTitle } = homeGridTaskPopUpData.value;
            showData.value.title = title;
            showData.value.subTitle = subTitle;
            // TIPS: 记录上一次展示结果，避免由于主接口不下发(3/3)进度，导致先出现任务进度从(2/3)切换到(0/3)后再关闭任务弹窗的情况 —— 保持(2/3)并任务关闭弹窗
            lastShowData.value.title = title;
            lastShowData.value.subTitle = subTitle;
        } else {
            // 主接口未下发任务弹窗时，使用上次展示信息或初始信息兜底
            showData.value.title = lastShowData.value.title || props.title;
            showData.value.subTitle = lastShowData.value.subTitle || props.subTitle;
        }
    },
    { immediate: true },
);

watch(
    () => props.show,
    () => {
        log('props.show', props.show);
    },
);
</script>

<template>
    <ClosePacket
        v-bind="props"
        :title="showData.title"
        :subTitle="showData.subTitle"
        @after-leave="onAfterLeave"
        @close="onCloseClick"
        @main-click="onMainClick"
        @mid-click="onMidClick"
    >
    </ClosePacket>
</template>

<style lang="scss" scoped></style>
