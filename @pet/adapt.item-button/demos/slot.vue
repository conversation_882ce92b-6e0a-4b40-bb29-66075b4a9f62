<pet-info lang="json">
{ "title": "扩展", "description": "右侧扩展区域" }
</pet-info>
<script setup lang="ts">
import IconMusic from '@pet/adapt.icons/music-svg.vue';
import ToggleSwitch from '@pet/adapt.toggle-switch/index.vue';
import { ref } from 'vue';

import ItemButton from '../index.vue';
const switchValue = ref(false);
</script>

<template>
    <div class="demo">
        <ItemButton>
            <template #icon>
                <IconMusic />
            </template>
            音效设置
            <template #addon>
                <ToggleSwitch v-model:show="switchValue" />
            </template>
        </ItemButton>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    width: 100%;
}
</style>
