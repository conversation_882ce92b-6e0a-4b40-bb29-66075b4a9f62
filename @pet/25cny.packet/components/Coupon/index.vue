<script setup lang="ts">
import Coupon from '@pet/adapt.coupon/index.vue';
import type { PlatformCouponData } from '@pet/adapt.coupon/types';
import { useOpenPage } from '@pet/yau.yoda';

export interface CouponParams {
    prizeType: 'coupon';
    coupon?: PlatformCouponData;
    showUseBtn?: boolean;
    adjustTop?: boolean;
    adjustBottom?: boolean;
}

defineProps<CouponParams>();

defineEmits<{
    (event: 'coupon-click'): void;
}>();

const openPage = useOpenPage();

const onJump = (url: string) => {
    openPage(url ?? '', { forceOpenInNewWebview: true, keepQuery: false });
};
</script>

<template>
    <Coupon
        v-if="coupon"
        :class="['coupon', adjustTop && 'adjust-top', adjustBottom && 'adjust-bottom']"
        :info="coupon"
        size="medium"
        :show-use-btn="showUseBtn"
        no-bkg
        click-all
        :jump-fn="onJump"
        @coupon-click="$emit('coupon-click')"
    />
</template>

<style lang="scss" scoped>
.coupon {
    margin-bottom: 20px;

    :deep(.card-medium) {
        height: 74px;
        width: 260px;
    }
}

.adjust-top {
    margin-top: 23px;
}

.adjust-bottom {
    margin-bottom: 0;
}
</style>
