<pet-info lang="json">
{
    "title": "自定义样式",
    "description": "自定义跑马灯样式"
}
</pet-info>

<script setup lang="ts">
import { MarqueeSlide } from '../index';

const messageList = [
    {
        avatar: 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
        text: '获得1000枚金币',
    },
    {
        avatar: 'https://memeprod.sgp1.digitaloceanspaces.com/meme/314c19b6b3d8a7ee7e07265cd5d27a64.png',
        text: '获得2000枚金币',
    },
    {
        avatar: 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
        text: '获得3000枚金币',
    },
];
</script>

<template>
    <div class="container">
        <MarqueeSlide
            :list="messageList"
            class="marquee-box"
            :animation-duration="1000"
            :stay-duration="3000"
            position="center"
        >
            <template #item="{ data }">
                <div class="row">
                    <div v-if="data" class="line">
                        <img class="avatar" :src="data.avatar" :alt="data.text" />
                        {{ data.text }}
                    </div>
                </div>
            </template>
        </MarqueeSlide>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.container {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 36px;
    width: 300px;
    margin: auto;
    margin-top: 20px;
}

.marquee-box {
    font-size: 18px;
    height: 20px;
    color: #fff;
    --adapt-marquee-slide-transition-timing-function: ease-in-out;
    font-weight: 700;
}

.line {
    display: flex;
    align-items: center;
    padding: 5px 0;
}
.avatar {
    width: 36px;
    height: 36px;
    margin-right: 6px;
}
</style>
