import { invoke } from '@yoda/bridge';

const IS_EXISTED_WIDGET = 'growth.isWidgetExisted';

const MENTION_CLASS = 'com.yxcorp.gifshow.growth.widget.provider.GrowthLiveNoticeWidget22Provider'; // cspell:disable-line

/**
 *  @desc: 控制是否展示桌面组件任务 - 如果用户已经安装则不展示
 *
 */
export async function hasLiveDeskComponent() {
    try {
        const data = await invoke(IS_EXISTED_WIDGET as any, {
            widgetClass: MENTION_CLASS,
        });

        return JSON.stringify({ liveDeskComponent: !!data?.existed });
    } catch (e) {
        return JSON.stringify({ liveDeskComponent: true });
    }
}
