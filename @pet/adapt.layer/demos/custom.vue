<pet-info lang="json">
{ "title": "自定义样式", "description": "" }
</pet-info>
<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import { ref } from 'vue-demi';

import Layer from '../index.vue';

const show = ref(false);
const createComponent = ref(false);

function confirm() {
    // eslint-disable-next-line no-alert
    alert('confirm');
}
function sunConfirm() {
    // eslint-disable-next-line no-alert
    alert('sub-confirm');
}

function handleClose() {
    console.log('close');
}

const isCustom = ref(false);

function showLayer(custom = false) {
    show.value = true;
    createComponent.value = true;
    isCustom.value = custom;
}
</script>

<template>
    <div>
        <Layer
            v-if="createComponent"
            v-model:show="show"
            has-logo
            custom-logo="https://w1-pro.kskwai.com/kos/nlav11066/2022_common_static/business/modal_logo_shanzhashuxia_v1.png"
            title="此处为主标题"
            sub-title="此处为副标题，可用于辅助信息"
            cover="https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2021-12-16/1639634522781.3e4249c181e76b81.png"
            main-btn-text="立即使用"
            main-btn-text-desc="小字啊啊"
            bottom-text="底部文字补充"
            :class="isCustom ? 'custom-layer' : ''"
            @confirm="confirm"
            @sub-confirm="sunConfirm"
            @close="handleClose"
            @after-leave="createComponent = false"
        >
        </Layer>
        <Button class="item-btn" @click="showLayer(false)"> 打开一般蒙层 </Button>
        <Button class="item-btn" @click="showLayer(true)"> 打开自定义蒙层 </Button>
    </div>
</template>

<style>
/* 这里全局配置 */
@import '@pet/adapt.fonts/style/kuaiyuanhui.css';
body {
    --adapt-heading-title-font-family: kuaiyuanhui;
    --adapt-heading-title-font-style: skewX(-7deg);
}
</style>

<style scoped>
.item-btn {
    margin: 0 2px;
}
/* 这里是自定义的样式 */
.custom-layer {
    --adapt-layer-main-button-primary-background-color: rgb(255, 0, 119);
    --adapt-layer-main-button-primary-background-image: url('./main-btn.png');
}
</style>
