<script lang="ts" setup>
import { useIntersectionObserver } from '@vueuse/core';
import { computed, type ComputedRef, ref, toRef, useSlots, type VNode, watch } from 'vue-demi';

import Card from '@pet/adapt.card/index.vue';
import type { CardSize } from '@pet/adapt.card/types';
import TaskConnectionCard from '@pet/25cny.task-list/components/TaskConnectionInfo.vue';
import type { ButtonProps, CardProps, TaskListOptions } from '@pet/25cny.task-list/components/types';
import { isTaskDisabled, isTaskLooksLikeDisabled } from '@pet/work.task-list-core/utils/displayRelatedUtils';
import { useLoggerEmits } from '@pet/work.task-list-core/utils/useLoggerEmits';
import type { TaskTimeLimitedInfo } from '@pet/work.task-list-core/utils/useTimeLimitedTask';
import { useTimeLimitedTaskCountdown } from '@pet/work.task-list-core/utils/useTimeLimitedTask';
import UniteFissionExtraModule from './UniteFissionInfo.vue';
import { vGuide } from '@pet/25cny.guide-directive/index';

import CDNImg from '@pet/25cny.cdn-image/index.vue';

import TaskButton from './TaskButton.vue';
import { DEFAULT_ICON } from '@pet/work.task-list-core/utils/const';
import type {
    RelationChainInfo,
    TaskActionEventInfoSet,
    TaskInfo,
} from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import { TaskActionType, TaskTimeLimitedStatus } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import type { GuideDirectiveOptions } from '@pet/25cny.guide-directive/type';

export interface TaskItemProps {
    readonly task: TaskInfo;
    readonly isLoading: boolean;
    readonly disabled?: boolean | undefined;
    readonly index: number;
    readonly timeLimitedInfo?: TaskTimeLimitedInfo | undefined;
    readonly defaultIcon?: string;
    readonly hideButtonLoading?: boolean;
    readonly guideTaskId?: number;
    readonly guideOptions?: GuideDirectiveOptions;
    readonly size?: CardSize;
    readonly relationChainInfoList?: RelationChainInfo[] | undefined;
    readonly isConnectionTask?: boolean;
    readonly uiPassThroughConfig?: TaskListOptions['uiPassThroughConfig'];
}
export interface TaskItemEmits {
    (e: 'doTask', eventInfo?: TaskActionEventInfoSet): void;
    (e: 'takeTask'): void;
}

export interface TaskItemSlots {
    timeLimitedTaskItem?: (_: {
        readonly timeStates: TimeStates;
        readonly task: TaskInfo;
        readonly isLoading: boolean;
    }) => VNode[];
    figure?: (_: {
        readonly timeStates: TimeStates;
        readonly task: TaskInfo;
        readonly isLoading: boolean;
        readonly disabled: boolean;
    }) => VNode[];
    title?: (_: {
        readonly timeStates: TimeStates;
        readonly task: TaskInfo;
        readonly isLoading: boolean;
        readonly disabled: boolean;
    }) => VNode[];
    desc?: (_: {
        readonly timeStates: TimeStates;
        readonly task: TaskInfo;
        readonly isLoading: boolean;
        readonly disabled: boolean;
    }) => VNode[];
    ctrl?: (_: {
        readonly timeStates: TimeStates;
        readonly task: TaskInfo;
        readonly isLoading: boolean;
        readonly disabled: boolean;
        readonly doTask: (eventInfo?: TaskActionEventInfoSet | undefined) => void;
    }) => VNode[];
    extraModule?: (_: {
        readonly timeStates: TimeStates;
        readonly task: TaskInfo;
        readonly isLoading: boolean;
        readonly disabled: boolean;
        readonly doTask: (eventInfo?: TaskActionEventInfoSet | undefined) => void;
    }) => VNode[];
}

export interface Countdown {
    readonly day: ComputedRef<number>;
    readonly hour: ComputedRef<number>;
    readonly minute: ComputedRef<number>;
    readonly second: ComputedRef<number>;
}
export interface TimeStates {
    readonly countdown: Countdown;
    readonly timeLimitedStatus: TaskTimeLimitedStatus;
}
const props = defineProps<TaskItemProps>();
const emits = defineEmits<TaskItemEmits>();
const slots = useSlots() as TaskItemSlots;

const computedDefaultIcon = computed<string>(() => props.defaultIcon ?? DEFAULT_ICON);

const iconUrl = computed<string>(() => {
    const propsIcon = props.task.icons[0];
    return propsIcon ? propsIcon : computedDefaultIcon.value;
});

const computedDisabled = computed(() => {
    if (typeof props.disabled !== 'undefined') {
        return props.disabled;
    }
    return isTaskDisabled(props.task);
});

const emitLoggerEvent = useLoggerEmits().emitLoggerEvent;

const handleClick = (eventInfo?: TaskActionEventInfoSet) => {
    const isConnectionAvatarClick = eventInfo?.type === TaskActionType.Share && eventInfo?.trigger === 'avatar';
    if (isConnectionAvatarClick) {
        emitLoggerEvent(
            {
                logType: 'connectionAvatarClick',
                logParam: {
                    uid: eventInfo.uid,
                },
            },
            props.task,
        );
    } else if (!isConnectionAvatarClick) {
        emitLoggerEvent(
            {
                logType: 'buttonClick',
                logParam: {
                    task_name: props.task.title,
                    task_id: props.task.id,
                    button_name: props.task.btnText,
                    task_status: props.task.status,
                    index: props.index,
                },
            },
            props.task,
        );
    }
    emits('doTask', eventInfo);
};

const computedRelationChainInfo = computed(() => {
    if (props.relationChainInfoList) {
        return props.relationChainInfoList;
    }
    return props.task.relationChainInfo;
});

const isTaskForConnection = computed(() => {
    // 展示条件为有相关字段并且有未助力的用户
    return computedRelationChainInfo.value && computedRelationChainInfo.value.length > 0;
});

const looksLikeDisabled = computed(() => isTaskLooksLikeDisabled(props.task));

const title = computed(() => (Boolean(isTaskForConnection.value ?? slots.title) ? '' : props.task.title));
const description = computed(() => (Boolean(isTaskForConnection.value ?? slots.desc) ? '' : props.task.description));

const { countDownInfo, timeLimitedStatus, isTimeLimitedTask } = useTimeLimitedTaskCountdown(
    toRef(props, 'timeLimitedInfo'),
);

const taskItemRoot = ref<HTMLDivElement | null>(null);
const targetIsVisible = ref(false);

const { stop } = useIntersectionObserver(taskItemRoot, (arr) => {
    if (arr?.[0]?.isIntersecting) {
        targetIsVisible.value = true;
    }
});

watch(
    () => targetIsVisible.value,
    (visible) => {
        if (visible) {
            emitLoggerEvent(
                {
                    logType: 'cardShow',
                    logParam: {
                        task_name: props.task.title,
                        task_id: props.task.id,
                        button_name: props.task.btnText,
                        task_status: props.task.status,
                        index: props.index,
                    },
                },
                props.task,
            );
        }
    },
);

function getCardProps(computedProps: CardProps) {
    const baseProps = props.uiPassThroughConfig?.taskCard?.base ?? {};
    const overrideProps = props.uiPassThroughConfig?.taskCard?.override ?? {};
    return {
        ...baseProps,
        ...computedProps,
        ...overrideProps,
    };
}

const guideArguments = computed(() => {
    return props.guideTaskId === props.task.id ? props.guideOptions! : undefined!;
});

const uiPassThroughConfigRelationChainTask = computed(() => props.uiPassThroughConfig?.relationChainTask);
const uiPassThroughConfigButton = computed(() => props.uiPassThroughConfig?.button);

/**
 * 下面是先写在这里面的会场特殊逻辑
 */
const isCoinRewd = computed(() => {
    return props.task.extParams.llrewdType === 'LLCN';
});

const uniteFissionTaskRwdList = computed(() => {
    try {
        return JSON.parse(props.task?.extParams?.rwdList ?? '[]');
    } catch (e) {
        return [];
    }
});
</script>
<template>
    <div ref="taskItemRoot" :id="`task-${task.id}`">
        <slot
            v-if="isTimeLimitedTask && slots.timeLimitedTaskItem"
            name="timeLimitedTaskItem"
            :time-states="{
                countdown: countDownInfo,
                timeLimitedStatus: timeLimitedStatus,
            }"
            :task="task"
            :is-loading="isLoading"
        >
        </slot>
        <!-- 邀人样式 -->
        <TaskConnectionCard
            v-else-if="props.isConnectionTask && task.taskProgress.needCompleteAmount > 1"
            :size="size"
            :avatar="iconUrl"
            :task-info="task"
            :user-list="task.assistUserInfo"
            :ui-pass-through-config="uiPassThroughConfigRelationChainTask"
            :default-icon="computedDefaultIcon"
            @clickAvatar="
                handleClick({
                    type: TaskActionType.Share,
                    trigger: 'avatar',
                    uid: $event,
                })
            "
            :need-complete-amount="task.taskProgress.needCompleteAmount"
        >
            <template #ctrl>
                <TaskButton
                    v-guide.target="guideArguments"
                    :task="task"
                    :is-loading="isLoading && !hideButtonLoading"
                    :disabled="computedDisabled"
                    :ui-pass-through-config="uiPassThroughConfigButton"
                    :looks-like-disabled="looksLikeDisabled"
                    @click="handleClick"
                ></TaskButton>
            </template>
        </TaskConnectionCard>
        <Card
            v-else
            v-bind="
                getCardProps({
                    avatar: iconUrl,
                    title: task.title,
                    desc: task.description,
                    size,
                    fallbackAvatar: computedDefaultIcon,
                })
            "
            :cdn-level="'P1'"
            class="task-list-item"
            :style="{
                backgroundColor: task.UIConfig.backgroundColor,
            }"
        >
            <template v-if="slots.figure" #figure>
                {{ task.UIConfig.backgroundColor }}
                <slot
                    name="figure"
                    :task="task"
                    :is-loading="isLoading"
                    :disabled="computedDisabled"
                    :time-states="{
                        countdown: countDownInfo,
                        timeLimitedStatus: timeLimitedStatus,
                    }"
                ></slot>
            </template>
            <template v-if="slots.title" #title>
                <slot
                    name="title"
                    :task="task"
                    :is-loading="isLoading"
                    :disabled="computedDisabled"
                    :time-states="{
                        countdown: countDownInfo,
                        timeLimitedStatus: timeLimitedStatus,
                    }"
                ></slot>
            </template>
            <template v-else #title>
                <div class="title">
                    <div class="title-text text-overflow">{{ task.title }}</div>
                    <div v-if="task.UIConfig.titleCornerUrl && task.UIConfig.titleCornerText" class="tag">
                        <img v-if="task.UIConfig.titleCornerUrl" :src="task.UIConfig.titleCornerUrl" class="tag-icon" />
                        <span
                            v-if="task.UIConfig.titleCornerText"
                            class="tag-num u-fw-500"
                            :class="{ 'tag-coin-num': isCoinRewd }"
                            >{{ task.UIConfig.titleCornerText }}</span
                        >
                    </div>
                </div>
            </template>

            <template v-if="slots.desc" #desc>
                <slot
                    name="desc"
                    :task="task"
                    :is-loading="isLoading"
                    :disabled="computedDisabled"
                    :time-states="{
                        countdown: countDownInfo,
                        timeLimitedStatus: timeLimitedStatus,
                    }"
                ></slot>
            </template>

            <template #ctrl>
                <slot
                    name="ctrl"
                    :task="task"
                    :is-loading="isLoading"
                    :disabled="computedDisabled"
                    :time-states="{
                        countdown: countDownInfo,
                        timeLimitedStatus: timeLimitedStatus,
                    }"
                    :do-task="handleClick"
                >
                    <TaskButton
                        v-guide.target="guideArguments"
                        :task="task"
                        :is-loading="isLoading && !hideButtonLoading"
                        :disabled="computedDisabled"
                        :ui-pass-through-config="uiPassThroughConfigButton"
                        :looks-like-disabled="looksLikeDisabled"
                        @click="handleClick"
                    ></TaskButton>
                </slot>
            </template>
        </Card>
        <!--
            25 cny 任务下挂模块：中台 banner 配置、三合一裂变任务
        -->
        <div
            v-if="task.extParams?.taskType === 'UNITE_FISSION_TASK' || task.UIConfig.bannerUrl"
            class="task-extra-module"
            @click="() => handleClick()"
        >
            <CDNImg
                v-if="task.UIConfig.bannerUrl"
                :src="task.UIConfig.bannerUrl"
                cdn-level="P1"
                downgrade-type="hidden"
                class="extra-module-item banner-img"
            />
            <UniteFissionExtraModule
                v-else-if="task.extParams?.taskType === 'UNITE_FISSION_TASK'"
                class="extra-module-item"
                :rwd-list="uniteFissionTaskRwdList"
            />
        </div>
    </div>
</template>

<style>
:root {
    --task-list-card-background: transparent;
}
</style>
<style lang="scss" scoped>
.task-list-item {
    --adapt-card-title-color: rgba(85, 0, 0, 1);
    --adapt-card-desc-color: rgba(85, 0, 0, 0.4);
    --adapt-card-main-ctrl-gap: 22px;
    --adapt-card-figure-main-gap: 10px;
    --adapt-card-figure-size: 44px;
    --adapt-card-padding: 15px 12px;
    --adapt-card-radius: 20px;
    --adapt-card-background: var(--task-list-card-background);
    height: 76px;
    box-sizing: border-box;
}
.task-extra-module {
    display: flex;
    margin-right: 12px;
    margin-top: -5px;
    margin-bottom: 15px;
    img {
        width: 312px;
        height: 87px;
    }
    .extra-module-item {
        margin-left: auto;
    }
}
.banner-img {
    border-radius: 16px;
}
.title {
    display: flex;
    line-height: 24px;
    align-items: center;
    &-text {
        color: rgba(85, 0, 0, 1);
    }
    .text-overflow {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .tag {
        background-color: rgba(255, 207, 196, 0.25);
        border-radius: 34px;
        display: flex;
        align-items: center;
        padding: 0 6px 0 1px;
        height: 20px;
        margin-left: 4px;
        &-icon {
            height: 18px;
            width: 18px;
        }
        &-num {
            font-family: 'MiSans';
            font-size: 13px;
            color: #ff2020;
            margin-left: 3px;
        }
        &-coin-num {
            color: rgba(255, 153, 0, 1);
        }
    }
}
</style>
