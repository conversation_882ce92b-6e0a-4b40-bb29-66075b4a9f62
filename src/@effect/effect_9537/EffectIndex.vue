<script setup lang="ts">
/**
 * pet组件使用示例。带动效平台导出尺寸，可直接使用
 * 动效预览：https://vision.corp.kuaishou.com/effect/9537
 *
 * 依赖pet组件 effect-item@^0.4.11
 * 更多组件使用方式、功能见：
 * https://pet-web.corp.kuaishou.com/gallery/detail/?name=@pet/vision.effect-item&version=
 */
import { preload } from '@vision/runtime';
import EffectItem from '@pet/vision.effect-item/index.vue';
import { GoalStatusEnum } from '@/components/forward-rush/config';

const props = defineProps<{
    goalUrl: string;
}>();
const emit = defineEmits<{
    (e: 'statusEnded', status: GoalStatusEnum): void;
}>();

const effectRef = ref();
const effectData = import('./config').then((mod) => mod.default);
const status = defineModel<GoalStatusEnum>('status', {
    required: true,
});

const isLoop = computed(() => status.value === GoalStatusEnum.Loop);
watch([status, () => effectRef.value], ([state, effect]) => {
    if (effect) {
        effect.playAction(state, isLoop.value);
    }
});

const onStatusEnded = () => {
    emit('statusEnded', status.value);
    if (status.value === GoalStatusEnum.Appear) {
        status.value = GoalStatusEnum.Loop;
    }
};
</script>

<template>
    <EffectItem
        ref="effectRef"
        class="effect-wrapper"
        :replace-data="{
            goal: props.goalUrl,
        }"
        :data="effectData"
        autoplay
        @ended="onStatusEnded"
    ></EffectItem>
</template>

<style scoped>
.effect-wrapper {
    position: absolute;
    width: 125px;
    height: 130px;
    overflow: hidden;
    right: -26px;
    top: -78px;
}
</style>
