/**
 * apng resource data
 * generate@ 1 for @vision/runtime@^0.7.0
 * preview@ https://vision.corp.kuaishou.com/effect/8558
 *
 * wrapper size expected: { width: 150px; height: 150px; }
 */
import '@vision/runtime/adapter/apng';

import entrySrcAvif from './assets/effectSource-effect.avif';
import entrySrc from './assets/effectSource-effect.png';
import entrySrcWebp from './assets/effectSource-effect.webp';


export default Object.seal({
    type: 'apng' as const,

    meta: {
        version: 1,
        id: '8558',
        effectVersion: 0,
    },

    layout: {
        adaptive: 414,
    },

    options: {
        /**
         * 默认循环播放
         */
        loop: false,
        /**
         * 默认自动播放
         */
        autoplay: true,
        ratio: 2,
    },


    entrySrc,

    extraData: {
        /**
         * 是否使用 apng-player 播放。
         * 如果没有特别需要，默认使用 img 播放
         */
        enableHandler: false,

        /**
         * 是否支持转换成webp/avif格式播放。
         */
        convertible: true,

        /**
         * 是否支持转换成webp/avif格式播放。
         */
        entrySrcAvif,
        entrySrcWebp,



    },
});
