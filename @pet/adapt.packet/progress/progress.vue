<script setup lang="ts">
import { px2rem } from '@pet/core.mobile';
import { computed } from 'vue';

interface prop {
    info: {
        startText?: string;
        endText?: string;
        percent: number;
    };
}

const props = defineProps<prop>();

const lineWidth = computed(() => {
    let resultPercent = props.info.percent;
    props.info.percent < 0.4 && (resultPercent = 0.4);
    props.info.percent > 0.9 && (resultPercent = 0.9);
    return px2rem(resultPercent * 210);
});
</script>

<template>
    <div class="progress">
        <div class="text">
            <span>{{ info.startText ?? '' }}</span>
            <span>{{ info.endText ?? '' }}</span>
        </div>
        <div class="line-bg">
            <div class="line-cover" :style="{ width: lineWidth }"></div>
            <div class="point" :style="{ left: lineWidth }"></div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import '../common.scss';

.progress {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: center;

    width: 264px;
    height: 30px;
    padding: 0 27px;
    margin-top: -2px;
    margin-bottom: 20px;

    .text {
        display: flex;
        position: absolute;
        font-family: 'PingFang SC', sans-serif;
        justify-content: space-between;
        width: 210px;
        top: -2px;
        padding: 0 27px;
        font-size: 12px;
        color: #9c9c9c;
    }

    .line-bg {
        position: relative;
        width: 212px;
        height: 10px;
        background-color: #ffdfdf;
        border-radius: 8px;
        margin-top: -1px;
    }

    .line-cover {
        height: 100%;
        background: linear-gradient(269.69deg, #ff0b24 0.55%, #ff9838 99.61%);
        border-radius: 8px;
    }

    .point {
        position: absolute;
        left: 0;
        top: 0;
        width: 18px;
        height: 18px;
        transform: translate(-50%, -25%);
        background: url(./assets/point.png) no-repeat center/100%;
    }
}
</style>
