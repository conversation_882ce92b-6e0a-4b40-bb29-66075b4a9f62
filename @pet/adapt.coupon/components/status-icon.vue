<script setup lang="ts">
interface ButtonProps {
    text?: string;
}
const props = withDefaults(defineProps<ButtonProps>(), {
    text: '已过期',
});
</script>

<template>
    <svg
        class="status-icon"
        width="52"
        height="65"
        viewBox="0 0 52 65"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
    >
        <g fill="none" fill-rule="evenodd" opacity=".6" transform="rotate(-30 33.45 28.963)">
            <circle stroke="#CECCCA" stroke-width=".75" cx="30" cy="30" r="30" />
            <circle stroke="#CECCCA" stroke-width=".75" cx="30" cy="30" r="27" />
            <path
                d="M42.09 39.5A15.348 15.348 0 0 1 30 45.376a15.348 15.348 0 0 1-12.09-5.874h.97A14.592 14.592 0 0 0 30 44.625c4.45 0 8.437-1.988 11.12-5.124ZM30 14.626c4.906 0 9.275 2.298 12.09 5.875h-.97A14.592 14.592 0 0 0 30 15.375 14.592 14.592 0 0 0 18.88 20.5h-.97A15.348 15.348 0 0 1 30 14.625Z"
                fill="#CECCCA"
                fill-rule="nonzero"
            />
            <text font-family="PingFangSC-Medium, PingFang SC" font-size="13" font-weight="400">
                <tspan x="10.5" y="35" fill="#CECCCA">{{ text }}</tspan>
            </text>
            <g transform="translate(14.5 8)" fill="#CECCCA">
                <circle cx="15.75" cy=".75" r="1" />
                <circle cx="24.25" cy="2.75" r="1" />
                <circle cx="7.25" cy="2.75" r="1" />
                <circle cx="30.75" cy="7.25" r="1" />
                <circle cx=".75" cy="7.25" r="1" />
            </g>
            <g transform="matrix(1 0 0 -1 14.5 52)" fill="#CECCCA">
                <circle cx="15.75" cy=".75" r="1" />
                <circle cx="24.25" cy="2.75" r="1" />
                <circle cx="7.25" cy="2.75" r="1" />
                <circle cx="30.75" cy="7.25" r="1" />
                <circle cx=".75" cy="7.25" r="1" />
            </g>
        </g>
    </svg>
</template>

<style lang="scss" scoped>
.status-icon {
    width: 52px;
    height: 75px;
}
</style>
