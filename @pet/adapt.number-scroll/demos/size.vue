<pet-info lang="json">
{
    "title": "尺寸控制",
    "description": "调整大小尺寸"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const numberValueData = ref(100);

function numberChange() {
    const num = 100;
    numberValueData.value += num * Math.random();
}
</script>

<template>
    <div class="demo">
        <h5>原始数值{{ numberValueData }}</h5>
        <div class="container">
            <h6>
                :font-size="24"<br />
                :line-height="36"<br />
                unit="rem"
            </h6>
            单位rem:
            <NumberScroll :number-value="numberValueData" :font-size="24" :line-height="36" />
            单位px:
            <NumberScroll :number-value="numberValueData" :font-size="24" :line-height="36" unit="px" />
        </div>
        <button @click="numberChange">增加数值</button>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 48px 0 24px;
    font-size: 12px;
    text-align: center;
}
h5 {
    position: absolute;
}
</style>
