import { createInjectionState } from '@vueuse/core';
import { ref } from 'vue-demi';
import { maxViewWidth, getMaxViewHeight, getDeviceHeight } from './index';
import { useBarInfo } from '@pet/yau.core/device/appInfo';
import { useDomListener } from '@pet/yau.core/event/useDomListener';
export const isSmallScreenRatio = 736 / 414;

export function simpleDecimalsCompare(a: number, b: number) {
    return +a.toFixed(2) * 100 <= +b.toFixed(2) * 100;
}

export function screenDetect() {
    const viewWidth = maxViewWidth;
    const viewHeight = getMaxViewHeight();
    const deviceHeight = getDeviceHeight();
    const statusBarHeight = useBarInfo().statusBarHeight;
    const availableViewHeight = viewHeight;
    const availableScreenHeight = deviceHeight - statusBarHeight;
    const viewRatio = viewHeight / viewWidth;
    const availableViewRatio = availableViewHeight / viewWidth;
    const isSmallScreenDevice = simpleDecimalsCompare(availableViewRatio, isSmallScreenRatio);
    return {
        viewWidth,
        viewHeight,
        availableViewHeight,
        viewRatio,
        availableViewRatio,
        /**
         * 小屏幕
         */
        isSmallScreenDevice,
        /**
         * 公测临时用
         */
        availableScreenHeight,
    };
}

const [provide, inject] = createInjectionState(() => {
    const viewWidthRef = ref(0);
    const viewHeightRef = ref(0);
    const availableViewHeightRef = ref(0);
    const viewRatioRef = ref(0);
    const availableViewRatioRef = ref(0);
    const isSmallScreenDeviceRef = ref(false);
    const availableScreenHeightRef = ref(0);

    function setinfoValues() {
        const {
            viewWidth,
            viewHeight,
            availableViewHeight,
            viewRatio,
            availableViewRatio,
            isSmallScreenDevice,
            availableScreenHeight,
        } = screenDetect();
        viewWidthRef.value = viewWidth;
        viewHeightRef.value = viewHeight;
        availableViewHeightRef.value = availableViewHeight;
        viewRatioRef.value = viewRatio;
        availableViewRatioRef.value = availableViewRatio;
        isSmallScreenDeviceRef.value = isSmallScreenDevice;
        availableScreenHeightRef.value = availableScreenHeight;
    }

    setinfoValues();

    useDomListener(() => window, 'resize', setinfoValues);

    return {
        viewWidth: viewWidthRef,
        viewHeight: viewHeightRef,
        availableViewHeight: availableViewHeightRef,
        viewRatio: viewRatioRef,
        availableViewRatio: availableViewRatioRef,
        isSmallScreenDevice: isSmallScreenDeviceRef,
        availableScreenHeight: availableScreenHeightRef,
    };
});

export { provide as provideViewInfo, inject as injectViewInfo };
