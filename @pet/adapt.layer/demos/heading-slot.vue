<pet-info lang="json">
{ "title": "标题插槽", "description": "" }
</pet-info>
<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import { ref } from 'vue-demi';

import Layer from '../index.vue';

const show = ref(false);
</script>

<template>
    <div>
        <Layer
            v-model:show="show"
            custom-logo="https://w1-pro.kskwai.com/kos/nlav11066/2022_common_static/business/modal_logo_shanzhashuxia_v1.png"
            sub-title="此处为副标题，可用于辅助信息"
            cover="https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2021-12-16/1639634522781.3e4249c181e76b81.png"
        >
            <template #title> 此处为主标题<span class="colorful">主标题</span> </template>
        </Layer>
        <Button class="item-btn" @click="show = true">打开蒙层</Button>
    </div>
</template>

<style scoped>
.item-btn {
    margin: 0 2px;
}

.colorful {
    color: #ffe601;
}
</style>
