import { createModel, useModel } from '@gundam/model';
import { sleep } from '@pet/yau.core';
import { until } from '@vueuse/core';

import { useLogger } from '@/init/logger';

import { useConfigModel } from './configModel';
import { QUEUE_TAGS_TYPE, TaskType, usePopupModel } from './popup.model';
import { useRefreshModel } from './refreshModel';

// banner 展示逻辑
// 存量用户弹窗>新手引导弹窗>最终大奖/奖励过期/续签/彻底断签>BigDay横幅(暂定)>组队弹窗>任务完成
export const BigDayModel = createModel(() => {
    const { openPopup } = usePopupModel();
    const { bigDayBannerConfig } = useConfigModel();
    const { sendShow } = useLogger();
    const { isShowCloud } = useRefreshModel();
    const resolver = ref<((value: unknown) => void) | null>(null);
    watch(isShowCloud, (val) => {
        if (!val) {
            resolver?.value?.(true);
        }
    });
    const openBigDayBannerPopup = async () => {
        console.log('[openBigDayBannerPopup]');
        await sleep(100);
        if (isShowCloud.value) {
            await new Promise((resolve) => {
                resolver.value = resolve;
            });
            await sleep(800);
        }
        const task = openPopup({
            component: () => import('@/components/popups/big-day/BannerPopup.vue'),
            data: {
                rightText: bigDayBannerConfig.value.text,
                textImage: bigDayBannerConfig.value.textImage,
                onShowLog: () => {
                    sendShow('OP_ACTIVITY_BIGDAY_GUID', {
                        title: bigDayBannerConfig.value.text ?? bigDayBannerConfig.value.textImage ?? '',
                    });
                },
            },
            options: {
                name: TaskType.PASSIVE_POPUP,
                queueTags: [QUEUE_TAGS_TYPE.POPUP],
            },
        });

        return task;
    };
    return {
        openBigDayBannerPopup,
    };
});

export const useBigDayModel = () => useModel(BigDayModel);
