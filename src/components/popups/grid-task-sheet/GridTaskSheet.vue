<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import EmptyPage from '@pet/adapt.empty-status/index.vue';

import CommonSheet from '@/components/common-sheet/CommonSheet.vue';
import { vShowLog, useLogger } from '@/init/logger';
import { useGridTaskSheetModel } from '@/models/gridTaskSheetModel';
import type { CUSTOM_POPUP_TYPE } from '@/models/popup.model';
import { POPUP_ACTION } from '@/utils/log/type';

const props = defineProps<{
    popupType: CUSTOM_POPUP_TYPE.GRID_TASK_SHEET;
}>();
const emit = defineEmits<{
    (e: 'end'): void;
}>();
const close = () => {
    emit('end');
};
const { sendClick } = useLogger();

const { popupType } = toRefs(props);
const { currentGridTask, handleGridTaskBtnClick } = useGridTaskSheetModel();

const title = ref('向前冲挑战');
const subTitle = ref('完成后可以继续向前冲哦');

const mainBtnText = computed(() => currentGridTask.value?.displayText ?? '去完成');

const logParams = computed(() => {
    if (!currentGridTask.value) {
        return {};
    }

    const extParams = currentGridTask.value.extParams;
    return {
        task_type: extParams?.groupName ?? '',
        task_id: String(currentGridTask.value.taskId ?? extParams?.taskId ?? ''),
        task_name: currentGridTask.value.title ?? '',
        brand_name: '',
        title: title.value ?? '',
        popup_type: popupType.value ?? '',
        button_name: mainBtnText.value,
    };
});

// 弹窗关闭按钮
const handleClose = () => {
    sendClick(POPUP_ACTION.TASK_SHOW, {
        ...logParams.value,
        button_name: 'close',
    });
    close();
};

const onMainButtonClick = () => {
    sendClick(POPUP_ACTION.TASK_SHOW, {
        ...logParams.value,
        button_name: mainBtnText.value,
    });

    handleGridTaskBtnClick(close);
};
</script>
<template>
    <CommonSheet
        v-show-log="{
            action: POPUP_ACTION.TASK_SHOW,
            params: logParams,
        }"
        :class="{
            grid_sheet: true,
            error: !currentGridTask,
        }"
        :title="title"
        :sub-title="subTitle"
        inner-scroll
        @close="handleClose"
        @after-leave="$emit('end')"
    >
        <div v-if="currentGridTask" class="content">
            <div class="icon_wrap">
                <div class="icon"></div>
                <div class="tag"></div>
            </div>
            <div class="title">{{ currentGridTask.title }}</div>
            <div class="sub_title">{{ currentGridTask.description }}</div>
            <Button class="main_btn" :height="72" @click="onMainButtonClick">{{ mainBtnText }}</Button>
        </div>
        <EmptyPage v-else icon="network">网络似乎断开了，刷新页面试试看</EmptyPage>
    </CommonSheet>
</template>
<style lang="scss" scoped>
.grid_sheet {
    --adapt-sheet-max-show-height: 100%;
    --adapt-sheet-small-screen-max-show-height: 100%;
    --adapt-button-plugin-opcity: 1;

    :deep(.sheet-content) {
        min-height: 300px;
        overflow: visible;
    }

    :deep(.sheet-body) {
        overflow: visible;
    }

    :deep(.header) {
        .sheet-title {
            top: 66px;
        }

        .sheet-sub-title {
            top: 105px;
        }
    }

    .content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .icon_wrap {
        position: relative;
        margin: 0 auto;
    }

    .icon {
        margin-top: 6px;
        width: 130.5px;
        height: 110px;
        @include bg('@/components/popups/grid-task-sheet/assets/sheet_grid_icon.png');
    }

    .tag {
        width: 90.5px;
        height: 69px;
        @include bg('@/components/popups/grid-task-sheet/assets/sheet_grid_running.png');
        position: absolute;
        top: -31px;
        right: -46px;
    }

    .icon,
    .tag {
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    .title {
        margin-top: 24px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 0;
        text-align: center;

        color: #670410;
    }

    .sub_title {
        margin-top: 8px;

        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        line-height: 18px;
        letter-spacing: 0;

        color: #c98888;
    }

    .main_btn {
        margin-top: 32px;
        margin-bottom: 44px;
        --adapt-button-width: 260px;

        :deep(.btn-primary) {
            background: linear-gradient(99.59deg, #ff7001 -1.65%, #fc2d39 47.88%, #f31906 98.68%);
        }

        :deep(.btn-words) {
            font-family: KuaiYuanHuiTi;
            font-weight: 400;
            font-size: 24px;
            line-height: 22px;
            letter-spacing: 0;
        }
    }

    &.error {
        :deep(.sheet-content) {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        :deep(.status-wrap) {
            transform: translateY(-30%);
        }
    }
}

.loading,
.error {
    margin-top: 100px;
    display: flex;
    justify-content: center;
    overflow: hidden;
}
</style>
