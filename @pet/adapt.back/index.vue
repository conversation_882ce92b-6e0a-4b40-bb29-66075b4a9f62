<script lang="ts">
export default {
    name: 'AdaptBack',
};
</script>

<script lang="ts" setup>
import { useRouterBack } from '@pet/yau.yoda/route/useRouterBack';

const props = defineProps<{
    customBack?: () => void;
    beforeBack?: (() => void) | (() => Promise<void>);
}>();

const routerBack = useRouterBack();

async function back() {
    if (props.customBack) {
        props.customBack();
        return;
    }
    if (props.beforeBack) {
        await props.beforeBack();
    }
    routerBack();
}
</script>
<template>
    <div class="nav-back" @click="back">
        <slot></slot>
    </div>
</template>
