<pet-info lang="json">
{ "title": "全部规格", "description": "所有checkbox样式", "priority": 88 }
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';

import CheckBox from '../index.vue';
const input1 = ref(true);
const input2 = ref(false);
</script>

<template>
    <div class="demo">
        <div>
            <h5>未选中</h5>
            <CheckBox v-model="input2" class="item" type="line" />
            <CheckBox v-model="input2" class="item" type="line" disabled />
        </div>

        <div>
            <h5>实色</h5>
            <CheckBox v-model="input1" class="item" type="flat" />
            <CheckBox v-model="input1" class="item" type="flat" disabled />
        </div>

        <h5>线框</h5>
        <CheckBox v-model="input1" class="item" type="line" />
        <CheckBox v-model="input1" class="item" type="line" disabled />
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.demo {
    padding: 10px;
}

.item {
    + .item {
        margin-left: 10px;
    }
}
</style>
