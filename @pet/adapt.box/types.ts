import type { InjectionKey, ComputedRef } from 'vue-demi';
export type DirectionType = 'row' | 'col' | 'row-rev' | 'col-rev';
export type AlignType = 'start' | 'end' | 'center' | 'baseline' | 'stretch';
export type JustifyType = 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';

export interface IGapInfo {
    direction: DirectionType;
    classic: boolean;
    row: number;
    col: number;
}

export const BoxInfoProvider: string | InjectionKey<ComputedRef<IGapInfo>> = 'box-gaps-provider';
