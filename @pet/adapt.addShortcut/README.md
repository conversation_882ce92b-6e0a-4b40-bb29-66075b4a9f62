## 函数

-   addShortCutInDesktopBridge 添加桌面快捷方式

*   参数 object
    -   {
        -   url: string; //必填 端内 h5 活动页 URL
        -   icon: string; //必填 活动 icon
        -   name: string; //必填 活动标题
        -   id?: string; //可选 自定义 id, 通过此字段可以避免重复创建快捷方式， 以及后续考虑通过 id 统计活动参与度
        -   hidePopup: boolean; //可选 代表是否隐藏默认弹窗 默认 false
        -   styleConfig: {
            -   bgImg: string; // 必填 IOS 中间页背景图
            -   bgColor: string; // 必填 IOS 中间色背景色
            -   btText?: string; // 非必填 按钮文案 默认‘点击进入活动页’
        -   }
    -   }
*   使用方式 - addShortCut({ url: '', icon: '', name:'', id: '', hidePopup: false, styleConfig: {bgImg: '',bgColor: '', btText:''} }).then t ((res) => { - console.log('then',res) - }).catch((ree) => { - console.log('err',ree.code) - });

*   返回参数

-   {
    -   status: string; // 成功：success 失败：fail 未知：unknown
    -   url: string //教程文档地址 只有安卓会返回 其他情况默认为 null
-   }
