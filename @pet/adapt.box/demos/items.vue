<pet-info lang="json">
{ "title": "子容器布局", "description": "" }
</pet-info>
<script setup lang="ts">
import Box from '../box.vue';
import BoxItem from '../box-item.vue';
</script>

<template>
    <div class="wrapper">
        <h4>不换行</h4>
        <div class="box-wrapper">
            <Box class="box" :gap-col="20">
                <BoxItem v-for="n in 3" :key="n">
                    <div class="box-item"></div>
                </BoxItem>
            </Box>
        </div>

        <h4>横向换行</h4>
        <div class="box-wrapper">
            <Box class="box" :gap-row="8" :gap-col="8" wrap :items="3">
                <BoxItem v-for="n in 6" :key="n">
                    <div class="box-item">{{ n }}</div>
                </BoxItem>
            </Box>
        </div>

        <h4>纵向换行</h4>
        <div class="box-wrapper">
            <Box class="box col-box" :gap-row="8" :gap-col="8" wrap :items="3" direction="col">
                <BoxItem v-for="n in 6" :key="n">
                    <div class="box-item-col">{{ n }}</div>
                </BoxItem>
            </Box>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.wrapper {
    width: 360px;
}
.box-wrapper {
    overflow: hidden;
}
.box {
    background: #f5f5f5;
    position: relative;
}
.box-item {
    height: 25px;
    background-color: #ccc;
}
.col-box {
    height: 180px;
}
.box-item-col {
    height: 100%;
    background-color: #ccc;
}

div {
    font-size: 16px;
}
</style>
