<script setup lang="ts">
import { ref } from 'vue-demi';

import XImage from '../index.vue';

const imgSrc = ref('https://memeprod.sgp1.digitaloceanspaces.com/meme/314c19b6b3d8a7ee7e07265cd5d27a64.png');

const imgSrcLoad = ref('加载中');

function handleLoad() {
    imgSrcLoad.value = '加载成功';
}
</script>

<template>
    <div class="demo">
        <h5>加载状态@load: {{ imgSrcLoad }}</h5>
        <div class="container">
            <XImage :src="imgSrc" class="image-item" @load="handleLoad" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
}
.image-item {
    width: 100px;
    height: 100px;
}
div {
    font-size: 16px;
}
</style>
