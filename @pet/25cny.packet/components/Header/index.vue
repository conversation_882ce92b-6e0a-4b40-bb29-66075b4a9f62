<script setup lang="ts">
import Logo from '@pet/adapt.logo/index.vue';
import AssistInfoDesc from './AssistInfo.vue';
import Header from '@pet/adapt.heading/index.vue';
import type { TitleContext } from '../type';

defineProps<{
    logo?: string;
    brandLogoDegradeText?: string;
    title?: string;
    assistInfo?: TitleContext;
}>();
</script>

<template>
    <Logo v-if="logo" :src="logo" :brand-logo-degrade-text="brandLogoDegradeText" />
    <Header class="header" :title="title"></Header>
    <AssistInfoDesc :info="assistInfo" />
</template>

<style lang="scss" scoped>
.header {
    --adapt-heading-title-letter-spacing: 1px;
}
</style>
