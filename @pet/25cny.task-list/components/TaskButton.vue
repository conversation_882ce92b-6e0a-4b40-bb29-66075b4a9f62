<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import type { ButtonProps, TaskListOptions } from '@pet/25cny.task-list/components/types';

import type { TaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';

const props = defineProps<{
    task: TaskInfo;
    isLoading: boolean;
    disabled: boolean;
    looksLikeDisabled?: boolean;
    uiPassThroughConfig?: Exclude<TaskListOptions['uiPassThroughConfig'], undefined>['button'];
}>();

const emits = defineEmits<{
    (e: 'click'): void;
}>();

function getButtonProps(computedProps: ButtonProps) {
    const baseProps = props.uiPassThroughConfig?.base ?? {};
    const overrideProps = props.uiPassThroughConfig?.override ?? {};
    return {
        ...baseProps,
        ...computedProps,
        ...overrideProps,
    };
}
</script>

<template>
    <div class="task-button">
        <Button
            v-bind="
                getButtonProps({
                    height: 38,
                    type: 'primary',
                    loading: isLoading,
                    disabled: disabled && !isLoading && !looksLikeDisabled,
                    looksLikeDisabled: looksLikeDisabled,
                })
            "
            class="task-button-item"
            @click="emits('click')"
        >
            <template v-if="!isLoading">
                {{ task.btnText }}
            </template>
        </Button>
    </div>
</template>

<style lang="scss" scoped>
.task-button {
    position: relative;
    --adapt-button-primary-background-color: rgba(255, 55, 33, 1);
}
.task-button-item {
    width: 76px !important;
    --adapt-button-font-size: 14px;
}
</style>
