<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import PictureImg from '@pet/quantum.PictureImg';

import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';

import haloArrowSkin from '../assets/bg/haloArrow.png?preset=modern';
import HaloArrowEffect from '../assets/effects/haloArrow/EffectIndex.vue';

const { effectShowStatus } = useDowngradeLevel();
const { autoScroll } = useForwardRushGridModel();

const showEffect = computed(() => effectShowStatus.value.L1);
// console.log('effectShowStatus', effectShowStatus.value.L1);
</script>

<template>
    <div :class="['halo-arrow-beginner-guide', autoScroll ? 'app-frame-full-height' : '']">
        <div class="image">
            <HaloArrowEffect v-if="showEffect" />
            <PictureImg v-else class="PictureImg" :src="haloArrowSkin" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.halo-arrow-beginner-guide {
    position: absolute;
    z-index: 49;
    top: 0;
    width: 414px;
    height: 100%;
    // background-color: #000d;

    .image {
        position: absolute;
        // top: var(--status-bar-height);
        // bottom: 40px;
        width: 100%;
        height: 100%;

        display: flex;
        justify-content: center;
        align-items: center;

        box-sizing: border-box;

        .PictureImg {
            position: relative;
            width: 348px;
            height: 141px;
            top: 2.5px;
            left: 1px;

            :deep(img) {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }
}

.app-frame-full-height {
    height: 736px !important;
}
</style>
