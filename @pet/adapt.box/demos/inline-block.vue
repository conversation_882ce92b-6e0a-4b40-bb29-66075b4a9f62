<pet-info lang="json">
{ "title": "inline-block", "description": "" }
</pet-info>
<script setup lang="ts">
import Box from '../box.vue';
import BoxItem from '../box-item.vue';
</script>

<template>
    <div class="wrapper">
        <Box :gap-row="20" :gap-col="20" classic :items="3">
            <BoxItem v-for="n in 3" :key="n">
                <div class="box-item">{{ n }}</div>
            </BoxItem>
        </Box>
    </div>
</template>

<style lang="scss" scoped>
.wrapper {
    width: 360px;
    background: #f5f5f5;
    position: relative;
}
.box-item {
    height: 25px;
    background-color: #ccc;
}

div {
    font-size: 16px;
}
</style>
