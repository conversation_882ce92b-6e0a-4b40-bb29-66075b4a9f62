<pet-info lang="json">
{
    "title": "头像组",
    "description": "一组头像控制间距，溢出模式"
}
</pet-info>
<script setup lang="ts">
import AvatarGroup from '../avatar-group.vue';

const ava =
    'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png';
const groupDemo1 = Array(3).fill(ava);
const groupDemo2 = Array(3).fill('empty');
const groupDemo3 = Array(4).fill(ava);
</script>

<template>
    <section class="demo-frame">
        <div>
            <h5>正常展示</h5>
            <div class="wrapper">
                <AvatarGroup :srcs="groupDemo1" />
                <AvatarGroup :srcs="groupDemo2" default-type="light" />
                <AvatarGroup :srcs="groupDemo2" default-type="dark" />
            </div>
            <h5>逆向展示</h5>
            <div class="wrapper">
                <AvatarGroup :srcs="groupDemo1" reverse />
                <AvatarGroup :srcs="groupDemo2" default-type="light" reverse />
                <AvatarGroup :srcs="groupDemo2" default-type="dark" reverse />
            </div>
            <h5>3头像溢出展示</h5>
            <div class="wrapper">
                <AvatarGroup :srcs="groupDemo3" :max="4" />
                <AvatarGroup :srcs="groupDemo3" rest-type="mask" />
                <AvatarGroup :srcs="groupDemo3" rest-type="number" />
                <AvatarGroup :srcs="groupDemo3" rest-type="icon" />
            </div>
            <h5>3头像溢出逆向展示</h5>
            <div class="wrapper">
                <AvatarGroup :srcs="groupDemo3" :max="4" reverse />
                <AvatarGroup :srcs="groupDemo3" rest-type="mask" reverse />
                <AvatarGroup :srcs="groupDemo3" rest-type="number" reverse />
                <AvatarGroup :srcs="groupDemo3" rest-type="icon" reverse />
            </div>
            <h5>边框&颜色&间距</h5>
            <div class="wrapper">
                <AvatarGroup :srcs="groupDemo3" class="head" />
                <AvatarGroup :srcs="groupDemo3" class="mask" rest-type="mask" />
                <AvatarGroup :srcs="groupDemo3" class="font" rest-type="number" />
                <AvatarGroup :srcs="groupDemo3" class="icon" rest-type="icon" />
            </div>
            <h5>slot</h5>
            <div class="wrapper">
                <AvatarGroup :srcs="groupDemo3" class="head"><span class="slot">牛</span></AvatarGroup>
                <AvatarGroup :srcs="groupDemo3" class="head" rest-type="mask"><span class="slot">牛</span></AvatarGroup>
            </div>
        </div>
    </section>
</template>

<style lang="scss" scoped>
.demo-frame {
    display: flex;
    font-size: 15px;
    h5 {
        font-size: 15px;
    }

    .wrapper {
        width: 414px;
        display: flex;
        justify-content: space-around;
        align-items: center;

        .head {
            /* 边框变量 */
            --adapt-avatar-border-width: 1px;
            --adapt-avatar-border-color: #ffde9f;
        }

        .mask {
            --adapt-avatar-mask-color: rgba(255, 192, 203, 0.6);
        }

        .font {
            --adapt-avatar-group-rest-number-color: pink;
            --adapt-avatar-group-rest-number-font-size: 12px;
        }

        .icon {
            --adapt-avatar-icon-color: blue;
        }

        .slot {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: red;
            font-weight: bold;
        }
    }
}
</style>
