import { until } from '@vueuse/core';
import { onMounted } from 'vue';

export const useWeakGuideTimeAwait = () => {
    const intervalAwaitSuccess = ref(false);
    const intervalFinish = ref(true);
    const timeoutRef = ref<number | null>(null);

    /** 取消当前等待间隔 */
    const nonIntervalCancel = () => {
        if (timeoutRef.value) {
            clearTimeout(timeoutRef.value);
            timeoutRef.value = null;
        }
        intervalAwaitSuccess.value = false;
        intervalFinish.value = true;
    };

    /** 等待指定时间间隔 */
    const nonIntervalAwait = async (timeout: number) => {
        // 取消已有定时器
        nonIntervalCancel();

        // 开始新等待
        intervalFinish.value = false;

        return new Promise<boolean>((resolve) => {
            timeoutRef.value = window.setTimeout(() => {
                intervalAwaitSuccess.value = true;
                intervalFinish.value = true;
                timeoutRef.value = null;
                resolve(true);
            }, timeout);

            // 可选：监听 until 实现（与示例一致）
            until(intervalFinish).toBeTruthy().then(() => {
                resolve(intervalAwaitSuccess.value);
            });
        });
    };

    // 组件卸载时自动清理
    onMounted(() => {
        return () => nonIntervalCancel();
    });

    return {
        nonIntervalAwait,
        nonIntervalCancel,
    };
};