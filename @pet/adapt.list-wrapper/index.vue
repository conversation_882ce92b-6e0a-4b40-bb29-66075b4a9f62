<script setup lang="ts" generic="T extends any">
import EmptyStatus from '@pet/adapt.empty-status/index.vue';
import type { IconName } from '@pet/adapt.empty-status/types';
import LoadMore from '@pet/adapt.load-more/index.vue';
import Loading from '@pet/adapt.loading/infinity-loading.vue';
import type { ButtonType } from '@pet/adapt.button/types';

import { computed } from 'vue-demi';

export interface EmptyItem {
    /**
     * 空状态图标
     */
    icon?: IconName;
    /**
     * 空状态文案
     */
    text: string;
    /**
     * 按钮类型
     */
    buttonType?: ButtonType;
    /**
     * 按钮文案
     */
    buttonText?: string;
}

export type ListWrapperEmits = {
    /**
     * 加载更多
     * @arg { number } count
     */
    (event: 'load-more', count: number): void;
    /**
     * 刷新
     */
    (event: 'refresh'): void;
};

const props = withDefaults(
    defineProps<{
        /**
         * 列表数据
         */
        data?: T[];
        /**
         * 加载中状态
         */
        loading?: boolean;
        /**
         * 是否错误
         */
        error?: boolean;
        /**
         * 显示空状态
         */
        showEmpty?: boolean;
        /**
         * 空状态内容定制
         */
        emptyConfig?: EmptyItem;
        /**
         * 懒加载的rootMargin
         */
        loaderRootMargin?: string;
        /**
         * 加载更多显示loading
         */
        loaderSpinner?: boolean;
        /**
         * 是否整屏列表
         */
        fullPage?: boolean;
    }>(),
    {
        data: () => [],
        error: false,
        loading: false,
        showEmpty: true,
        loaderRootMargin: '0px 0px 20px 0px',
        loaderSpinner: true,
        fullPage: true,
    },
);

const emit = defineEmits<ListWrapperEmits>();

const slots = defineSlots<{
    item(props: { data: T; index: number }): any;
    top(props: any): any;
    bottom(props: any): any;
    loader(props: any): any;
}>();

const EMPTY_STATUS: { [key: string]: EmptyItem } = {
    empty: {
        icon: 'empty',
        text: '暂时没有明细',
        buttonText: '',
    },
    error: {
        icon: 'network',
        text: '网络似乎断开了，请再试一试',
        buttonText: '刷新',
        buttonType: props.emptyConfig?.buttonType,
    },
};
const emptyInfo = computed(() => {
    if (props.error) {
        return EMPTY_STATUS.error;
    } else if (props.data.length === 0) {
        if (props.emptyConfig) {
            return props.emptyConfig;
        }
        return EMPTY_STATUS.empty;
    }
    return EMPTY_STATUS.empty;
});

const emptyClasses = computed(() => [props.data.length === 0 && 'list-wrapper-empty', !props.showEmpty && 'no-empty']);

function handleRefresh() {
    emit('refresh');
}

let loadCounter = 0;

function loadMore() {
    loadCounter++;
    emit('load-more', loadCounter);
}
</script>
<script lang="ts">
export default {
    name: 'AdaptListWrapper',
};
</script>
<template>
    <div class="list-wrapper" :class="emptyClasses">
        <div v-if="loading" class="list-loading stay-center">
            <Loading />
        </div>
        <div v-else-if="data.length" class="list-main">
            <slot name="top" />
            <template v-for="(item, index) in data">
                <slot name="item" :data="item" :index="index" />
            </template>
            <LoadMore class="list-loader" :root-margin="loaderRootMargin" @to-bottom="loadMore">
                <template #info>
                    <Loading v-if="data.length && loaderSpinner" />
                    <slot name="loader" />
                </template>
            </LoadMore>
            <slot name="bottom" />
        </div>
        <EmptyStatus
            v-else-if="showEmpty"
            class="stay-center"
            :icon="emptyInfo?.icon"
            :button-text="emptyInfo?.buttonText"
            :button="!!emptyInfo?.buttonText"
            :botton-type="emptyInfo?.buttonType"
            @btn-click="handleRefresh"
        >
            {{ emptyInfo?.text }}
        </EmptyStatus>
    </div>
</template>
<style>
:root {
    /* 兜底状态色值 */
    --adapt-list-wrapper-empty-color: currentColor;
    /* loading颜色 */
    --adapt-list-wrapper-loading-color: #773012;
}
</style>
<style lang="scss" scoped>
.list-wrapper-empty {
    position: relative;
    min-height: inherit;
    height: 100%;
    &.no-empty {
        min-height: 0 !important;
        max-height: 0 !important;
    }
}
.list-loading,
.list-loader {
    text-align: center;
    --adapt-infinityLoading-color: var(--adapt-list-wrapper-loading-color);
}
.list-loader {
    min-height: 1px;
}
.stay-center {
    --adapt-empty-status-icon-color: var(--adapt-list-wrapper-empty-color);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
}
</style>
