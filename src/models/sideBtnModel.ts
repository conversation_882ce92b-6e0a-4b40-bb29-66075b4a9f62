import { createUseModel } from '@gundam/model';
import { debounce, isNotNil } from '@pet/yau.core';

import { HugeSignInStatus } from '@/services/open-api-docs/home/<USER>/schemas';

import { useHomeModel } from './homeModel';
import { TaskType, usePopupModel } from './popup.model';
import { useSignInModel } from './signInModel';
import { TASK_ENTRY, useTaskModel } from './taskModel';

export const useSideBtnModel = createUseModel(() => {
    const { signInStatus, homeData } = useHomeModel();
    const { openResumeSignInPopup } = useSignInModel();
    const { openSummerPopup } = usePopupModel();
    const { openLLCHTaskSheet, openSheet } = useTaskModel();

    /**
     * 5.21 需要变更
     * 1. 断签中，点击下发三个按钮调起补签面板
     * 2. 彻底断签/领奖过期：点击按钮或叉掉开启新一轮；关掉后点击下方三个按钮展示挑战失败弹窗
     * 3. 领奖态：点击下方三个按钮展示挑战失败弹窗
     * @returns
     */
    function checkSignInStatus() {
        // 断签中: 调起补签面板
        if (signInStatus.value === HugeSignInStatus.INTERCEPTED) {
            openResumeSignInPopup();
            return true;
        }
        // 彻底断签 | 领奖过期：展示挑战失败弹窗
        // 待领取：调起领奖弹窗
        if (
            [HugeSignInStatus.INTERCEPTED_EXPIRE, HugeSignInStatus.REWARD_EXPIRE, HugeSignInStatus.COMPLETED].includes(
                signInStatus.value,
            ) &&
            isNotNil(homeData.value?.popList?.[0])
        ) {
            openSummerPopup(homeData.value?.popList[0], {
                taskType: TaskType.PASSIVE_POPUP,
            });
            return true;
        }
        return false;
    }

    /**
     * 点击左侧的赚现金按钮
     */
    const handleClickLeftSideBtn = debounce(() => {
        const checked = checkSignInStatus();
        if (!checked) {
            openLLCHTaskSheet();
        }
    });
    /**
     * 点击右侧的赚步数按钮
     */
    const handleClickRightSideBtn = debounce(() => {
        const checked = checkSignInStatus();
        if (!checked) {
            openSheet(TASK_ENTRY.TASK);
        }
    });

    return {
        handleClickLeftSideBtn,
        handleClickRightSideBtn,
    };
});
