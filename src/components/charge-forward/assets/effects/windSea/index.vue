<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Picture from "@pet/quantum.PictureImg";
import TVPlayer from '@pet/summer.tvplayer/index.vue';

import { useAnimationPause } from '@/models/animationVisibility';

import seaPng from './assets/fallback.png';
import seaDown from './assets/fallback.png?preset=modern';
import seaMov from './assets/sea.mov?url';
import seaMp4 from './assets/sea.mp4?url';
import seaWebm from './assets/sea.webm?url';
const { effectShowStatus } = useDowngradeLevel();
const { isAnimationPause } = useAnimationPause()
const emit = defineEmits<{
    (e: 'ended'): void;
}>();

const urls = {
    mp4: seaMp4,
    mov: seaMov,
    webm: seaWebm,
}

</script>
<template>
    <TVPlayer v-if="effectShowStatus.L1" id="8357" class="tv-wrapper" :urls="urls" :autoplay="true" :loop="true" :placeholder="seaPng" :play="!isAnimationPause" @ended="emit('ended')"/>
    <Picture v-else class="tv-wrapper" :src="seaDown" />
</template>
<style lang="scss" scoped>
.tv-wrapper {
    position: absolute;
    width: 42px;
    height: 90px;
    overflow: hidden;
    top: -30px;
}
</style>
