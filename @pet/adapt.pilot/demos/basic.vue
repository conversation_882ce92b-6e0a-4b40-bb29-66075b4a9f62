<pet-info lang="json">
{ "title": "基本使用", "description": "" }
</pet-info>
<script setup lang="ts">
import Pilot from '@pet/adapt.pilot/index.vue';
import { ref } from 'vue';

type PilotType = {
    boot: () => void;
};

const pilotRef = ref<PilotType | null>(null);

const fly = () => {
    pilotRef.value?.boot();
};
</script>

<template>
    <div class="demo">
        <h1>这里是 demo</h1>
        <div class="container">
            <Pilot ref="pilotRef" fly-to-target="target">
                <div class="start">起始点</div>
            </Pilot>
            <div id="target" class="target">目标点</div>
            <button @click="fly">飞入</button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
div {
    font-size: 16px;
}

.start,
.target {
    width: 100px;
    height: 100px;
}

.start {
    background-color: aqua;
}

.target {
    background-color: sandybrown;
}
</style>
