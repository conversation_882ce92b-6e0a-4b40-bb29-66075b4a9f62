<script setup lang="ts">
import useCaptureDebugLog from '@pet/yau.logger';

import IconBgDefault from './assets/icon_bg_default.png';
import IconBgOpened from './assets/icon_bg_opened.png';
import IconPrize from './assets/icon_prize.png';
import type { RewardPointItem } from './types';

const props = defineProps<Pick<RewardPointItem, 'icon' | 'opened' | 'isLastPoint'> & { logName: string }>();

const { opened, isLastPoint, logName } = toRefs(props);

const { log } = useCaptureDebugLog(logName.value);

const iconUrl = ref<string>();

const onImgError = () => {
    log('url 加载失败');
    if (isLastPoint.value) {
        iconUrl.value = IconPrize;
    } else {
        iconUrl.value = opened.value ? IconBgOpened : IconBgDefault;
    }
};

watch(
    () => props.icon,
    () => {
        iconUrl.value = props.icon;
    },
    { immediate: true },
);
</script>
<template>
    <img v-if="iconUrl" :src="iconUrl" :onerror="onImgError" />
</template>

<style scoped lang="scss">
img {
    object-fit: cover;
}
</style>
