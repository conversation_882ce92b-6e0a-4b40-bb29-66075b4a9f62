<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import Card from '@pet/adapt.card/index.vue';
import type { TaskListOptions } from '@pet/25cny.task-list/components/types';
import CustomTaskList from '@pet/25cny.task-list/demo-utils/CustomTaskList.vue';
import ExternalTaskList from '@pet/25cny.task-list/demo-utils/ExternalTaskList.vue';
import { DEFAULT_ICON } from '@pet/work.task-list-core/utils/const';
import { createExecutor } from '@pet/work.task-list-core/utils/framework/DefaultImpl/TriggerFlowModules/ExecuteModule/createExecutor';
import {
    type TaskActionShare,
    type TaskInfo,
    TaskTimeLimitedStatus,
} from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';

import TaskList from './components/TaskList.vue';
import CustomTaskItem from './demo-utils/CustomTaskItem.vue';

const taskListConfig = {
    subBizId: 3,
    executor: createExecutor({
        emit: async (event, taskInfo) => {
            console.log('emit');
            console.log(event, taskInfo);
            return;
        },
        share: async (event, taskInfo) => {
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
        shareByAvatar: async (event, taskInfo) => {
            // 关系链任务点击头像，可能是直接拉微信，可能是拉分享面板
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
    }),
};

const getIconUrl = (task: TaskInfo) => (Boolean(task.icons[0]) ? task.icons[0] : DEFAULT_ICON);

const taskListConfig2 = {
    subBizId: 3,
    executor: createExecutor({
        emit: async (event, taskInfo) => {
            console.log('emit');
            console.log(event, taskInfo);
            return;
        },
        share: async (event, taskInfo) => {
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
        shareByAvatar: async (event, taskInfo) => {
            // 关系链任务点击头像，可能是直接拉微信，可能是拉分享面板
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
    }),
};

const taskListConfig3 = {
    subBizId: 2835,
    executor: createExecutor({
        emit: async (event, taskInfo) => {
            console.log('emit');
            console.log(event, taskInfo);
            return;
        },
        share: async (event, taskInfo) => {
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
        shareByAvatar: async (event, taskInfo) => {
            // 关系链任务点击头像，可能是直接拉微信，可能是拉分享面板
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
    }),
};
</script>

<template>
    <div class="demo">
        <div class="container">
            <h1>默认的任务列表</h1>
            <TaskList class="demo-task-list-with-time-limited" :config="taskListConfig"></TaskList>
            <h1>默认的任务列表(限时任务)</h1>
            <TaskList class="demo-task-list-with-time-limited" :config="taskListConfig">
                <template #timeLimitedTaskItem="{ timeStates, task }">
                    自定义限时任务卡片(使用slot)
                    <Card
                        :avatar="getIconUrl(task)"
                        :title="task.title"
                        :desc="task.description"
                        size="normal"
                        class="some-card"
                    >
                        <template #ctrl>
                            <span v-if="timeStates.timeLimitedStatus === TaskTimeLimitedStatus.BeforeLimit">
                                还有{{
                                    `${timeStates.countdown.hour.value}:${timeStates.countdown.minute.value}:${timeStates.countdown.second.value}`
                                }}开始
                            </span>
                            <span v-else-if="timeStates.timeLimitedStatus === TaskTimeLimitedStatus.InLimit">
                                还有{{
                                    `${timeStates.countdown.hour.value}:${timeStates.countdown.minute.value}:${timeStates.countdown.second.value}`
                                }}结束
                            </span>
                            <span v-else-if="timeStates.timeLimitedStatus === TaskTimeLimitedStatus.AfterLimit">
                                已结束
                            </span>
                        </template>
                    </Card>
                </template>
            </TaskList>
            <h1>使用slot自定义按钮、文案</h1>
            <TaskList class="task-list-default" :config="taskListConfig">
                <template #ctrl="{ isLoading, task, doTask }">
                    <Button type="plain" @click="doTask()">{{ `${task.btnText}  加载:${isLoading}` }}</Button>
                </template>
            </TaskList>
            <h1>任务列表同时多实例</h1>
            <TaskList class="task-list-default" :config="taskListConfig3"> </TaskList>
            <h1>自定义任务项的任务列表</h1>
            <TaskList class="task-list-custom" :config="taskListConfig2">
                <template #customTaskItem="{ isLoading, task, disabled, doTask, taskList, index, timeLimitedInfo }">
                    <CustomTaskItem
                        :is-loading="isLoading"
                        :task="task"
                        :disabled="disabled"
                        :do-task="doTask"
                        :task-list="taskList"
                        :index="index"
                        :time-limited-info="timeLimitedInfo"
                    ></CustomTaskItem>
                </template>
            </TaskList>
            <h1>整个任务UI由自己决定</h1>
            <CustomTaskList />
            <h1>使用外部的任务列表，自行处理查询列表的逻辑</h1>
            <ExternalTaskList />
        </div>
    </div>
</template>

<style lang="stylus" scoped>
div
    font-size: 16px

.task-list-default
    max-height 600px
    overflow: auto;

.task-list-custom
    width 100vw
    overflow auto
    display flex
    flex-wrap wrap
    :deep(.task-item)
        margin-right 0 !important
</style>
