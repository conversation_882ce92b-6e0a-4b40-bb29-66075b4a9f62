# @pet/adapt.number-scroll

基本数值滚动组件，满足整数、小数的数值增加效果。

## 属性

| 属性名                 | 类型    | 默认值   | 可选值 | 说明                     |
| ---------------------- | ------- | -------- | ------ | ------------------------ |
| numberValue            | number  | 0        | -      | 数值数值                 |
| toFixed                | number  | 0        | -      | 保留位数                 |
| math                   | union   | 'floor'  | -      | 小数点取值方式           |
| fontSize               | number  | 12       | -      | 文字字号                 |
| lineHeight             | number  | 0        | -      | 文字行高                 |
| unit                   | string  | 'rem'    | -      | 单位                     |
| animationDuration      | number  | 500      | -      | 动效时长                 |
| max                    | number  | 9999999  | -      | 最大值                   |
| thousandsSeparator     | boolean | false    | -      | 千分位分隔               |
| notShowValue           | boolean | -        | -      | 当前暂无值时显示问号     |
| immediateEffect        | boolean | true     | -      | 是否初始化就播放动效     |
| itemEffectDelay        | number  | 0        | -      | 每位滚动延迟时间         |
| itemEffectDelayReverse | boolean | -        | -      | 每位滚动延迟是否从右向左 |
| timingFunction         | string  | 'linear' | -      | 滚动的动效曲线           |

## 事件

| 事件名        | 载荷               | 说明     |
| ------------- | ------------------ | -------- |
| transitionend | **index** `number` | 动画结束 |
