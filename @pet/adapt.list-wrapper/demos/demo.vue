<script setup lang="ts">
import { ref } from 'vue-demi';
import UserCard from '@pet/adapt.card/index.vue';
import '@pet/adapt.reset/reset.css';

import ListWrapper, { type EmptyItem } from '../index.vue';

interface UserInfo {
    avatar: string;
    title: string;
    desc: string;
    attachedText: string;
}

function fetchData(n = 10, page = 0) {
    return Array.from({ length: n }).map((x, i) => ({
        avatar: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
        title: `快手极速版-tag${page}`,
        desc: `买了高级礼包X${i}`,
        attachedText: '6.99元',
    }));
}

const userInfo = ref<UserInfo[]>([]);
const loading = ref(true);
const error = ref(false);
const page = ref(1);
const emptyMessage = ref<EmptyItem | undefined>(undefined);

function initData() {
    loading.value = true;
    setTimeout(() => {
        userInfo.value = fetchData(10);
        loading.value = false;
    }, 1000);
}

initData();

function clearData() {
    error.value = false;
    userInfo.value = [];
    emptyMessage.value = undefined;
}

function loadNewData(count: number) {
    console.log('count', count);
    if (page.value === 5) {
        return;
    }
    setTimeout(() => {
        userInfo.value = userInfo.value.concat(fetchData(5, page.value));
        page.value++;
    }, 300);
}

function getError() {
    error.value = true;
    userInfo.value = [];
}

function handleFresh() {
    initData();
}

function changeErrorMsg() {
    console.log('changeErrorMsg');
    emptyMessage.value = {
        icon: 'noGift',
        text: '暂时没有获得礼物',
        buttonText: '去查看',
    };
}
function changeErrorType() {
    console.log('changeErrorMsg');
    emptyMessage.value = {
        icon: 'noGift',
        text: '暂时没有获得礼物',
        buttonText: '去查看',
        buttonType: 'primary',
    };
}
</script>

<template>
    <div title="列表">
        <div class="ctrl-buttons">
            <button class="button" @click="clearData">清空列表</button>
            <button class="button" @click="getError">列表错误</button>
            <button class="button" @click="changeErrorMsg">变更空文案</button>
            <button class="button" @click="changeErrorType">变更空按钮类型</button>
        </div>
        <div class="container">
            <ListWrapper
                class="list-wrapper"
                :data="userInfo"
                :loading="loading"
                :error="error"
                :loader-spinner="page < 5"
                :empty-config="emptyMessage"
                @load-more="loadNewData"
                @refresh="handleFresh"
            >
                <template #item="{ data, index }">
                    <UserCard
                        :key="index"
                        class="user-card"
                        :avatar="data.avatar"
                        :title="data.title"
                        :desc="data.desc"
                        :attached-text="data.attachedText"
                    />
                </template>
                <template v-if="page === 5" #loader>
                    <div class="no-more">没有更多了</div>
                </template>
            </ListWrapper>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.button {
    height: 50px;
    padding: 0 5px;
    margin-right: 10px;
    background-color: antiquewhite;
}
.container {
    background: #f5f5f5;
    min-height: calc(100vh - 120px);
}
.ctrl-buttons {
    position: absolute;
    right: 10px;
    top: 46px;
}
.no-more {
    padding: 12px 0;
}
.user-card {
    border-radius: 0 !important;
    &:not(:first-child) {
        border-top: 1px solid #e5e5e5;
    }
}
.list-wrapper {
    margin-top: 200px;
    --adapt-list-wrapper-empty-statusbtn-color: #000;
}
</style>
