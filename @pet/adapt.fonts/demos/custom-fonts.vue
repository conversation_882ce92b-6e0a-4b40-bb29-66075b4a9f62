<script setup lang="ts"></script>

<template>
    <div class="demo">
        <h3>增长特殊字体全集</h3>
        <dl>
            <dt class="mi-sans-demi">小米字体Demibold</dt>
        </dl>
        <dl>
            <dt class="mi-sans-semi">小米字体Semibold</dt>
        </dl>
        <dl>
            <dt class="kuaiyuanhui">「快元惠体」</dt>
        </dl>
        <dl>
            <dt class="puhui105">「阿里巴巴惠普体3.0」105让天下没有难做的生意ABCDEFGHIJKLMNOPQRSTUVWXYZ</dt>
            <dd class="puhui105">0123456789</dd>
            <dd>对应figma中 Alibaba PuHuiTi 3.0 Weight 900</dd>
        </dl>
        <dl>
            <dt class="puhui115">「阿里巴巴惠普体3.0」115让天下没有难做的生意ABCDEFGHIJKLMNOPQRSTUVWXYZ</dt>
            <dd class="puhui115">0123456789</dd>
            <dd>对应figma中 Alibaba PuHuiTi 3.0 Weight 1000</dd>
        </dl>
        <dl>
            <dt class="fzcchjw">「方正超粗黑简体」</dt>
            <dd class="fzcchjw">0123456789</dd>
            <dd>对应figma中 FZChaoCuHei-M10S</dd>
        </dl>
        <dl>
            <dt class="din">「DIN」</dt>
            <dd class="din">0123456789</dd>
            <dd>对应figma中 DIN Alternates</dd>
        </dl>

        <i
            >为什么figma字体名不能对应<a
                href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/CSS_fonts/Variable_fonts_guide"
                target="_blank"
                >Variable fonts</a
            ></i
        >
    </div>
</template>

<style lang="scss" scoped>
@import '../style/kuaiyuanhui.css';
@import '../style/puhui.css';
@import '../style/fzcchjw.css';
@import '../style/din.css';
@import '../style/misans.css';

.demo {
    font-size: 14px;
    dl {
        font-size: 30px;
    }
    dd {
        font-size: 16px;
        margin: 0;
        text-indent: 1em;
        color: #ff0606;
        font-weight: 700;
    }
    a {
        color: #222;
    }
}

h3 {
    font-size: 30px;
    font-weight: 400;
}

.kuaiyuanhui {
    font-family: kuaiyuanhui, sans-serif;
}

.mi-sans-demi {
    font-family: MiSans, sans-serif;
    font-weight: 450;
}

.mi-sans-semi {
    font-family: MiSans, sans-serif;
    font-weight: 520;
}

.puhui105 {
    font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
    font-weight: 900;
}

.puhui115 {
    font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
    font-weight: 1000;
}

.fzcchjw {
    font-family: FZChaoCuHei-M10S, sans-serif;
}

.din {
    font-family:
        DIN Alternates,
        sans-serif;
}
</style>
