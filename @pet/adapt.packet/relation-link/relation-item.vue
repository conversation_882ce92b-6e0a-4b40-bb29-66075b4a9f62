<script setup lang="ts">
import Avatar from '@pet/adapt.avatar/index.vue';
import AvatarGroup from '@pet/adapt.avatar/avatar-group.vue';
import ArrRight from '../assets/arr-right.vue';
import type { RelationItemInfo } from '../types';

defineProps<{
    info: RelationItemInfo;
}>();

const emits = defineEmits<{
    (e: 'groupClick'): void;
    (e: 'inviteAvatarClick'): void;
}>();
</script>

<template>
    <div :class="['relation-item', info.type === 'group' && 'group']">
        <template v-if="info.type === 'normal'">
            <Avatar :src="info.userInfo.avatar" :width="38" class="avatar"></Avatar>
            <div class="name">{{ info.userInfo.name }}</div>
            <div v-if="info.userInfo.amount" class="amount">
                <span class="mark">+</span><span class="number">{{ info.userInfo.amount }}</span
                ><span class="unit">{{ info.userInfo.unit }}</span>
            </div>
        </template>
        <div v-else-if="info.type === 'group'" @click="emits('groupClick')">
            <AvatarGroup
                :srcs="info.userList.map((item) => item.avatar)"
                :width="38"
                :gap="6"
                class="avatar group"
                :max="3"
                :rest-type="info.userList.length > 3 ? 'icon' : 'none'"
                reverse
            ></AvatarGroup>
            <div id="group_check" class="desc go-check">
                去查看
                <ArrRight class="arr" />
            </div>
        </div>
        <template v-else-if="info.type === 'invite'">
            <Avatar :width="38" class="invite" status="invite" @click="emits('inviteAvatarClick')"></Avatar>
            <div class="desc">待邀请</div>
        </template>
        <template v-else-if="info.type === 'more'">
            <Avatar :width="38" class="invite" status="more"></Avatar>
            <div class="number">+{{ info.count }}</div>
        </template>
    </div>
</template>

<style lang="scss" scoped>
@import '../common.scss';

.relation-item {
    display: flex;
    flex-direction: column;
    width: 44px;
    align-items: center;
    position: relative;
    color: var(--adapt-packet-inner-relation-text-color);
    font-family: 'PingFang SC', sans-serif;

    .avatar {
        margin-bottom: 2px;
        --adapt-avatar-border-width: 1px;
        --adapt-avatar-border-color: var(--adapt-packet-inner-relation-avatar-border-color);
        --adapt-avatar-group-more-mask-width: 38px;
        --adapt-avatar-group-more-mask-height: 38px;
    }

    .invite {
        margin-bottom: 2px;
        --adapt-avatar-border-color: unset;
        --adapt-avatar-icon-color: var(--adapt-packet-inner-relation-avatar-icon-color);
        --adapt-avatar-mask-color: var(--adapt-packet-inner-relation-icon-mask-color);
        --adapt-avatar-border-width: 0;
    }

    .name,
    .desc {
        line-height: 15px;
        @include ellipse;
        width: 100%;
        font-size: 11px;
        text-align: center;
        --adapt-packet-arr-right-color: var(--adapt-packet-inner-relation-text-color);
    }
    .go-check {
        @include flex-center;

        .arr {
            margin-bottom: 1px;
        }
    }

    .number {
        font-size: 12px;
        font-weight: bold;
        font-family: 'PingFang SC', sans-serif;
        margin-top: -1px;
    }

    .amount {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        line-height: 20px;
        color: var(--adapt-packet-inner-relation-amount-color);

        .mark {
            display: inline-block;
            font-size: 12px;
            font-weight: bold;
        }

        .number {
            font-size: 14px;
            font-weight: bold;
        }

        .unit {
            font-weight: bold;
            font-size: 11px;
        }
    }
}

.group {
    width: 52px;
}
</style>
