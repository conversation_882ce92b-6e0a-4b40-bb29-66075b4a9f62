<script lang="ts">
export default {
    name: 'AdaptBadge',
};
</script>

<script lang="ts" setup>
import { px2rem } from '@pet/core.mobile';
import { computed, useSlots } from 'vue-demi';

import type { BadgeType, BadgeAlignType } from './types';

interface BadgeBaseProps {
    /**
     * 徽标类型
     * @values 'round' | 'sharp' | 'dot' | 'dark'
     * 对应示意：数字 | 汉字 | 圆点 | 黑色
     */
    type?: BadgeType;
    /**
     * 徽标内容
     */
    value?: number | string;
    /**
     * 数字型显示最大数值，如200显示为99+
     */
    max?: number;
    /**
     * 徽标对齐的方式
     */
    align?: BadgeAlignType;
    /**
     * 徽标父容器的x轴的坐标，基于414，单位px
     */
    offsetX?: number;
    /**
     * 徽标父容器的y轴的坐标，基于414，单位px
     */
    offsetY?: number;
    /**
     * 是否需要成为inline元素
     */
    inline?: boolean;
    /**
     * 是否为大规格
     */
    large?: boolean;
}

const props = withDefaults(defineProps<BadgeBaseProps>(), {
    type: 'round',
    value: 0,
    max: 99,
});

const computedValue = computed(() => (+props.max < +props.value ? props.max : props.value));
const badgeType = computed(() => [
    `badge-${props.type}`,
    props.large && 'badge-size-large',
    typeof props.value === 'number' && 'badge-value-number',
]);
const slots = useSlots();
const badgeWrapper = computed(() => (slots.root ? 'badge-wrapper-root' : 'badge-wrapper-item'));
const isInline = computed(() => slots.root && props.inline && 'is-wrapper-inline');
const badgePosition = computed(() => [
    slots.root && 'badge-absolute',
    props.align === 'self-center' && 'badge-self-center',
]);
const badgeWhere = computed(() => {
    const info = props.align?.split('-');
    const xAxis = info?.includes('right') ? 'right' : 'left';
    const yAxis = info?.includes('bottom') ? 'bottom' : 'top';
    return slots.root && props.align !== 'self-center'
        ? {
              [xAxis]: props.offsetX ? px2rem(props.offsetX) : 0,
              [yAxis]: props.offsetY ? px2rem(props.offsetY) : 0,
          }
        : {};
});
const showMoreTag = computed(() => {
    if (typeof props.value === 'number') {
        return props.max < props.value;
    }
    return false;
});
</script>

<template>
    <span :class="[badgeWrapper, isInline]">
        <span :class="['badge', badgeType, badgePosition]" :style="badgeWhere">
            <template v-if="value">
                {{ computedValue }}
                <span v-if="showMoreTag" class="badge-more"></span>
            </template>
            <slot />
        </span>
        <slot name="root" />
    </span>
</template>

<style>
:root {
    /* 边框大小 */
    --adapt-badge-border-width: 0;
    /* 边框颜色 */
    --adapt-badge-border-color: transparent;
    /* 普通背景色 */
    --adapt-badge-flat-background: #fe3666;
    /* 文字色 */
    --adapt-badge-font-color: #fff;
    /* 默认高度 */
    --adapt-badge-height: 18px;
    /* 默认内边距 */
    --adapt-badge-padding-x: 6px;
    /* 默认字号 */
    --adapt-text-font-size: 9px;
}
</style>

<style lang="scss" scoped>
$badge-border: 0 0 0 var(--adapt-badge-border-width) var(--adapt-badge-border-color);
$badge-bg-color: var(--adapt-badge-flat-background);
$badge-dark-color: rgba(#000, 0.5);
$badge-text-font-size: var(--adapt-text-font-size);
$badge-font-color: var(--adapt-badge-font-color, #fff);
$badge-height: var(--adapt-badge-height);
$badge-dot-height: 8px;

.badge-wrapper-root {
    display: table;
    position: relative;
    &.is-wrapper-inline {
        display: inline-table;
    }
}
.badge-wrapper-item {
    display: inline-flex;
}
.badge-absolute {
    position: absolute;
}
.badge-self-center {
    right: 0;
    transform: translateX(50%);
}
.badge {
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: $badge-border;
    background: $badge-bg-color;
    font-size: $badge-text-font-size;
    color: $badge-font-color;
    white-space: nowrap;
    overflow: hidden;
    box-sizing: border-box;
    height: $badge-height;
    &.badge-value-number {
        --adapt-text-font-size: 10px;
    }
    &.badge-size-large {
        --adapt-text-font-size: 13px;
        --adapt-badge-height: 22px;
        &.badge-value-number {
            --adapt-text-font-size: 11px;
        }
    }
}
.badge-round {
    min-width: $badge-height;
    padding: 0 var(--adapt-badge-padding-x);
    border-radius: $badge-height;
}
.badge-sharp {
    padding: 0 var(--adapt-badge-padding-x);
    border-radius: 8px 10px 10px 2px;
}
.badge-dot {
    width: $badge-dot-height;
    height: $badge-dot-height;
    min-width: 0;
    padding: 0;
    border-radius: 50%;
    font-size: 0;
}
.badge-dark {
    background: $badge-dark-color;
    min-width: $badge-height;
    height: $badge-height;
    padding: 0 var(--adapt-badge-padding-x);
    border-radius: $badge-height;
}
.badge-more {
    margin-top: -1px;
    &::after {
        content: '+';
    }
}
</style>
