<script setup lang="ts">
import { computed } from 'vue-demi';
import type { AssistInfo } from '../types';
import AvatarGroup from '@pet/adapt.avatar/avatar-group.vue';

interface Props {
    info: AssistInfo;
}

const props = defineProps<Props>();

const assistResult = computed(() => {
    const assistUserList = props.info?.userList;
    if (assistUserList?.length) {
        return {
            length: assistUserList.length,
            avatarList: assistUserList.slice(0, 3).map((item) => item.avatar),
            firstUserName: assistUserList[0]?.name,
        };
    }
    return {};
});
</script>

<template>
    <div class="assist-info">
        <AvatarGroup
            class="avatar"
            :width="19"
            :gap="13"
            :srcs="info.userList.slice(0, 3).map((item) => item.avatar)"
        />
        <span class="name">{{ assistResult.firstUserName }}</span>
        <span v-if="assistResult.length && assistResult.length > 1"> 等{{ assistResult.length }}人</span>
        <span>{{ info?.desc }}</span>
    </div>
</template>

<style lang="scss" scoped>
@import '../common.scss';
.assist-info {
    display: flex;
    justify-content: center;
    height: 20px;
    width: 100%;
    text-align: center;
    margin-top: 6px;
    font-size: 14px;
    color: #fff4ca;
    font-family: 'PingFang SC', sans-serif;
    .avatar {
        margin-right: 4px;
        --adapt-avatar-border-width: 0.5px;
        --adapt-avatar-border-color: #ffde9f;
    }

    > span {
        height: 20px;
        line-height: 20px;
        display: inline-block;
    }

    .name {
        @include ellipse;
        max-width: 60px;
        margin-right: 2px;
    }
}
</style>
