import { useOpenPage } from '@pet/yau.yoda/index';
/** 各种跳转场景 */
export function useNavigate() {
    const openPage = useOpenPage();
    // 跳转城市二级页面
    const gotoCityPage = (query?: Record<string, any>) => {
        openPage('/city', {
            keepQuery: true,
            query: {
                ...query,
            },
        });
    };
    // 跳转首页
    const gotoHomePage = () => {
        openPage('/home', {
            keepQuery: true,
        });
    };
    const jumpToProfile = (userId: number) => {
        openPage(`kwai://profile/${userId}`);
    };
    return {
        gotoCityPage,
        gotoHomePage,
        jumpToProfile,
    };
}
