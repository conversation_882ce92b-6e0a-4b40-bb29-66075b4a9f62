{"id": "lnpjugk0tbp", "projectName": "summer-activity-2025-h5", "projectChineseName": "25暑期活动h5", "template": {"id": 3, "name": "H5-Vue3", "platform": "H5", "framework": "Vue3", "plugins": [{"name": "@gundam/gundam-plugin-yoda", "description": "<PERSON><PERSON>", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5"], "domain": "bridge", "tags": ["容器"], "docs": "yoda", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-font-min", "description": "字体裁剪", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "font", "tags": ["字体", "性能"], "docs": "font-min", "contributors": ["<PERSON><PERSON>junhe", "<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-deliver", "description": "用于派发各种默认依赖", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "tags": ["效率"], "docs": "", "contributors": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-mock", "description": "接口Mock", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "mock", "tags": ["mock"], "docs": "mock", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>", "chenzihao03", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-deploy-staging", "description": "一键部署Staging环境", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "test", "tags": ["部署", "测试"], "docs": "deploy-staging", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "zhong<PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-fmp", "description": "自动上报FMP", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "performance", "tags": ["性能", "监控"], "docs": "fmp", "contributors": ["ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-output-filename-normalize", "description": "提供一套默认的build输出配置", "type": "Vite", "framework": ["Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["标准化"], "docs": "output-filename-normalize", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-request", "description": "网络请求", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "network", "tags": ["请求"], "docs": "request", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-weblogger", "description": "埋点&监控", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "logger", "tags": ["监控", "稳定性"], "docs": "weblogger", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-led", "description": "自动创建LED项目", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "deploy", "tags": ["部署", "测试"], "docs": "led", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "zhong<PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-radar", "description": "自动创建雷达项目", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "operation", "tags": ["radar", "监控"], "docs": "radar", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-radar-seed", "description": "雷达种子包", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "stability", "tags": ["监控", "radar"], "docs": "radar-seed", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-yoda-seed", "description": "Yoda seed", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5"], "domain": "bridge", "tags": ["yoda", "容器"], "docs": "yoda-seed", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-blank-screen-detector", "description": "白屏检测插件", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "stability", "tags": ["监控", "radar"], "docs": "blank-screen-detector", "contributors": ["zhangbo10"]}, {"name": "@gundam/gundam-plugin-vite-info", "description": "增强Vite的输出", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["效率"], "docs": "", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-unocss", "description": "自动安装并完成unocss的配置", "type": "Vite", "framework": ["Vue3"], "platform": ["H5", "PC"], "domain": "style", "tags": ["css"], "docs": "unocss", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-postcss", "description": "自动postcss插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "style", "tags": ["css"], "docs": "postcss", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-css-normalize", "description": "用于统一不同浏览器的默认样式。", "type": "Vite", "framework": ["Vue3"], "platform": ["H5", "PC"], "domain": "compatibility", "tags": ["css", "标准化"], "docs": "css-normalize", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-split-chunks", "description": "分包插件", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "split-chunks", "tags": ["性能"], "docs": "split-chunks", "contributors": ["cuiliangliang", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-sharp-ui-next", "description": "sharp-ui-next的自动按需导入", "type": "Vite", "framework": ["Vue3"], "platform": ["H5"], "domain": "component", "tags": ["UI", "组件"], "docs": "<PERSON><PERSON>-next", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-script-error-clarifier", "description": "为Script标签配置crossorigin", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "stability", "tags": ["稳定性"], "docs": "script-error-clarifier", "contributors": ["su<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-hyper", "description": "上传打包产物到性能归因平台", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "performance", "tags": ["性能", "工具"], "docs": "hyper", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-led-debug", "description": "适用于移动端的抓包与Devtools", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["测试", "域名", "工具"], "docs": "", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-portal", "description": "快捷打开各插件应用页面", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["效率"], "docs": "", "contributors": ["zhong<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-prefetch-api", "description": "gundam prefetch-api插件", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "performance", "tags": ["性能"], "docs": "prefetch-api", "contributors": ["l<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-cdn", "description": "配置CDN", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "deploy", "tags": ["部署"], "docs": "cdn", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-api-generator", "description": "根据 swagger3 接口文档生成 interface 和 service 请求文件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "mock", "tags": ["请求", "效率"], "docs": "api-generator", "contributors": ["chenzihao03", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-code-style", "description": "代码规范", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue3", "Vue2"], "platform": ["PC", "H5"], "domain": "standard", "tags": ["规范"], "docs": "code-style", "contributors": ["<PERSON><PERSON>junhe", "<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-gitlab", "description": "创建gitlab仓库", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["仓库"], "docs": "gitlab", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-kfx", "description": "创建kfx应用", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "deploy", "tags": ["部署"], "docs": "kfx", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-kdev", "description": "kdev CD流水线创建插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "standard", "tags": ["CI", "CD", "流水线"], "docs": "kdev", "contributors": ["ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong", "yumingming"]}, {"name": "@gundam/gundam-plugin-kfc", "description": "创建 kfc 应用插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "standard", "tags": ["规范"], "docs": "kfc", "contributors": ["<PERSON><PERSON>junhe", "ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-coverage", "description": "前端代码覆盖率", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "coverage", "tags": ["增量代码覆盖率", "工具"], "docs": "coverage", "contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-meta-priority", "description": "帮助Meta标签尽可能的出现HTML Head标签的前面", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "meta-priority", "tags": ["兼容性"], "docs": "meta-priority", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-babel", "description": "帮助业务安装一些必备的babel插件，包括安全、编译等babel插件", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "contributors": ["<PERSON><PERSON><PERSON><PERSON>"]}], "type": "Git", "uri": "*************************:mfe/platform/gundam/template-h5-vue3.git", "description": "移动端Vue3的模板"}, "serviceTreeNode": {"name": "activity", "value": 64021, "path": "/kuaishou/webservice/user-growth/frontend/activity"}, "plugins": ["@gundam/gundam-plugin-yoda", "@gundam/gundam-plugin-font-min", "@gundam/gundam-plugin-deliver", "@gundam/gundam-plugin-mock", "@gundam/gundam-plugin-deploy-staging", "@gundam/gundam-plugin-fmp", "@gundam/gundam-plugin-output-filename-normalize", "@gundam/gundam-plugin-request", "@gundam/gundam-plugin-weblogger", "@gundam/gundam-plugin-led", "@gundam/gundam-plugin-radar", "@gundam/gundam-plugin-radar-seed", "@gundam/gundam-plugin-yoda-seed", "@gundam/gundam-plugin-blank-screen-detector", "@gundam/gundam-plugin-vite-info", "@gundam/gundam-plugin-unocss", "@gundam/gundam-plugin-postcss", "@gundam/gundam-plugin-css-normalize", "@gundam/gundam-plugin-split-chunks", "@gundam/gundam-plugin-sharp-ui-next", "@gundam/gundam-plugin-script-error-clarifier", "@gundam/gundam-plugin-hyper", "@gundam/gundam-plugin-led-debug", "@gundam/gundam-plugin-portal", "@gundam/gundam-plugin-prefetch-api", "@gundam/gundam-plugin-cdn", "@gundam/gundam-plugin-api-generator", "@gundam/gundam-plugin-code-style", "@gundam/gundam-plugin-gitlab", "@gundam/gundam-plugin-kfx", "@gundam/gundam-plugin-kdev", "@gundam/gundam-plugin-kfc", "@gundam/gundam-plugin-coverage", "@gundam/gundam-plugin-meta-priority", "@gundam/gundam-plugin-babel", "@gundam/gundam-plugin-ssg", "@gundam/gundam-plugin-ssg-config", "@gundam/gundam-plugin-inject", "@gundam/gundam-plugin-sig3", "@gundam/gundam-plugin-image-min", "@gundam/gundam-plugin-css-path-absolute", "@gundam/gundam-plugin-fallback"], "customConfig": {"@gundam/gundam-plugin-blank-screen-detector": {"containers": ["#app"], "emptyElementsPercent": 0.9, "sameElementsPercent": 0.9, "ignoreContainers": ["CANVAS", "VIDEO"], "kConfKey": "summer2025.wishTravel.blankScreenReport"}, "@gundam/gundam-plugin-font-min": {"fontSrc": "../@pet/adapt.fonts/assets", "fontPath": "../font.json", "dest": "../src/assets/font"}, "@gundam/gundam-plugin-weblogger": {"autoCreateWeblog": true}, "@gundam/gundam-plugin-coverage": {"include": ["src/"], "coverageVariable": "summer-activity-2025-h5"}, "@gundam/gundam-plugin-image-min": {"sourcePath": ["../src/assets", "../src/components/charge-forward/assets"], "whiteList": [], "compressConfig": {"quality": 80}}, "@gundam/gundam-plugin-css-path-absolute": {"cdnHosts": ["p5-plat.wskwai.com", "p4-plat.wskwai.com", "p2-plat.wskwai.com", "p66-plat.wskwai.com", "p66-plat.wsbkwai.com", "p2-plat.wsbkwai.com", "p1-plat.wsbkwai.com", "p4-plat.wsbkwai.com", "p5-plat.wsbkwai.com", "p3-plat.wskwai.com", "p2-plat.wsbkwai.com"], "cdnPath": "kos/nlav111449/activity/assets", "applyAll": false, "htmlClassName": ["detect-ready"]}, "@gundam/gundam-plugin-fallback": {"iOSMinVersion": 12, "androidMinVersion": 6, "chromeMinVersion": 70, "disabledModels": "", "fallbackUrl": "https://ppg.viviv.com/doodle/fLGFOwtV.html?uni_src=other_secondary_page&layoutType=4&noBackNavi=true", "fallbackUrlFourTab": "https://ppg.viviv.com/doodle/fLGFOwtV.html?uni_src=activity_tab&layoutType=4&noBackNavi=true"}, "@gundam/gundam-plugin-cdn": {"sdkOptions": {"projectNames": ["mfe-common", "summer2025-server"]}}}, "ledId": "summer-activity-2025-h5-ruanyeli", "radarId": "c15db43435", "gitlabId": 99634, "devCloudProductName": "summer-activity-2025-h5", "devCloudProductId": 85069, "kfxId": "755a36be97", "kdevPipeline": [{"name": "staging", "pipelineId": 12215073, "pipelineName": "staging环境流水线-[summer-activity-2025-h5]"}, {"name": "online", "pipelineId": 12215074, "pipelineName": "prod环境流水线-[summer-activity-2025-h5]"}, {"name": "prt", "pipelineId": 12215072, "pipelineName": "prt环境流水线-[summer-activity-2025-h5]"}], "kfcId": "24651"}