<script lang="ts" setup></script>
<template>
    <svg width="1em" height="1.2em" viewBox="0 0 10 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M4 3L7 6.00005L4.00009 9"
            stroke="currentColor"
            stroke-opacity="0.8"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
</template>
<style lang="scss" scoped>
.invite-svg {
    color: var(--adapt-packet-arr-right-color);
    vertical-align: top;
}
</style>
