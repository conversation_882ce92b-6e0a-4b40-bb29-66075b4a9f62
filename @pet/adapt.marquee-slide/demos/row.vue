<pet-info lang="json">
{
    "title": "横向滚动",
    "description": "横向切换跑马灯",
    "priority": 97
}
</pet-info>
<script setup lang="ts">
import { MarqueeSlide } from '../index';

const messageList = ['获得1000枚金币', '获得2000积分', '获得100元'];
</script>

<template>
    <div class="container">
        <MarqueeSlide :list="messageList" class="marquee-box" direction="row">
            <template #item="{ data, index }">
                <div class="row">
                    <div class="line">
                        <b>index: {{ index }}</b> data: {{ data }}
                    </div>
                </div>
            </template>
        </MarqueeSlide>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.marquee-box {
    font-size: 14px;
    width: 300px;
    height: 20px;
    margin-bottom: 10px;
}
</style>
