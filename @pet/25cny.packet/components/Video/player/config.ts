/* cSpell:disable */
/** 直播mock数据，可以从这里获取mock数据：https://docs.corp.kuaishou.com/d/home/<USER>/
export const data = {
    giftComboBuffSeconds: 10,
    liveAdaptiveManifest: [
        {
            autoDefaultSelect: true,
            adaptationSet: {
                gopDuration: 2000,
                representation: [
                    {
                        id: 0,
                        // eslint-disable-next-line sonarjs/no-duplicate-string
                        url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_sd1000.flv?auth_key=1730190304-0-0-0d05a814281e55ea463580acfd3bf9de&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                        bitrate: 1000,
                        qualityType: 'STANDARD',
                        level: 30,
                        name: '高清',
                        shortName: '高清',
                        hidden: false,
                        enableAdaptive: true,
                        defaultSelect: false,
                        avgBitrate: 0,
                        resolution: '960x540',
                        streamState: 0,
                        codec: 'AVC',
                    },
                    {
                        id: 1,
                        url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_hd2000.flv?auth_key=1730190304-0-0-64e1a1139d0a41b273596eded2881f4c&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                        bitrate: 2000,
                        qualityType: 'HIGH',
                        level: 50,
                        name: '超清',
                        shortName: '超清',
                        hidden: false,
                        enableAdaptive: true,
                        defaultSelect: true,
                        avgBitrate: 0,
                        resolution: '1280x720',
                        streamState: 0,
                        codec: 'AVC',
                    },
                    {
                        id: 2,
                        url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_hd4000.flv?auth_key=1730190304-0-0-d361d7f29fee23673c7d534e922880e0&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                        bitrate: 4000,
                        qualityType: 'SUPER',
                        level: 70,
                        name: '蓝光 4M',
                        shortName: '4M',
                        hidden: false,
                        enableAdaptive: true,
                        defaultSelect: false,
                        avgBitrate: 0,
                        resolution: '1920x1080',
                        streamState: 0,
                        codec: 'AVC',
                    },
                    {
                        id: 3,
                        url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_avc.flv?auth_key=1730190304-0-0-fd573bfd22571a172d14a3a1075c43f7&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                        bitrate: 8000,
                        qualityType: 'BLUE_RAY',
                        level: 130,
                        name: '蓝光 质臻',
                        shortName: '质臻',
                        hidden: false,
                        enableAdaptive: false,
                        defaultSelect: false,
                        avgBitrate: 0,
                        resolution: '1920x1080',
                        streamState: 0,
                        codec: 'AVC',
                    },
                ],
            },
            cdnFeature: [],
            hideAuto: false,
            businessType: 0,
            freeTrafficCdn: false,
            type: 'dynamic',
            version: '2.0',
            Mue: 0.006216477095998353,
        },
    ],
    livePolicy: ['multiRate'],
    stat: {
        clientId: 'PC',
    },
    expectFreeTraffic: true,
    playUrls: [
        {
            freeTrafficCdn: false,
            cdn: 'Alibaba',
            pushCdn: 'OriginPCLiveMate',
            url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_sd1000.flv?auth_key=1730190304-0-0-0d05a814281e55ea463580acfd3bf9de&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
            cdnIp: null,
            ipValidTime: null,
            bitrate: 1000,
            Mue: 0.006216477095998353,
            codec: 'AVC',
        },
    ],
    multiResolutionPlayUrls: [
        {
            type: 'standard',
            level: 30,
            name: '高清',
            shortName: '高清',
            defaultSelect: false,
            urls: [
                {
                    freeTrafficCdn: false,
                    cdn: 'Alibaba',
                    pushCdn: 'OriginPCLiveMate',
                    url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_sd1000.flv?auth_key=1730190304-0-0-0d05a814281e55ea463580acfd3bf9de&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                    cdnIp: null,
                    ipValidTime: null,
                    bitrate: 1000,
                    Mue: 0.006216477095998353,
                    codec: 'AVC',
                },
            ],
        },
        {
            type: 'high',
            level: 50,
            name: '超清',
            shortName: '超清',
            defaultSelect: true,
            urls: [
                {
                    freeTrafficCdn: false,
                    cdn: 'Alibaba',
                    pushCdn: 'OriginPCLiveMate',
                    url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_hd2000.flv?auth_key=1730190304-0-0-64e1a1139d0a41b273596eded2881f4c&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                    cdnIp: null,
                    ipValidTime: null,
                    bitrate: 2000,
                    Mue: 0.006216477095998353,
                    codec: 'AVC',
                },
            ],
        },
        {
            type: 'super',
            level: 70,
            name: '蓝光 4M',
            shortName: '4M',
            defaultSelect: false,
            urls: [
                {
                    freeTrafficCdn: false,
                    cdn: 'Alibaba',
                    pushCdn: 'OriginPCLiveMate',
                    url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_hd4000.flv?auth_key=1730190304-0-0-d361d7f29fee23673c7d534e922880e0&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                    cdnIp: null,
                    ipValidTime: null,
                    bitrate: 4000,
                    Mue: 0.006216477095998353,
                    codec: 'AVC',
                },
            ],
        },
        {
            type: 'blueRay',
            level: 130,
            name: '蓝光 8M',
            shortName: '8M',
            defaultSelect: false,
            urls: [
                {
                    freeTrafficCdn: false,
                    cdn: 'Alibaba',
                    pushCdn: 'OriginPCLiveMate',
                    url: 'http://ali-adaptive.pull.yximgs.com/gifshow/iHQXvkq1iP0_avc.flv?auth_key=1730190304-0-0-fd573bfd22571a172d14a3a1075c43f7&tsc=origin&oidc=watchmen&sidc=3054&no_script=1&ss=s19',
                    cdnIp: null,
                    ipValidTime: null,
                    bitrate: 8000,
                    Mue: 0.006216477095998353,
                    codec: 'AVC',
                },
            ],
        },
    ],
    hosts: ['live.gifshow.com', 'live.ksapisrv.com'],
    patternType: 2,
    locale: 'zh_CN_#Hans',
    streamType: 1,
    abrConfigs:
        '{"businessType":0,"dynamicConfig":"","authorTypes":["GAME_HIGH_RESOLUTION","PROFESSIONAL_GAME_AUTHOR","NEW_BORN_AUTHOR","COMMON_GAME_AUTHOR"],"strategy":[""],"sensitiveScore":-1,"lowDelaySensitiveScore":-1,"postProcessScore":-1,"Mue":0.006216477095998353}',
    liveStreamId: 'iHQXvkq1iP0',
    isFromLiveMate: true,
    multiTabLive: false,
    giftComboBuffThreshold: 50,
    subType: 0,
    playRtmpUrl:
        'http://tx-pclivemate.pull.yximgs.com/live/6000_iHQXvkq1iP0.flv?txSecret=3b7ae0f1415a4e50538b4f103671f538&txTime=67209be0&stat=vvPGxez7pTqDvgekSh2kTpk9HeSUJE54EYmQ8aJYP2wOdgtq3y5gmYIw0igVCmzB',
    landscape: true,
};
