import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import { EOL as endOfLine } from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const currentDir = resolve(__dirname, '../');
const demoFile = resolve(__dirname, '../demos/all-icons.vue');

function getIcons() {
    return fs.readdirSync(currentDir).filter((file) => file.includes('-svg.vue'));
}

function capitalize(str) {
    return str
        .toLowerCase()
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join('');
}

function createVue() {
    const icons = getIcons();
    let importTpl = '';
    let templateTpl = '';
    if (icons.length) {
        icons.forEach((icon) => {
            const ComponentName = `${capitalize(icon.replace('-svg.vue', ''))}Icon`;
            importTpl += `import ${ComponentName} from '../${icon}';${endOfLine}`;
            templateTpl += `    <div class="item"><${ComponentName} class="icon" /><h4>${icon}</h4></div>${endOfLine}`;
        });
    }

    const fileContent = `<script setup lang="ts">
/* generate by gen_demo.js */
${importTpl}</script>
<template>
<div>
${templateTpl}</div>
</template>
<style src="../assets/css/demo.scss" lang="scss" scoped></style>
`;
    return fileContent;
}

function writeFile() {
    fs.writeFile(demoFile, createVue(), (err) => {
        if (err) {
            console.log(err);
        } else {
            console.log('all-icons.vue written successfully');
        }
    });
}

writeFile();
