<script setup lang="ts">
import type { MessageCard } from '../types';
import HighLightText from '../high-light-text/high-light-text.vue';

defineProps<{
    info: MessageCard;
}>();
</script>

<template>
    <div class="wrapper">
        <div class="icon"></div>
        <div class="detail">
            <HighLightText class="title" :pre-text="info.title"></HighLightText>
            <span class="sub">{{ info.desc }}</span>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import '../common.scss';

.wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding-left: 16px;
    width: 304px;
    height: 74px;
    margin: auto;
    background: url(../assets/board.png) no-repeat center/100%;

    .icon {
        width: 56px;
        height: 56px;
        margin-right: 8px;
        background: url(../assets/award-icon.png) no-repeat center/100%;
    }

    .detail {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        height: 100%;
        flex: 1;
        color: #d7d7d7;

        .main {
            font-size: 14px;
            line-height: 22px;
            font-weight: bold;
            margin-bottom: 4px;

            span {
                font-size: 17px;
                line-height: 22px;
                font-weight: bold;
                color: #fff4ca;
            }
        }

        .title {
            font-size: 14px;
            line-height: 22px;
        }

        .sub {
            font-size: 12px;
            line-height: 16px;
            margin-top: 4px;
        }
    }
}
</style>
