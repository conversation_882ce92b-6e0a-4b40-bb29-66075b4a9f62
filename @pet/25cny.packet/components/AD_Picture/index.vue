<script setup lang="ts">
export interface AD_PictureParams {
    prizeType: 'adPicture';
    src: string;
}

defineProps<AD_PictureParams>();
</script>

<template>
    <img class="pic" :src="src" />
</template>

<style lang="scss" scoped>
.pic {
    margin-bottom: -27px;
    width: 228px;
    height: 130px;
    background: no-repeat center/100%;
    border-radius: 12px;
    overflow: hidden;
}
</style>
