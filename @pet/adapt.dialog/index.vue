<script lang="ts">
export default {
    name: 'AdaptDialog',
};
</script>
<script lang="ts" setup>
import { computed, useSlots } from 'vue-demi';
import type { AdaptTransitionName as AnimationType } from '@pet/adapt.transition/types';
import Popup from '@pet/adapt.popup/index.vue';
import Box from '@pet/adapt.box/box.vue';
import Logo from '@pet/adapt.logo/index.vue';
import LayCartoon from '@pet/adapt.lay-cartoon/index.vue';
import Button from '@pet/adapt.button/index.vue';
import IconArr from '@pet/adapt.icons/arr-svg.vue';

interface DialogProps {
    /**
     * 是否展示
     */
    show?: boolean;
    /**
     * 动效类型
     */
    aniType?: AnimationType;
    /**
     * 自定义logo地址、商业化logo
     */
    customLogo?: string;
    /**
     * 头部卡通图
     */
    cartoon?: boolean;
    /**
     * 头部卡通图在面板的位置、默认上方
     */

    cartoonPos?: 'top' | 'bottom';
    /**
     * 主标题
     */
    title?: string;
    /**
     * 次标题
     */
    subTitle?: string;
    /**
     * 弹窗内展示的icon中部图
     */
    icon?: string;
    /**
     * 确认信息内容，用于二次确认
     */
    message?: string;
    /**
     * 确认按钮文案、右边主按钮
     */
    confirmText?: string;
    /**
     * 取消按钮文案、左边此按钮
     */
    cancelText?: string;
    /**
     * 确认按钮内部第二行副文案
     */
    confirmSubText?: string;
    /**
     * 主按钮不可用
     */
    btnDisabled?: boolean;
    /**
     * 主按钮加载中
     */
    btnLoading?: boolean;
    /**
     * 按钮组底部文案
     */
    bottomMessage?: string;
    /**
     * 弹窗底部带尖角的文字按钮
     */
    bottomBtnText?: string;
    /**
     * 显示关闭按钮
     */
    showClose?: boolean;
    /**
     * 弹窗mask是否点击关闭
     */
    maskCloseable?: boolean;
    /**
     * 是够需要内部滚动
     */
    innerScroll?: boolean;
    /**
     * 关闭飞入对应的DomID位置
     */
    flyToTarget?: string;
    /**
     * 飞入时长
     */
    enterDuration?: number;
    /**
     * 离开时长
     */
    leaveDuration?: number;
    /**
     * 飞入延时
     */
    enterDelay?: number;
    /**
     * 离开延时
     */
    leaveDelay?: number;
}
const props = withDefaults(defineProps<DialogProps>(), {
    show: false,
    aniType: 'pop-crisp',
    cartoon: false,
    cartoonPos: 'top',
    title: '',
    subTitle: '',
    message: '',
    showClose: true,
    maskCloseable: false,
    flyToTarget: '',
    enterDuration: 400,
    leaveDuration: 200,
    innerScroll: false,
});
const emit = defineEmits<{
    /**
     * 可见状态
     * @arg { boolean } show
     */
    (event: 'update:show', show: boolean): void;
    /**
     * 取消
     */
    (event: 'cancel'): void;
    /**
     * 确认
     */
    (event: 'confirm'): void;
    /**
     * 底部文字尖角按钮点击
     */
    (event: 'bottom-text-btn-click'): void;
    /**
     * 底部文字点击
     */
    (event: 'bottom-message-click'): void;
    /**
     * 进入前
     */
    (event: 'before-enter'): void;
    /**
     * 进入
     */
    (event: 'enter'): void;
    /**
     * 进入后
     */
    (event: 'after-enter'): void;
    /**
     * 离开前
     */
    (event: 'before-leave'): void;
    /**
     * 离开
     */
    (event: 'leave'): void;
    /**
     * 离开后
     */
    (event: 'after-leave'): void;
    /**
     * 关闭按钮点击
     */
    (event: 'close'): void;
    /**
     * 遮罩关闭
     */
    (event: 'mask-close'): void;
}>();

function closePopup() {
    emit('close');

    emit('update:show', false);
}
function maskClosePopup() {
    if (props.maskCloseable) {
        emit('mask-close');
        emit('update:show', false);
    }
}
const slots = useSlots();
const multipleBtns = computed(() => Boolean(props.confirmText) && Boolean(props.cancelText));
const singleRowBtn = computed(() => !multipleBtns.value);
const ctrlDirection = computed(() => (singleRowBtn.value ? 'col-rev' : 'row'));
const ctrlJustify = computed(() => (singleRowBtn.value ? 'start' : 'between'));
const ctrlAlign = computed(() => (singleRowBtn.value ? 'center' : 'stretch'));
const btnClass = computed(() => (singleRowBtn.value ? 'dialog-vertical-btn' : 'dialog-horizontal-btn'));
const btnHeight = computed(() => (multipleBtns.value ? 60 : 66));
const noPlusText = computed(
    () => !Boolean(slots.plus ?? props.bottomMessage ?? props.bottomBtnText) && 'no-bottom-content',
);
const cartoonClass = computed(() => (props.cartoonPos === 'bottom' ? 'dialog-panel-up' : ''));
const hasTopContentClass = computed(() => (slots.header || props.cartoon) && 'has-top-content');

function clickAction(event: 'cancel' | 'confirm' | 'bottomText' | 'bottomMessage') {
    switch (event) {
        case 'cancel':
            emit('cancel');
            break;
        case 'confirm':
            emit('confirm');
            break;
        case 'bottomText':
            emit('bottom-text-btn-click');
            break;
        case 'bottomMessage':
            emit('bottom-message-click');
            break;
        default: {
            event satisfies never;
            emit('cancel');
        }
    }
}
</script>

<template>
    <Popup
        class="dialog-popup"
        :show="show"
        :mask-closeable="maskCloseable"
        :show-close="showClose"
        :inner-scroll="innerScroll"
        :fly-to-target="flyToTarget"
        :ani-type="aniType"
        :enter-duration="enterDuration"
        :leave-duration="leaveDuration"
        :enter-delay="enterDelay"
        :leave-delay="leaveDelay"
        :class="hasTopContentClass"
        position="center"
        @before-enter="emit('before-enter')"
        @enter="emit('enter')"
        @after-enter="emit('after-enter')"
        @leave="emit('leave')"
        @after-leave="emit('after-leave')"
        @close="closePopup"
        @mask-click="maskClosePopup"
    >
        <template #addons>
            <div class="dialog-addons">
                <Logo v-if="customLogo" class="dialog-logo" :src="customLogo" />
                <div class="dialog-placeholder">
                    <slot name="topHeader"></slot>
                    <!-- 只做占位不展示，解决内容区绝对定位没有高度的问题 -->
                    <LayCartoon v-if="cartoon" />
                </div>
                <slot name="addons" />
            </div>
        </template>
        <div class="dialog-cartoon-wrapper">
            <!-- 面板顶部插槽、在趴图上方 -->
            <slot name="header"></slot>
            <LayCartoon v-if="cartoon" />
        </div>
        <div class="dialog-panel" :class="cartoonClass">
            <div class="dialog" :class="noPlusText">
                <Box v-if="title || subTitle" class="dialog-inner-header" direction="col" align="center">
                    <h3 v-if="title" class="dialog-title">{{ title }}</h3>
                    <h4 v-if="subTitle" class="dialog-sub-title">{{ subTitle }}</h4>
                </Box>
                <!-- 用于标题插槽 -->
                <template v-if="$slots.inheader">
                    <slot name="inheader"></slot>
                </template>
                <div class="dialog-main">
                    <div v-if="message" class="dialog-message">{{ message }}</div>
                    <div v-if="$slots.icon || icon" class="dialog-content-icon">
                        <img v-if="icon" :src="icon" />
                        <!-- @slot 弹窗内ICON区域 -->
                        <slot name="icon" />
                    </div>
                    <Box v-if="$slots.default || $slots.coupon" justify="center" class="dialog-content">
                        <!-- @slot 弹窗主内容区域 -->
                        <slot />
                        <!-- @slot 弹窗卡券区域 -->
                        <slot name="coupon" />
                    </Box>
                </div>
                <Box
                    v-if="cancelText || confirmText || $slots.btn"
                    :justify="ctrlJustify"
                    :align="ctrlAlign"
                    :direction="ctrlDirection"
                >
                    <Button
                        v-if="cancelText"
                        type="plain"
                        class="dialog-btn dialog-btn-sub"
                        :class="btnClass"
                        :height="btnHeight"
                        @click="clickAction('cancel')"
                    >
                        {{ cancelText }}
                    </Button>
                    <Button
                        v-if="confirmText"
                        type="primary-linear"
                        class="dialog-btn dialog-btn-main"
                        :class="btnClass"
                        :loading="btnLoading"
                        :disabled="btnDisabled"
                        :height="btnHeight"
                        @click="clickAction('confirm')"
                    >
                        {{ confirmText }}
                        <template v-if="confirmSubText" #plugin>
                            {{ confirmSubText }}
                        </template>
                    </Button>
                    <div v-if="$slots.btn" class="dialog-btn-slot">
                        <!-- @slot 操作按钮区域 -->
                        <slot name="btn"></slot>
                    </div>
                </Box>
                <div v-if="$slots.plus || bottomMessage || bottomBtnText" class="dialog-plus">
                    <!-- @slot 操作按钮下方区域 -->
                    <slot name="plus" />
                    <span v-if="bottomMessage" @click="clickAction('bottomMessage')">{{ bottomMessage }}</span>
                    <span v-if="bottomBtnText" class="dialog-text-btn" @click="clickAction('bottomText')">
                        {{ bottomBtnText }}<IconArr class="dialog-text-btn-arr" />
                    </span>
                </div>
            </div>
        </div>
    </Popup>
</template>
<style>
:root {
    /* 弹窗宽度 */
    --adapt-dialog-width: 304px;
    /* 最小高度 */
    --adapt-dialog-min-height: 200px;
    /* 弹窗面板底层背景 */
    --adapt-dialog-back-background: #fff;
    /* 弹窗面板上层背景 */
    --adapt-dialog-head-background: #fff;
    /* 弹窗圆角 */
    --adapt-dialog-border-radius: 36px;
    /* 弹窗整体字色 */
    --adapt-dialog-color: #000;
    /* 弹窗padding */
    --adapt-dialog-padding: 24px 24px;
    /* 无按钮下文案情况默认底部padding */
    --adapt-dialog-no-bottom-padding-bottom: 46px;
    /* logo与弹窗间距 */
    --adapt-dialog-logo-margin-bottom: 4px;
    /* 主标题字号 */
    --adapt-dialog-title-font-size: 22px;
    /* 主标题行高 */
    --adapt-dialog-title-line-height: 33px;
    /* 主标题字重 */
    --adapt-dialog-title-font-weight: 400;
    /* 主标题字色 */
    --adapt-dialog-title-font-color: #000;
    /* 副标题距离主标题间距 */
    --adapt-dialog-subtitle-margin-top: 4px;
    /* 副标题字号 */
    --adapt-dialog-sub-title-font-size: 14px;
    /* 副标题行高 */
    --adapt-dialog-sub-title-line-height: 20px;
    /* 副标题字重 */
    --adapt-dialog-sub-title-font-weight: 400;
    /* 副标题字色 纯色或rgba */
    --adapt-dialog-sub-title-color: #9c9c9c;
    /* 内容区域距离margin */
    --adapt-dialog-content-margin: 24px auto;
    /* 中部切图高度 */
    --adapt-dialog-icon-height: 148px;

    /* 中部内容区是message的字号 */
    --adapt-dialog-message-font-size: 14px;
    /* 中部内容区是message的行高 */
    --adapt-dialog-message-line-height: 20px;
    /* 中部内容区是message的字重 */
    --adapt-dialog-message-font-weight: 400;
    /* 按钮下方文案字号 */
    --adapt-dialog-plus-font-size: 13px;
    /* 按钮下方文案行高 */
    --adapt-dialog-plus-line-height: 18px;
    /* 按钮下方文案字重 */
    --adapt-dialog-plus-font-weight: 400;
    /* 按钮下方文案字色 */
    --adapt-dialog-plus-color: var(--adapt-dialog-color);
    /* 按钮下方文案透明度 */
    --adapt-dialog-plus-opcity: 0.6;
    /* 按钮下方文案距离按钮margin */
    --adapt-dialog-plus-margin-top: 14px;
    /* 按钮下方文案箭头icon大小 */
    --adapt-dialog-plus-arr-size: 10px;
    /* 按钮下方文案箭头icon距离左边文案 */
    --adapt-dialog-plus-arr-margin-left: 0;

    /* 按钮高度 */
    --adapt-dialog-btn-height: 40px;
    /* 按钮字号 */
    --adapt-dialog-btn-font-size: 16px;
    /* 按钮行高 */
    --adapt-dialog-btn-line-height: 22px;
    /* 按钮字重 */

    /* 双按钮下按钮宽度 */
    --adapt-dialog-multi-btn-width: 60px;
    /* 单按钮下按钮宽度 */
    --adapt-dialog-single-btn-width: 192px;

    /* 主按钮文字颜色 */
    --adapt-dialog-main-button-primary-font-color: #fff;
    /* 主按钮背景色 */
    --adapt-dialog-main-button-primary-background-color: #fe3666;
    /* 主按钮字体 */
    --adapt-dialog-main-button-primary-font-family: unset;
    /* 主按钮字间距 */
    --adapt-dialog-main-button-primary-letter-spacing: 0;
    /* 次级按钮文字颜色 */
    --adapt-dialog-second-button-font-color: #fe3666;
    /* 次级按钮边框颜色 */
    --adapt-dialog-second-button-border-color: #fe3666;
    /* 次级按钮背景色 */
    --adapt-dialog-second-button-background-color: transparent;
    /* 按钮渐变文字颜色 */
    --adapt-dialog-main-button-primary-font-linear: var(--adapt-dialog-main-button-primary-font-color);
    /* cartoon的层级 */
    --adapt-dialog-lay-cartoon-z-index: 1;
}
</style>
<style lang="scss" scoped>
.has-top-content {
    --adapt-popup-close-btn-top-y-value: 26px;
}

.dialog-header {
    max-width: var(--adapt-dialog-width);
    margin: auto;
    :deep(> img) {
        display: block;
        max-width: 100%;
    }
}
.dialog-logo {
    margin-bottom: var(--adapt-dialog-logo-margin-bottom);
}
.dialog-cartoon-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    z-index: var(--adapt-dialog-lay-cartoon-z-index);
    width: 100%;
    transform: translateY(-100%);
}
.dialog-placeholder {
    visibility: hidden;
}
.dialog {
    width: var(--adapt-dialog-width);
    min-height: var(--adapt-dialog-min-height);
    color: var(--adapt-dialog-color);
    box-sizing: border-box;
    padding: var(--adapt-dialog-padding);
    background: var(--adapt-dialog-head-background) no-repeat;
    border-radius: var(--adapt-dialog-border-radius);
    background-size: 100% auto;
    background-position: top;
    &.no-bottom-content {
        padding-bottom: var(--adapt-dialog-no-bottom-padding-bottom);
    }
}
.dialog-title {
    font-size: var(--adapt-dialog-title-font-size);
    line-height: var(--adapt-dialog-title-line-height);
    font-weight: var(--adapt-dialog-title-font-weight);
    /* 主标题字体 */
    font-family: var(--adapt-dialog-title-font-family);
    /* 标题字色 支持渐变色 */
    background: var(--adapt-dialog-title-font-color);
    background-clip: text;
    /* stylelint-disable scale-unlimited/declaration-strict-value */
    -webkit-text-fill-color: transparent;
}
.dialog-sub-title {
    margin-top: var(--adapt-dialog-subtitle-margin-top);
    font-size: var(--adapt-dialog-sub-title-font-size);
    line-height: var(--adapt-dialog-sub-title-line-height);
    font-weight: var(--adapt-dialog-sub-title-font-weight);
    color: var(--adapt-dialog-sub-title-color);
}
.dialog-panel {
    background: var(--adapt-dialog-back-background) no-repeat;
    border-radius: var(--adapt-dialog-border-radius);
    background-size: 100% auto;
    &-up {
        position: relative;
        z-index: 1;
    }
}

.dialog-message {
    text-align: center;
    font-size: var(--adapt-dialog-message-font-size);
    line-height: var(--adapt-dialog-message-line-height);
    white-space: pre-wrap;
    margin: var(--adapt-dialog-content-margin);
}
.dialog-content {
    margin: var(--adapt-dialog-content-margin);
}
.dialog-content-icon {
    height: var(--adapt-dialog-icon-height);
    margin: var(--adapt-dialog-content-margin);
    overflow: hidden;
    :deep(img) {
        height: 100%;
        display: block;
        margin: auto;
    }
    & + .dialog-content {
        margin: var(--adapt-dialog-content-margin);
    }
}
.dialog-btn {
    width: var(--adapt-dialog-multi-btn-width);
}
.dialog-vertical-btn {
    width: var(--adapt-dialog-single-btn-width);
}

.dialog-btn-main {
    --adapt-button-primary-font-color: var(--adapt-dialog-main-button-primary-font-color);
    --adapt-button-primary-background-color: var(--adapt-dialog-main-button-primary-background-color);
    --adapt-button-primary-background-image: var(--adapt-dialog-main-button-primary-background-image);
    --adapt-button-main-font-family: var(--adapt-dialog-main-button-primary-font-family);
    --adapt-button-primary-font-linear: var(--adapt-dialog-main-button-primary-font-linear);
    --adapt-button-primary-letter-spacing: var(--adapt-dialog-main-button-primary-letter-spacing);
}

.dialog-btn-sub {
    --adapt-button-plain-font-color: var(--adapt-dialog-second-button-font-color);
    --adapt-button-plain-background-color: var(--adapt-dialog-second-button-background-color);
    --adapt-button-plain-border-color: var(--adapt-dialog-second-button-border-color);
}

.dialog-plus {
    color: var(--adapt-dialog-plus-color);
    font-size: var(--adapt-dialog-plus-font-size);
    line-height: var(--adapt-dialog-plus-line-height);
    text-align: center;
    margin-top: var(--adapt-dialog-plus-margin-top);
    span {
        opacity: var(--adapt-dialog-plus-opcity);
    }
}
.dialog-text-btn {
    display: inline-flex;
    align-items: center;
    font-weight: var(--adapt-dialog-plus-font-weight);
}
.dialog-text-btn-arr {
    height: var(--adapt-dialog-plus-arr-size);
    width: var(--adapt-dialog-plus-arr-size);
    margin-left: var(--adapt-dialog-plus-arr-margin-left);
    color: currentColor;
}
</style>
