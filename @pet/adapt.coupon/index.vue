<script lang="ts">
export default {
    name: 'AdaptCoupon',
};
</script>

<script setup lang="ts">
import { computed } from 'vue-demi';

import type { ButtonType } from './coupon-ui.vue';
import CouponUI from './coupon-ui.vue';
import { transformCoupon } from './transform';
import type { CouponSize, PlatformCouponData } from './types';

interface Props {
    /**
     * 服务端卡券数据
     */
    info: PlatformCouponData;
    /**
     * 是否整个卡券点击生效
     */
    clickAll?: boolean;
    /**
     * 卡券尺寸
     * @values "large" | "simple" | "mini" | "small" | "medium" | "full"
     */
    size?: CouponSize;
    /**
     * 是否展示立即使用的按钮
     */
    showUseBtn?: boolean;
    /**
     * 按钮类型
     * @values "flat" 实心 | "plain" 空心
     */
    btnType?: ButtonType;
    /**
     * 按钮文案
     */
    btnText?: string;
    /**
     * 按钮状态
     */
    btnDisabled?: boolean;
    /**
     * 按钮加载
     */
    btnLoading?: boolean;
    /**
     * 是否显示背景
     */
    noBkg?: boolean;
    /**
     * 标签
     */
    tag?: string;
    /**
     * 多张
     */
    count?: number;
    /**
     * 传入跳转方法
     */
    jumpFn?: (url: string) => void;
}

const props = withDefaults(defineProps<Props>(), {
    clickAll: false,
    size: 'large',
    showUseBtn: true,
    noBkg: false,
    count: 1,
});
const emit = defineEmits<{
    /**
     * 卡券点击
     * @arg { PlatformCouponData } info
     */
    (event: 'coupon-click', info: PlatformCouponData): void;
}>();
const couponInfo = computed(() => transformCoupon(props.info));
function handleClick() {
    emit('coupon-click', props.info);
    if (props.jumpFn) {
        props?.jumpFn(props.info.forwardUrl);
    }
}
</script>
<template>
    <CouponUI
        :type="couponInfo.type"
        :value-main="couponInfo.mainDesc"
        :value-desc="couponInfo.subDesc"
        :value-image="couponInfo.img"
        :main-title="couponInfo.title"
        :main-icon="couponInfo.icon"
        :main-desc="couponInfo.desc"
        :main-value="couponInfo.value"
        :main-og-value="couponInfo.ogValue"
        :main-attach="couponInfo.sub"
        :status="couponInfo.status"
        :size="size"
        :show-use-btn="showUseBtn"
        :btn-type="btnType"
        :btn-text="btnText ?? couponInfo.btnText"
        :btn-disabled="btnDisabled"
        :btn-loading="btnLoading"
        :no-bkg="noBkg"
        :click-all="clickAll"
        :tag="tag"
        :count="count"
        @coupon-click="handleClick"
    />
</template>
