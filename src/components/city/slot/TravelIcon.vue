<script lang="ts" setup>
import Avatar from '@pet/adapt.avatar/index.vue';
import { MarqueeSlide } from '@pet/adapt.marquee-slide';
import { toast } from '@pet/adapt.toast';

import { useLogger } from '@/init/logger';
import { useNavigate } from '@/models/utils/useNavigate';
import { followUser } from '@/utils/yoda';
const props = defineProps<{
    avatar: string;
    name: string;
    uid: number;
    followed: boolean;
    refresh: boolean;
}>();
const list = computed<
    {
        isDesc: boolean;
        name: string;
    }[]
>(() => [
    { isDesc: false, name: props.name },
    { isDesc: true, name: '立即关注' },
]);
const followedList = computed<
    {
        isDesc: boolean;
        name: string;
    }[]
>(() => [
    {
        isDesc: true,
        name: '已关注',
    },
    { name: props.name, isDesc: false },
]);
const { sendClick } = useLogger();
// 是否关注成功
const followSuccess = ref(false);
// 关注动画结束
const finishFollow = ref(false);
const { jumpToProfile } = useNavigate();
const follow = async () => {
    if (props.followed || followSuccess.value) {
        return;
    }
    const res = await followUser({
        userId: `${props.uid}`,
    });
    // 关注成功
    if (res.followState === 1) {
        followSuccess.value = true;
        toast('关注成功，快去看看ta的作品吧');
    } else {
        toast('系统繁忙，请重新关注');
    }
};
const gotoProfile = () => {
    sendClick('OP_ACTIVITY_COUNTRY_PROFILE_BANNER', {
        user_id: props.uid,
        user_name: props.name,
        click_area: 'head',
    });
    jumpToProfile(props.uid);
};
const marqueeEnd = (index: number) => {
    // 关注成功后动画轮播一次
    if (index === 1) {
        finishFollow.value = true;
        sendClick('OP_ACTIVITY_COUNTRY_PROFILE_BANNER', {
            user_id: props.uid,
            user_name: props.name,
            click_area: 'follow',
        });
    }
};
const reset = () => {
    followSuccess.value = false;
    finishFollow.value = false;
};
watch(
    () => props.refresh,
    (newVal) => {
        if (newVal) {
            reset();
        }
    },
    {
        immediate: true,
    },
);
</script>

<template>
    <div class="main-venue">
        <!-- 头像 -->
        <Avatar :src="avatar" alt="头像" class="avatar-image" default-type="light" @click="gotoProfile" />
        <div class="btn-marquee">
            <MarqueeSlide
                v-if="!followed && !followSuccess"
                :key="`default-${uid}`"
                class="default-marquee"
                :list="list"
                :animation-duration="670"
                :stay-duration="2500"
                :slide-height="24"
                @click="follow"
            >
                <template #item="{ data }"
                    ><div>
                        <div class="follow-btn">
                            <span v-if="data?.isDesc" class="icon"></span><span class="text">{{ data?.name }}</span>
                        </div>
                    </div></template
                >
            </MarqueeSlide>
            <!-- 关注成功执行一次轮播动画，轮播一次消失 -->
            <MarqueeSlide
                v-else-if="followSuccess && !finishFollow"
                :key="`success-${uid}`"
                class="success-marquee"
                :list="followedList"
                :animation-duration="670"
                :stay-duration="2500"
                :slide-height="24"
                @stop="marqueeEnd"
                @click="gotoProfile"
            >
                <template #item="{ data }"
                    ><div>
                        <div class="follow-btn">
                            <span v-if="data?.isDesc" class="icon"></span><span class="text">{{ data?.name }}</span>
                        </div>
                    </div></template
                >
            </MarqueeSlide>
            <!-- 已关注后静态 -->
            <div v-else-if="finishFollow || followed" class="follow-btn" @click="gotoProfile">
                <span class="text">{{ name }}</span>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.main-venue {
    --adapt-marquee-slide-transition-timing-function: cubic-bezier(0.33, 0, 0.67, 0, 1);
    box-sizing: border-box;
    position: absolute;
}

.avatar-image {
    width: 38px;
    height: 38px;
    border-radius: 38px;
    overflow: hidden;
}

.btn-marquee {
    display: flex;
    width: 71px;
    height: 24px;
    margin-top: -10px;
    transform: translateX(calc(-1 * 50% + 19px));
    overflow: hidden;
}
.follow-btn {
    display: flex;
    height: 24px;
    width: 71px;
    font-size: 10px;
    background: url('./assets/wenlv_bg.png') center / 100% 100% no-repeat;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-weight: 600;
    .text {
        max-width: 60px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.default-marquee {
    .icon {
        margin-right: 3px;
        width: 8px;
        height: 8px;
        background: url('./assets/wenlv_add.png') center / 100% 100% no-repeat;
    }
}
.success-marquee {
    .icon {
        margin-right: 3px;
        width: 8px;
        height: 8px;
        background: url('./assets/wenlv_success.png') center / 100% 100% no-repeat;
    }
}
</style>
