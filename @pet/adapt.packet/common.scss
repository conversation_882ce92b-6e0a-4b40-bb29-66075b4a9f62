@mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

@mixin ellipse {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@keyframes pull-down {
    0% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    2.857% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    5.714% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    8.571% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    11.429% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    14.286% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    17.143% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    20% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    22.857% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    25.714% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    28.571% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    31.429% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    34.286% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    37.143% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    40% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    42.857% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    45.714% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    48.571% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    51.429% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    54.286% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    57.143% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    60% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    62.857% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    65.714% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    68.571% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    71.429% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    74.286% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    77.143% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, -60, 0, 1);
    }
    80% {
        opacity: 0.321609178613013;
        transform: matrix3d(0.475, 0, 0, 0, 0, 0.475, 0, 0, 0, 0, 1, 0, 0, -52.5, 0, 1);
    }
    82.857% {
        opacity: 0.561125;
        transform: matrix3d(0.65, 0, 0, 0, 0, 0.65, 0, 0, 0, 0, 1, 0, 0, -45, 0, 1);
    }
    85.714% {
        opacity: 0.779650534424428;
        transform: matrix3d(0.825, 0, 0, 0, 0, 0.825, 0, 0, 0, 0, 1, 0, 0, -37.5, 0, 1);
    }
    88.571% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -30, 0, 1);
    }
    91.429% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -22.5, 0, 1);
    }
    94.286% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -15, 0, 1);
    }
    97.143% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -7.5, 0, 1);
    }
    100% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}

@keyframes show-close-btn {
    0% {
        opacity: 0;
    }
    84% {
        opacity: 0;
    }
    96% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

@keyframes flyout {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes show-package {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    30% {
        opacity: 0;
        transform: scale(0);
    }
    65% {
        opacity: 0.65;
        transform: scale(1);
    }
    82% {
        opacity: 0.82;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes btn-shake {
    0% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    4.348% {
        opacity: 1;
        transform: matrix3d(1.04, 0, 0, 0, 0, 0.96, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    8.696% {
        opacity: 1;
        transform: matrix3d(1.078, 0, 0, 0, 0, 0.922, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    13.043% {
        opacity: 1;
        transform: matrix3d(1.1, 0, 0, 0, 0, 0.9, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    17.391% {
        opacity: 1;
        transform: matrix3d(1.038, 0, 0, 0, 0, 0.962, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    21.739% {
        opacity: 1;
        transform: matrix3d(0.961, 0, 0, 0, 0, 1.039, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    26.087% {
        opacity: 1;
        transform: matrix3d(0.9, 0, 0, 0, 0, 1.1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    30.435% {
        opacity: 1;
        transform: matrix3d(0.913, 0, 0, 0, 0, 1.087, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    34.783% {
        opacity: 1;
        transform: matrix3d(0.94, 0, 0, 0, 0, 1.06, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    39.13% {
        opacity: 1;
        transform: matrix3d(0.97, 0, 0, 0, 0, 1.03, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    43.478% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    47.826% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    52.174% {
        opacity: 1;
        transform: matrix3d(0.999, 0.035, 0, 0, -0.035, 0.999, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    56.522% {
        opacity: 1;
        transform: matrix3d(0.998, 0.07, 0, 0, -0.07, 0.998, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    60.87% {
        opacity: 1;
        transform: matrix3d(0.995, 0.105, 0, 0, -0.105, 0.995, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    65.217% {
        opacity: 1;
        transform: matrix3d(0.999, 0.052, 0, 0, -0.052, 0.999, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    69.565% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    73.913% {
        opacity: 1;
        transform: matrix3d(0.999, -0.052, 0, 0, 0.052, 0.999, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    78.261% {
        opacity: 1;
        transform: matrix3d(0.995, -0.105, 0, 0, 0.105, 0.995, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    82.609% {
        opacity: 1;
        transform: matrix3d(0.996, -0.084, 0, 0, 0.084, 0.996, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    86.957% {
        opacity: 1;
        transform: matrix3d(0.998, -0.063, 0, 0, 0.063, 0.998, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    91.304% {
        opacity: 1;
        transform: matrix3d(0.999, -0.042, 0, 0, 0.042, 0.999, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    95.652% {
        opacity: 1;
        transform: matrix3d(1, -0.021, 0, 0, 0.021, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
    100% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}
