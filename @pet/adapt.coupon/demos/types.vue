<pet-info lang="json">
{ "title": "卡券类型", "description": "所有卡券的类型", "priority": 10 }
</pet-info>
<script setup lang="ts">
import Coupon from '../index.vue';
import type { PlatformCouponData } from '../types';

const displayTypeNumber: PlatformCouponData = {
    displayType: 'number',
    branding: false,
    name: '日常测试',
    icon: '',
    desc: '全平台可用（除少数商品外）',
    forwardUrl:
        'https://app.kwaixiaodian.com/page/kwaishop-pub-couponuse/v20211111?couponTargetName=平台通用券&layoutType=4&enableWK=1&__launch_options__=%7B%22enableLoading%22%3Atrue%2C%22enableProcess%22%3Afalse%7D&breakupTime=0&webview_bgcolor=%23f80031&breakupTime=0&hyId=couponus&entry_src=cnycoupongezi&source=cnycoupongezi&couponId=6201481043&couponTargetType=3&doorPrice=1000&reducePrice=100&couponStartTime=1660201848196&couponEndTime=1660205448196&couponSourceType=1',
    forwardText: '立即使用',
    status: 'unused',
    value: 990,
    valueText: '1',
    valueDesc: '满10元可用',
    expiredTime: 1660205448196,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
    count: 2,
};

const displayTypeDiscount: PlatformCouponData = {
    displayType: 'discount',
    branding: false,
    name: '优惠券',
    icon: 'https://kcdn.staging.kuaishou.com/kos/nlav100106/encourage-1667278331449-lgzXvj.png',
    desc: '仅限新客使用',
    forwardUrl: 'https://www.baidu.com/zzzzzzzzzz',
    forwardText: '立即使用',
    status: 'unused',
    value: 5.0,
    valueText: '5.0',
    valueDesc: '无门槛',
    expiredTime: 1661928815415,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeImage: PlatformCouponData = {
    displayType: 'image',
    branding: false,
    name: '直播勋章',
    desc: '五一活动直播勋章',
    forwardUrl:
        'kwai://liveaggregatesquare?liveSquareSource=1&sourceType=269&liveSource=HOMEPAGE_51_ACTIVITY_PAGE_2&internaljump=kwailive://giftpanel?tab=PropsPanel&sourceType=h5',
    forwardText: '去佩戴',
    status: 'unused',
    value: 0.0,
    image: 'http://blobstore-nginx.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1659064164269-oifvCN.png',
    expiredTime: 1665648351770,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeText: PlatformCouponData = {
    displayType: 'text',
    branding: false,
    name: '优惠券',
    icon: 'https://kcdn.staging.kuaishou.com/kos/nlav100106/encourage-1670845410832-wESpAq.png',
    desc: '仅限电商新客首单使用',
    forwardUrl: 'https://www.baidu.com/zzzzzzzzzz',
    forwardText: '立即使用',
    status: 'unused',
    value: 0.0,
    valueText: '首单免减',
    valueDesc: '满100可用',
    expiredTime: 1661928815415,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeSpecial: PlatformCouponData = {
    displayType: 'special',
    branding: false,
    name: '特价商品',
    desc: '',
    forwardUrl:
        'kwai://liveaggregatesquare?liveSquareSource=1&sourceType=269&liveSource=HOMEPAGE_51_ACTIVITY_PAGE_2&internaljump=kwailive://giftpanel?tab=PropsPanel&sourceType=h5',
    forwardText: '去购买',
    status: 'unused',
    value: 100,
    originalValue: 60,
    image: 'http://blobstore-nginx.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1659064164269-oifvCN.png',
    expiredTime: 1665648351770,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

function handleClick(info: PlatformCouponData) {
    console.log('coupon-click');
    console.info(info);
}
</script>

<template>
    <div class="wrapper">
        <div class="demo">
            <b>数字</b>
            <Coupon :info="displayTypeNumber" @coupon-click="handleClick"></Coupon>
            <b>折扣</b>
            <Coupon :info="displayTypeDiscount" @coupon-click="handleClick"></Coupon>
            <b>图片</b>
            <Coupon :info="displayTypeImage" @coupon-click="handleClick"></Coupon>
            <b>文字</b>
            <Coupon :info="displayTypeText" @coupon-click="handleClick"></Coupon>
            <b>特价商品</b>
            <Coupon :info="displayTypeSpecial" @coupon-click="handleClick"></Coupon>
            <b>券合并 多张</b>
            <Coupon :info="displayTypeNumber" :count="displayTypeNumber.count" @coupon-click="handleClick"></Coupon>
        </div>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    width: 100%;
    justify-content: center;
    background: #f5f5f5;
}

.demo {
    padding: 10px 0;
    b {
        display: block;
        margin: 10px 0;
    }
}
</style>
