<script lang="ts">
export default {
    name: '<PERSON>pt<PERSON>abelA<PERSON>',
};
</script>

<script setup lang="ts"></script>

<template>
    <svg class="arrow" width="6" height="9" viewBox="0 0 6 9" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M0.450413 0.489659C0.732269 0.186129 1.20682 0.168558 1.51035 0.450413L5.01035 3.70048C5.16317 3.84239 5.25 4.04152 5.25 4.25007C5.25 4.45862 5.16317 4.65775 5.01035 4.79966L1.51049 8.04959C1.20696 8.33145 0.732414 8.31388 0.450559 8.01035C0.168704 7.70682 0.186274 7.23227 0.489805 6.95041L3.39781 4.25007L0.489659 1.54959C0.186129 1.26774 0.168558 0.793189 0.450413 0.489659Z"
            fill="currentColor"
        />
    </svg>
</template>

<style>
:root {
    /* 箭头颜色 */
    --adapt-label-arrow-color: currentColor;
}
</style>

<style lang="scss" scoped>
.arrow {
    width: var(--adapt-label-arrow-width);
    height: var(--adapt-label-arrow-height);
    path {
        fill: var(--adapt-label-arrow-color);
    }
}
</style>
