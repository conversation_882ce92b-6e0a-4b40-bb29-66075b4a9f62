<pet-info lang="json">
{ "title": "自定义样式", "description": "使用variable重新定义样式", "priority": 2 }
</pet-info>
<script setup lang="ts">
import { toast } from '@pet/adapt.toast';
import AdaptDialog from '../index.vue';
import Heading from '@pet/adapt.heading/index.vue';
import Button from '@pet/adapt.button/index.vue';

function handleConfirm() {
    toast('触发 confirm 事件');
}

function handleBottomClick() {
    toast('触发 bottom-text-btn-click 事件');
}
function handleClose() {
    toast('触发 close 事件');
}
const titles = ['签到成功', '恭喜获得红包奖励'];
</script>

<template>
    <div class="demo-wrapper">
        <AdaptDialog
            class="static dialog-all demo-dialog"
            show
            sub-title="此处为副标题"
            bottom-message="已存入「我的钱包」"
            bottom-btn-text="去查看"
            cartoon
            cartoon-pos="bottom"
            @confirm="handleConfirm"
            @close="handleClose"
            @bottom-text-btn-click="handleBottomClick"
        >
            <template #header>
                <Heading class="header-title" :title="titles"></Heading>
            </template>
            <div class="demo-content">
                <span class="number">1.88<span class="unit">元</span></span>
            </div>
            <template #btn><Button :height="66">继续领现金 (3)</Button></template>
        </AdaptDialog>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.dialog-all {
    --adapt-dialog-background: url('../assets/imgs/play-dialog-bg.png');
    --adapt-dialog-padding: 42px 32px;
    --adapt-dialog-sub-title-color: #fe3666;
    --adapt-dialog-sub-title-font-size: 16px;
    --adapt-dialog-sub-title-line-height: 22px;
    --adapt-dialog-sub-title-font-weight: 600;
    --adapt-lay-cartoon: url('../assets/imgs/play-cartoon.png');
    --adapt-lay-cartoon-background: none;
    --adapt-lay-cartoon-height: 224px;
    --adapt-lay-cartoon-margin-bottom: 135px;
    --adapt-dialog-plus-opcity: 0.8;
    --adapt-dialog-plus-font-weight: 500;
    --adapt-button-primary-background-image: url('../assets/imgs/play-dialog-btn.png');
    --adapt-button-primary-background-color: none;
}
.demo-content {
    .number {
        position: relative;
        font-size: 46px;
        line-height: 50px;
        font-weight: 400;
        font-family: kuaiyuanhuiti, sans-serif;
        color: #fe3666;
        .unit {
            position: absolute;
            right: -16px;
            bottom: 10px;
            font-size: 14px;
            line-height: 22px;
        }
    }
}

.header-title {
    margin-bottom: 12px;
}
</style>
