<script setup lang="ts">
/**
 * pet组件使用示例。带动效平台导出尺寸，可直接使用
 * 动效预览：https://vision.corp.kuaishou.com/effect/8713
*
 * 依赖pet组件 effect-item@^0.4.11
 * 更多组件使用方式、功能见：
 * https://pet-web.corp.kuaishou.com/gallery/detail/?name=@pet/vision.effect-item&version=
 */
import EffectItem from '@pet/vision.effect-item/index.vue';
import { preload } from '@vision/runtime';

const emit = defineEmits<{
    (e: 'ended'): void;
}>();
const props = defineProps<{
	avatarUrl: string;
    location: string;
    play: boolean;
}>();

// 如果需要依赖动效播放结束ended 事件触发其他逻辑，强烈建议修改effectDuration和在下面添加 :race-end="effectDuration" 保证ended触发。详细见pet组件描述
// const effectDuration = 2000;

// 默认使用dynamic import传入，或可以改成其他promise作异步加载
const effectData = import('./config').then((mod) => mod.default);

// 使用import同步加载动效数据。⚠️ pet组件<0.4.9 只支持这种方式使用
// import effectData from './config';

// 使用async function控制加载时机
// const effectData = async () => { /* do something */ return import('./config').then((mod) => mod.default); }

// 如果遇到动效资源加载慢的问题，可以使用preload 进行资源预加载。注意预加载逻辑可能会占用网络带宽
// preload(effectData);

const effectRef = ref<any>(null);
defineExpose({
    goToAndStop: (frame: number) => {
        effectRef.value?.goToAndStop(frame)
    },
    playAction : (frame: number) => {
        effectRef.value?.playAction(frame)
    }
});

</script>

<template>
    <EffectItem
        ref="effectRef"
        class="effect-wrapper"
        :data="effectData"
        :loop="false"
        :replace-data="{
            avatarUrl,
            smAvatarUrl: avatarUrl,
        }"
        :play="play"
        :autoplay="false"
        @ended="emit('ended')"
    >
        <template #location>
            <!-- DOM placeholder -->
            <div style="font-size: 26px; color: #000; font-weight: 600;">{{ location }}</div>
        </template>
</EffectItem>
</template>

<style scoped>
.effect-wrapper {
    position: relative;
    width: 125px;
    height: 115px;
    overflow: hidden;
}
</style>
