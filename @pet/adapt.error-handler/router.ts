import type { App } from 'vue-demi';
import { createRouter, createWebHashHistory } from 'vue-router';

import ErrorPage from './error-page.vue';
import { ErrorPageName } from './index';

export const RouterName = {
    Home: 'home',
};

export const router = createRouter({
    history: createWebHashHistory(),
    routes: [
        {
            path: '/',
            name: RouterName.Home,

            component: () => import('./about.vue'),
        },
        {
            path: '/error',
            name: ErrorPageName,
            component: ErrorPage,
        },
    ],
});

export const install = (app: App) => {
    app.use(router);
    // Start the Mock Service Worker
};
