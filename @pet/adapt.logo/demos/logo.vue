<script setup lang="ts">
import { ref } from 'vue-demi';

import Logo from '../index.vue';

const logoSrc =
    'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-02-15/1676454395080.b22f5e2752b1bb23.png';
const customLogo = ref(logoSrc);

function toggleLogo() {
    customLogo.value = customLogo.value.length > 0 ? '' : logoSrc;
}
</script>

<template>
    <div>
        <div>
            <label>Logo地址：<input v-model="customLogo" type="text" /></label>
            <button @click="toggleLogo">切换</button>
        </div>
        <div class="logo-box">
            <Logo :src="customLogo" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.logo-box {
    background: rgba(0, 0, 0, 0.8);
}
label {
    display: inline-block;
    margin-bottom: 10px;
}
button {
    margin-left: 10px;
}

div {
    font-size: 16px;
}
</style>
