<pet-info lang="json">
{
    "title": "纵向对齐",
    "description": "纵向滚动对齐的位置",
    "priority": 99
}
</pet-info>
<script setup lang="ts">
import { MarqueeSlide } from '../index';

const messageList = ['获得1000枚金币', '获得2000积分', '获得100元'];
</script>

<template>
    <div class="container">
        <h5>居中</h5>
        <MarqueeSlide :list="messageList" class="marquee-box" position="center">
            <template #item="{ data, index }">
                <div class="row">
                    <div class="line">
                        <b>index: {{ index }}</b> data: {{ data }}
                    </div>
                </div>
            </template>
        </MarqueeSlide>
        <h5>居右</h5>
        <MarqueeSlide :list="messageList" class="marquee-box" position="end">
            <template #item="{ data, index }">
                <div class="row">
                    <div class="line">
                        <b>index: {{ index }}</b> data: {{ data }}
                    </div>
                </div>
            </template>
        </MarqueeSlide>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.container {
    h5 {
        font-size: 16px;
    }
}
.marquee-box {
    font-size: 14px;
    height: 20px;
    margin-bottom: 10px;
}
</style>
