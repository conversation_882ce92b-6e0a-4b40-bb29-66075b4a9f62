<script setup lang="ts">
import { computed } from 'vue-demi';
import type { TitleContext } from '../type';
import AvatarGroup from '@pet/adapt.avatar/avatar-group.vue';

interface Props {
    info?: TitleContext;
}

const props = defineProps<Props>();

const assistResult = computed(() => {
    const assistUserList = props.info?.userList;
    if (assistUserList?.length) {
        return {
            length: assistUserList.length,
            avatarList: assistUserList.slice(0, 3).map((item) => item.avatar),
            firstUserName: assistUserList[0]?.name,
        };
    }
    return {};
});
</script>

<template>
    <div v-if="info?.desc" class="assist-info">
        <template v-if="info?.userList && info.userList.length">
            <AvatarGroup
                class="avatar"
                :width="18"
                :gap="13"
                :srcs="info.userList!.slice(0, 3).map((item) => item.avatar)"
            />
            <span class="name">{{ assistResult.firstUserName }}</span>
            <span v-if="assistResult.length && assistResult.length > 1"> 等{{ assistResult.length }}人</span>
        </template>
        <span>{{ info?.desc }}</span>
    </div>
</template>

<style lang="scss" scoped>
.assist-info {
    display: flex;
    justify-content: center;
    height: 20px;
    width: 100%;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: rgba(255, 242, 219, 0.8);
    .avatar {
        margin-right: 4px;
        --adapt-avatar-border-width: 0.5px;
        --adapt-avatar-border-color: #ffecc5;
    }

    > span {
        height: 20px;
        line-height: 20px;
        display: inline-block;
    }

    .name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 60px;
        margin-right: 2px;
    }
}
</style>
