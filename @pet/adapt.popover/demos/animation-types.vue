<pet-info lang="json">
{ "title": "动效类型", "description": "提供三种弹出动效" }
</pet-info>

<script setup lang="ts">
import type { ArrDirection, ArrAlign } from '@pet/adapt.tool-tip/types';
import { ref, onMounted } from 'vue-demi';

import Popover from '../index.vue';
import type { AnimationType } from '../types';
const show = ref(false);
function handleShow() {
    show.value = !show.value;
}

const arrDirection: ArrDirection = 'bottom';
const arrAlign: ArrAlign = 'center';
const arrDistance = ref(-1);
const bgColor = ref(JSON.stringify({ r: 255, g: 255, b: 255, a: 1 }));
const selectColor = ref('');
const color = ref('#000');
const animationType: AnimationType = 'scale-in-out-debounce';
const animationTypes = ['scale-in-out', 'fade', 'scale-in-out-debounce'];
const scriptEl = document.createElement('script');
scriptEl.type = 'module';
scriptEl.src = 'https://unpkg.com/vanilla-colorful/rgba-color-picker?module';
document.head.appendChild(scriptEl);

const scriptLoaded = new Promise((resolve) => {
    scriptEl.onload = resolve;
});

async function getColor() {
    await scriptLoaded;
    const picker = document.querySelector('rgba-color-picker');
    picker?.addEventListener('color-changed', (event) => {
        // get updated color value
        console.log(event);
        // @ts-expect-error
        const red = event?.detail?.value?.r ?? 255;
        // @ts-expect-error
        const green = event?.detail?.value?.g ?? 255;
        // @ts-expect-error
        const blue = event?.detail?.value?.b ?? 255;
        // @ts-expect-error
        const alpha = event?.detail?.value?.a ?? 1;

        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
        selectColor.value = `rgba(${red}, ${green}, ${blue}, ${alpha})`;
    });
}

onMounted(async () => {
    await getColor();
});
</script>

<template>
    <div class="demo">
        <div class="popover-wrapper">
            popover: {{ show }}
            <Popover
                :show="show"
                :animation-type="animationType"
                :arr-direction="arrDirection"
                :arr-align="arrAlign"
                :arr-distance="+arrDistance"
                :text-color="color"
                :background="selectColor"
                class="popover-main"
                @close="handleShow"
            >
                显示文案内容
            </Popover>
            <div>
                <div class="color-picker">
                    色盘调整popover背景色

                    <!-- <rgba-color-picker :color="bgColor" /> -->
                </div>

                <button @click="handleShow">{{ show ? '隐藏' : '显示' }}</button>
            </div>
        </div>

        <section>
            <h5>animation-type: {{ animationType }}</h5>
            <label v-for="type in animationTypes" :key="type">
                <input v-model="animationType" type="radio" name="aniType" :value="type" />{{ type }}
            </label>
        </section>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
    font-size: 14px;
    padding-top: 100px;
}
label {
    display: inline-flex;
    justify-content: center;
}
.popover-wrapper {
    position: relative;
}
.popover-main {
    left: 206px;
    top: -40px;
    white-space: nowrap;
}

.color-picker {
    width: 200px;
    height: 230px;
    margin: 20px auto 0;
}
</style>
