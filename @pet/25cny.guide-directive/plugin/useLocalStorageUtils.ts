import type { RawActivityStore } from '@pet/yau.yoda/local-storage';
import { computed, ref, type ComputedRef } from 'vue';

function getNextDayTimestamp(days = 1) {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() + 24 * 3600 * 1000 * days;
}

const useLocalStorageUtils = <K extends string>(asyncLocalStore: Pick<RawActivityStore, 'get' | 'set'>) => {
    async function getLocalStorageItem<T extends Record<string, any> | string | boolean>(key: K, initialValue: T) {
        const storedValue = await asyncLocalStore.get(key).catch(() => null);

        if (storedValue) {
            return storedValue as T;
        } else {
            asyncLocalStore.set(key, initialValue);
            return initialValue;
        }
    }

    /** 控制多少天后的0点过期，过期后会返回初始值 */
    async function useLocalStorageExpireDaily<T>(key: K, initialValue: T, afterDays = 1) {
        const item = ref(
            await getLocalStorageItem<{ expired: number; value: T }>(key, {
                expired: getNextDayTimestamp(afterDays),
                value: initialValue,
            }),
        );

        return computed({
            get: () => {
                const now = Date.now();
                if (now > item.value?.expired) {
                    item.value = { expired: getNextDayTimestamp(afterDays), value: initialValue };
                    asyncLocalStore.set(key, item.value);
                }
                return item.value.value;
            },
            set: (value) => {
                item.value = { expired: getNextDayTimestamp(afterDays), value };
                asyncLocalStore.set(key, item.value);
            },
        });
    }

    /** 控制频率 每天只能用多少次, 总共能用多少次 */
    const useFrequencyLimit = async (key: K, limit: ComputedRef<number>, totalLimit?: ComputedRef<number>) => {
        const item = ref(
            await getLocalStorageItem<{ count: number; totalCount: number; date: string | undefined }>(key, {
                count: 0,
                date: undefined,
                totalCount: 0,
            }),
        );

        const today = new Date().toISOString().split('T')[0];
        const isOverLimit = computed(
            () =>
                ((item.value.date === today || !item.value.date) && item.value.count >= limit.value) ||
                (typeof totalLimit !== 'undefined' && item.value.totalCount >= totalLimit.value),
        );

        const recordOnce = () => {
            if (typeof totalLimit !== 'undefined' && item.value.totalCount >= totalLimit.value) {
                console.log('超过总频次限制', totalLimit, item.value.totalCount);
                return;
            }
            if (item.value.date === today) {
                if (item.value.count >= limit.value) {
                    console.log('超过每日限制');
                } else {
                    item.value = { count: ++item.value.count, date: today, totalCount: ++item.value.totalCount };
                    asyncLocalStore.set(key, item.value);
                }
                return;
            }
            item.value = { count: 1, date: today, totalCount: ++item.value.totalCount };
            asyncLocalStore.set(key, item.value);
        };

        return {
            isOverLimit,
            recordOnce,
        };
    };

    async function checkGuideLimit(key: K, limit: number, totalLimit?: number): Promise<boolean> {
        const storedValue = await getLocalStorageItem<{ count: number; totalCount: number; date: string | undefined }>(
            key,
            {
                count: 0,
                date: undefined,
                totalCount: 0,
            },
        );
        const defaultValue = { count: 0, date: undefined, totalCount: 0 };
        const item = storedValue || defaultValue;

        const today = new Date().toISOString().split('T')[0];
        return (
            ((item.date === today || !item.date) && item.count >= limit) ||
            (typeof totalLimit !== 'undefined' && item.totalCount >= totalLimit) ||
            (storedValue?.date === today && storedValue.count >= limit)
        );
    }

    async function recordGuideOnce(key: K, limit: number, totalLimit?: number): Promise<void> {
        const storedValue = await getLocalStorageItem<{ count: number; totalCount: number; date: string | undefined }>(
            key,
            {
                count: 0,
                date: undefined,
                totalCount: 0,
            },
        );
        const defaultValue = { count: 0, date: undefined, totalCount: 0 };
        const item = storedValue || defaultValue;
        const today = new Date().toISOString().split('T')[0];

        if (typeof totalLimit !== 'undefined' && item.totalCount >= totalLimit) {
            console.log('超过总频次限制', totalLimit, item.totalCount);
            return;
        }

        if (item.date === today) {
            if (item.count >= limit) {
                console.log('超过每日限制');
            } else {
                const newValue = {
                    count: item.count + 1,
                    date: today,
                    totalCount: item.totalCount + 1,
                };
                await asyncLocalStore.set(key, newValue);
            }
        } else {
            const newValue = { count: 1, date: today, totalCount: item.totalCount + 1 };
            await asyncLocalStore.set(key, newValue);
        }
    }

    return {
        getLocalStorageItem,
        useLocalStorageExpireDaily,
        useFrequencyLimit,
        checkGuideLimit,
        recordGuideOnce,
    };
};

export default useLocalStorageUtils;
