<script lang="ts">
export default {
    name: 'AdaptLayer',
};
</script>
<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import Heading from '@pet/adapt.heading/index.vue';
import Logo from '@pet/adapt.logo/index.vue';
import AdaptTransition from '@pet/adapt.transition/index.vue';
import type { ClosePosType, PilotInfo } from '@pet/adapt.popup/types';
import type { AdaptTransitionName } from '@pet/adapt.transition/types';
import { computed, ref, type Ref } from 'vue-demi';
import Popup from '@pet/adapt.popup/index.vue';
import type { ButtonHeight } from '@pet/adapt.button/types';

interface Props {
    /**
     * 是否显示
     */
    show?: boolean;
    /**
     * 动画类型
     */
    aniType?: AdaptTransitionName;
    /**
     * 标题
     */
    title?: string | [string, string];
    /**
     * 副标题
     */
    subTitle?: string;
    /**
     * 中间图片
     */
    cover?: string;
    /**
     * 是否有logo
     */
    hasLogo?: boolean;
    /**
     * 自定义logo图片
     */
    customLogo?: string;

    /**
     * 是否隐藏遮罩
     */
    hideMask?: boolean;
    /**
     * 遮罩可否关闭
     */
    maskCloseable?: boolean;
    /**
     * 显示关闭
     */
    showClose?: boolean;
    /**
     * 关闭按钮位置
     */
    closePos?: ClosePosType;
    /**
     * 显示主按钮
     */
    showMainBtn?: boolean;
    /**
     * 主按钮文案
     */
    mainBtnText?: string;
    /**
     * 主按钮解释文案（plugin）
     */
    mainBtnTextDesc?: string;
    /**
     * 主按钮禁用
     */
    mainBtnDisabled?: boolean;
    /**
     * 主按钮加载中
     */
    mainBtnLoading?: boolean;
    /**
     * 主按钮不可用但响应点击
     */
    mainBtnLikeDisabled?: boolean;
    /**
     * 显示次级按钮
     */
    showSecondBtn?: boolean;
    /**
     * 次级按钮文案
     */
    secondBtnText?: string;
    /**
     * 主按钮下文字
     */
    bottomText?: string;
    /**
     * 弹层内滚动
     */
    innerScroll?: boolean;
    /**
     * 是否执行自动飞入
     */
    autoFly?: boolean;
    /**
     * 关闭飞入DomID
     */
    flyToTarget?: string;
    /**
     *  飞入参数控制
     */
    pilotCtrl?: PilotInfo;
    /**
     * 最小飞离时长
     */
    minFlyDuration?: number;
    /**
     * 主要内容入场延迟时间
     */
    enterDelay?: number;
    /**
     * 主要内容入场持续时间
     */
    enterDuration?: number;
    /**
     * 非主要内容入场延迟时间
     */
    addonEnterDelay?: number;
    /**
     * 非主要内容入场持续时间
     */
    addonEnterDuration?: number;
    /**
     * 主要内容离场延迟时间（非主要内容复用）
     */
    leaveDelay?: number;
    /**
     * 主要内容离场持续时间（非主要内容复用）
     */
    leaveDuration?: number;
    /**
     * 遮罩入场延迟时间
     */
    maskEnterDelay?: number;
    /**
     * 遮罩入场持续时间
     */
    maskEnterDuration?: number;
    /**
     * 遮罩离场延迟时间
     */
    maskLeaveDealy?: number;
    /**
     * 遮罩离场持续时间
     */
    maskLeaveDuration?: number;
    /**
     * 中间主要描述
     */
    infoMain?: number | string;
    /**
     * 中间次要描述
     */
    infoSub?: string;
    /**
     * logo文案
     */
    brandLogoDegradeText?: string;
    /**
     * 标题按钮延迟入场时间
     */
    headerTransDelay?: number;
    /**
     * 标题按钮出现持续时间
     */
    headerTransDuration?: number;
    /**
     * 标题按钮延迟入场时间
     */
    footerTransDelay?: number;
    /**
     * 标题按钮出现持续时间
     */
    footerTransDuration?: number;
}

const props = withDefaults(defineProps<Props>(), {
    show: false,
    aniType: 'pop-25cny',
    title: '',
    subTitle: '',
    cover: '',
    hasLogo: false,
    customLogo: '',
    hideMask: false,
    maskCloseable: false,
    showClose: true,
    closePos: 'top',
    showMainBtn: true,
    mainBtnText: '愉快收下',
    mainBtnTextDesc: '',
    mainBtnDisabled: false,
    mainBtnLoading: false,
    mainBtnLikeDisabled: false,
    showSecondBtn: false,
    sencondBtnText: '',
    bottomText: '',
    innerScroll: false,
    autoFly: true,
    flyToTarget: '',
    minFlyDuration: 360,
    enableBgLight: false,
    enterDelay: 0,
    enterDuration: 533,
    addonEnterDelay: 0,
    addonEnterDuration: 167,
    leaveDelay: 0,
    leaveDuration: 233,
    maskEnterDelay: 0,
    maskEnterDuration: 233,
    maskLeaveDealy: 67,
    maskLeaveDuration: 233,
    lightType: 'normal',
    headerTransDelay: 200,
    headerTransDuration: 133,
    footerTransDelay: 200,
    footerTransDuration: 133,
});

const emit = defineEmits<{
    /**
     * 更新show状态
     * @arg { boolean } show
     */
    (event: 'update:show', show: boolean): void;
    /**
     * 关闭按钮
     */
    (event: 'close'): void;
    /**
     * 遮罩点击
     */
    (event: 'mask-click'): void;
    /**
     * 主按钮点击
     */
    (event: 'confirm'): void;
    /**
     * 次按钮点击
     */
    (event: 'second-click'): void;
    /**
     * 下方文字点击
     */
    (event: 'bottom-confirm'): void;
    /**
     * 入场前
     */
    (event: 'before-enter'): void;
    /**
     * 入场
     */
    (event: 'enter'): void;
    /**
     * 入场后
     */
    (event: 'after-enter'): void;
    /**
     * 出场前
     */
    (event: 'before-leave'): void;
    /**
     * 出场
     */
    (event: 'leave'): void;
    /**
     * 出场后
     */
    (event: 'after-leave'): void;
}>();

const multiButton = computed(() => Boolean(props.showSecondBtn || props.secondBtnText));
const buttonHeight = computed(() => (multiButton.value ? 66 : 66) as ButtonHeight);
const multiBtnClass = computed(() => (multiButton.value ? 'multi-btn' : 'single-btn'));
const infoMainNum = computed(() => typeof props.infoMain === 'number');
const infoMainText = computed(() => (infoMainNum.value ? `+${props.infoMain!}` : props.infoMain));
const infoMainTextClass = computed(() => (infoMainNum.value ? 'is-text-num' : 'is-text-string'));
const headerTransInfo = computed(() => ({
    name: 'fade' as const,
    delay: {
        enter: props.headerTransDelay,
    },
    duration: {
        enter: props.headerTransDuration,
    },
}));
const footerTransInfo = computed(() => ({
    name: 'fade' as const,
    delay: {
        enter: props.footerTransDelay,
    },
    duration: {
        enter: props.footerTransDuration,
    },
}));

const popupRef = ref<InstanceType<typeof Popup>>();

function close() {
    // eslint-disable-next-line sonarjs/no-duplicate-string
    emit('update:show', false);
    emit('close');
}

function clickMain() {
    emit('confirm');
    if (!props.mainBtnLikeDisabled) {
        emit('update:show', false);
    }
}

function clickBottomText() {
    emit('update:show', false);
    emit('bottom-confirm');
}

function clickSecond() {
    emit('update:show', false);
    emit('second-click');
}

function boot() {
    popupRef.value?.boot();
}

defineExpose({
    /**
     * 控制飞入效果
     */
    boot,
});

const layerHeaderRef = ref<HTMLElement | null>(null);
const layerFooterRef = ref<HTMLElement | null>(null);

// 如果有飞入需求的时候获取组件高度
const getComponentHeight = (elRef: Ref<HTMLElement | null>) => {
    if (elRef.value && Boolean(props.flyToTarget)) {
        return {
            height: `${elRef.value.getBoundingClientRect().height}px`,
        };
    }
    return null;
};
// 这里是为了方式执行飞入动画时，高度为0，然后造成飞入动画抖动
const headerHeight = computed(() => getComponentHeight(layerHeaderRef));
const footerHeight = computed(() => getComponentHeight(layerFooterRef));
</script>

<template>
    <Popup
        ref="popupRef"
        class="layer"
        :show="show"
        position="center"
        :hide-mask="hideMask"
        :mask-closeable="maskCloseable"
        :show-close="showClose"
        :close-pos="closePos"
        :inner-scroll="innerScroll"
        :fly-to-target="flyToTarget"
        :auto-fly="autoFly"
        :min-fly-duration="minFlyDuration"
        :pilot-ctrl="pilotCtrl"
        :ani-type="aniType"
        :enter-delay="enterDelay"
        :enter-duration="enterDuration"
        :addon-enter-delay="addonEnterDelay"
        :addon-enter-duration="addonEnterDuration"
        :leave-delay="leaveDelay"
        :leave-duration="leaveDuration"
        :mask-enter-delay="maskEnterDelay"
        :mask-enter-duration="maskEnterDuration"
        :mask-leave-dealy="maskLeaveDealy"
        :mask-leave-duration="maskEnterDuration"
        @before-enter="emit('before-enter')"
        @enter="emit('enter')"
        @after-enter="emit('after-enter')"
        @before-leave="emit('before-leave')"
        @leave="emit('leave')"
        @after-leave="emit('after-leave')"
        @close="close"
        @mask-click="emit('mask-click')"
    >
        <template #addons>
            <AdaptTransition
                appear
                :name="headerTransInfo.name"
                :delay="headerTransInfo.delay"
                :duration="headerTransInfo.duration"
            >
                <Logo
                    v-if="hasLogo && show"
                    class="layer-logo"
                    :src="customLogo"
                    :brand-logo-degrade-text="brandLogoDegradeText"
                />
            </AdaptTransition>
            <slot name="addons" />
        </template>
        <template #header>
            <div ref="layerHeaderRef" class="layer-header-placeholder" :style="headerHeight">
                <AdaptTransition
                    appear
                    :name="headerTransInfo.name"
                    :delay="headerTransInfo.delay"
                    :duration="headerTransInfo.duration"
                >
                    <Heading v-if="show" :title="title" :sub-title="subTitle" class="layer-header">
                        <template v-if="$slots.title" #title>
                            <slot name="title" />
                        </template>
                        <template v-if="$slots.subTitle" #subTitle>
                            <slot name="subTitle" />
                        </template>
                    </Heading>
                </AdaptTransition>
            </div>
        </template>
        <div class="layer-main">
            <img v-if="cover" class="layer-main-image" :src="cover" />
            <div class="layer-cover-info">
                <div v-if="infoMainText" class="info-main" :class="infoMainTextClass">{{ infoMainText }}</div>
                <div v-if="infoSub" class="info-sub">{{ infoSub }}</div>
            </div>
            <slot />
        </div>
        <template #footer>
            <div ref="layerFooterRef" class="layer-footer-placeholder" :style="footerHeight">
                <AdaptTransition
                    appear
                    :name="footerTransInfo.name"
                    :delay="footerTransInfo.delay"
                    :duration="footerTransInfo.duration"
                >
                    <div v-if="show" class="layer-ctrl">
                        <div class="layer-buttons">
                            <!-- @slot 按钮操作区域 -->
                            <slot name="button">
                                <Button
                                    v-if="secondBtnText"
                                    type="plain"
                                    class="second-btn"
                                    :class="multiBtnClass"
                                    @click="clickSecond"
                                >
                                    {{ secondBtnText }}
                                </Button>
                                <Button
                                    v-if="showMainBtn"
                                    type="primary-linear"
                                    :disabled="mainBtnDisabled"
                                    :loading="mainBtnLoading"
                                    :class="['main-btn', multiBtnClass]"
                                    :height="buttonHeight"
                                    :looks-like-disabled="mainBtnLikeDisabled"
                                    @click="clickMain"
                                >
                                    {{ mainBtnText }}
                                    <slot name="buttonInset" />
                                    <template v-if="mainBtnTextDesc" #plugin>
                                        {{ mainBtnTextDesc }}
                                    </template>
                                </Button>
                            </slot>
                        </div>
                        <div class="layer-sub-ctrl">
                            <div v-if="$slots.buttonBottom" class="layer-sub-ctrl-slot">
                                <!-- @slot 按钮操作区域下方 -->
                                <slot name="buttonBottom" />
                            </div>
                            <div v-if="bottomText" class="layer-sub-ctrl-text" role="button" @click="clickBottomText">
                                {{ bottomText }}
                            </div>
                        </div>
                    </div>
                </AdaptTransition>
            </div>
        </template>
        <template #extension>
            <slot v-if="$slots.bgLight" name="bgLight" />
        </template>
    </Popup>
</template>

<style>
:root {
    /* 标题区域上间距 */
    --adapt-layer-header-margin-top: 4px;
    /* 标题区域下间距 */
    --adapt-layer-header-margin-bottom: 24px;
    /* 中间主区域上间距 */
    --adapt-layer-main-margin-top: 24px;
    /* 中间主区域下间距 */
    --adapt-layer-main-margin-bottom: 40px;
    /* 中间主区域最大宽度 */
    --adapt-layer-main-max-width: 360px;
    /* 中间主区域最大高度 */
    --adapt-layer-main-max-height: 300px;
    /* 中间主区域最小高度 */
    --adapt-layer-main-min-height: 190px;
    /* 中间图片尺寸宽 */
    --adapt-layer-image-size-width: auto;
    /* 中间图片尺寸高 */
    --adapt-layer-image-size: 190px;
    --adapt-layer-image-max-size: 400px;
    /* 按钮区域下间距 */
    --adapt-layer-button-margin-bottom: 14px;
    /* 多按钮时按钮宽度 */
    --adapt-layer-multi-button-width: 150px;
    /* 单按钮宽度 */
    --adapt-layer-main-button-width: 200px;
    /* 多按钮时中间间距 */
    --adapt-layer-multi-button-gap: 16px;
    /* 主按钮文字颜色 */
    --adapt-layer-main-button-primary-font-color: #fff;
    /* 主按钮背景色 */
    --adapt-layer-main-button-primary-background-color: #fe3666;
    /* 主按钮字体 */
    --adapt-layer-main-button-primary-font-family: unset;
    /* 主按钮字间距 */
    --adapt-layer-main-button-primary-letter-spacing: 0;
    /* 次级按钮文字颜色 */
    --adapt-layer-second-button-font-color: #ffdcb4;
    /* 次级按钮边框颜色 */
    --adapt-layer-second-button-border-color: #ffdcb4;
    /* 次级按钮背景色 */
    --adapt-layer-second-button-background-color: transparent;
    /* 按钮操作区下方文字颜色 */
    --adapt-layer-button-bottom-text-font-color: rgba(255, 238, 203, 0.6);
    /* 按钮操作区下方文字字号 */
    --adapt-layer-button-bottom-text-font-size: 13px;
    /* 按钮渐变文字颜色 */
    --adapt-layer-main-button-primary-font-linear: var(--adapt-layer-main-button-primary-font-color);
    /* 描述文案字号 */
    --adapt-layer-info-main-font-size: 38px;
    /* 描述文案行距 */
    --adapt-layer-info-main-line-height: 38px;
    /* 描述文案字体 */
    --adapt-layer-info-main-font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
    /* 描述文案字重 */
    --adapt-layer-info-main-font-weight: 900;
    /* 描述文案颜色 */
    --adapt-layer-info-sub-color: rgba(255, 244, 202, 0.8);
    /* 辅助描述字号 */
    --adapt-layer-info-sub-font-size: 13px;
    /* 按钮字号 */
    --adapt-layer-main-button-primary-font-size: 22px;
    --adapt-layer-multi-button-font-size: 20px;
    /* 描述字色 */
    --adapt-layer-info-main-color: #ffeca8;
}
</style>

<style lang="scss" scoped>
.layer {
    --adapt-popup-close-btn-top-y-value: 26px;
    :deep(.popup-main) {
        width: var(--adapt-layer-main-max-width);
    }
    :deep(.popup-content-main) {
        position: relative;
    }

    .layer-header-placeholder {
        margin-top: var(--adapt-layer-header-margin-top);
        margin-bottom: var(--adapt-layer-header-margin-bottom);
    }

    .layer-main {
        margin-top: var(--adapt-layer-main-margin-top);
        margin-bottom: var(--adapt-layer-main-margin-bottom);
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: var(--adapt-layer-main-min-height);
        max-height: var(--adapt-layer-main-max-height);
        &-image {
            width: var(--adapt-layer-image-size-width);
            height: var(--adapt-layer-image-size);
            max-height: var(--adapt-layer-image-max-size);
        }
    }

    :deep(.popup-footer) {
        position: static;
        transform: none;
    }

    .layer-ctrl {
        .layer-buttons {
            display: flex;
            align-items: center;
            justify-content: center;

            .second-btn {
                width: var(--adapt-layer-multi-button-width);
                min-width: var(--adapt-layer-multi-button-width);
                margin-right: var(--adapt-layer-multi-button-gap);
                --adapt-button-plain-font-color: var(--adapt-layer-second-button-font-color);
                --adapt-button-plain-background-color: var(--adapt-layer-second-button-background-color);
                --adapt-button-plain-border-color: var(--adapt-layer-second-button-border-color);
                /* 单独处理按钮边框宽度，保证缩放后大于1px */
                --adapt-button-plain-border-width: 1.2px;
            }
            .main-btn {
                width: var(--adapt-layer-main-button-width);
                --adapt-button-width: 0;
                --adapt-button-primary-font-color: var(--adapt-layer-main-button-primary-font-color);
                --adapt-button-primary-background-color: var(--adapt-layer-main-button-primary-background-color);
                --adapt-button-primary-background-image: var(--adapt-layer-main-button-primary-background-image);
                --adapt-button-main-font-family: var(--adapt-layer-main-button-primary-font-family);
                --adapt-button-primary-font-linear: var(--adapt-layer-main-button-primary-font-linear);
                --adapt-button-primary-letter-spacing: var(--adapt-layer-main-button-primary-letter-spacing);
                --adapt-button-font-size: var(--adapt-layer-main-button-primary-font-size);
            }
            .multi-btn {
                width: var(--adapt-layer-multi-button-width);
                min-width: var(--adapt-layer-multi-button-width);
                --adapt-button-font-size: var(--adapt-layer-multi-button-font-size);
            }
        }
        .layer-sub-ctrl {
            &-text {
                color: var(--adapt-layer-button-bottom-text-font-color);
                text-align: center;
                font-size: var(--adapt-layer-button-bottom-text-font-size);
            }
            &:empty {
                display: none;
            }
        }

        .layer-buttons + .layer-sub-ctrl {
            margin-top: var(--adapt-layer-button-margin-bottom);
        }
    }

    .layer-cover-info {
        text-align: center;
        .info-main {
            font-family: var(--adapt-layer-info-main-font-family);
            font-size: var(--adapt-layer-info-main-font-size);
            font-weight: var(--adapt-layer-info-main-font-weight);
            line-height: var(--adapt-layer-info-main-line-height);
            background: var(--adapt-layer-info-main-color);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            &.is-text-string {
                --adapt-layer-info-main-font-family: KuaiYuanHuiTi, sans-serif;
                --adapt-layer-info-main-font-weight: 400;
                --adapt-layer-info-main-font-size: 24px;
            }
        }

        .info-sub {
            font-size: var(--adapt-layer-info-sub-font-size);
            line-height: 1;
            color: var(--adapt-layer-info-sub-color);
        }

        .info-main + .info-sub {
            margin-top: 8px;
        }
    }
}
</style>
