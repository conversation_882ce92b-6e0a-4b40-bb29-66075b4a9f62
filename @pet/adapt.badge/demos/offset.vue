<pet-info lang="json">
{ "title": "布局", "description": "" }
</pet-info>
<script setup lang="ts">
import Badge from '../base.vue';
</script>

<template>
    <div>
        <Badge type="sharp" value="徽标">
            <template #root>
                <div class="icon"></div>
            </template>
        </Badge>
        <hr />
        <h5>align="self-center"</h5>
        <Badge inline :value="999" align="self-center">
            <template #root>
                <div class="icon"></div>
            </template>
        </Badge>
        <hr />
        <h5>align="left-top"<br />:offset-x="40" :offset-y="8"</h5>
        inline:
        <Badge inline :value="999" align="left-top" :offset-x="40" :offset-y="8">
            <template #root>
                <div class="icon"></div>
            </template>
        </Badge>
        <hr />
        <h5>align="right-top"<br />:offset-x="-20"</h5>
        <Badge :value="999" align="right-top" :offset-x="-20">
            <template #root>
                <div class="icon"></div>
            </template>
        </Badge>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.icon {
    width: 88px;
    height: 88px;
    /* stylelint-disable-next-line csstree/validator */
    background: rgba(black, 0.4);
}
</style>
