<script setup lang="ts">
import { computed } from 'vue-demi';

const props = withDefaults(
    defineProps<{
        loading?: boolean;
    }>(),
    {
        loading: true,
    },
);

const DASH_LENGTH = 160;

const loadingStatus = computed(() => props.loading && 'infinity-loading');
</script>

<template>
    <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg" :class="loadingStatus">
        <path
            :stroke-dasharray="DASH_LENGTH"
            stroke-linecap="round"
            d="M40 40l8.201-8.201c4.53-4.53 11.873-4.53 16.402 0A11.598 11.598 0 0 1 68 40c0 6.405-5.193 11.598-11.598 11.598a11.598 11.598 0 0 1-8.201-3.397L40 40l-8.201-8.201c-4.53-4.53-11.873-4.53-16.402 0A11.598 11.598 0 0 0 12 40c0 6.405 5.193 11.598 11.598 11.598 3.076 0 6.026-1.222 8.201-3.397L40 40z"
            stroke="currentColor"
            stroke-width="7"
            fill="none"
            fill-rule="evenodd"
        ></path>
    </svg>
</template>

<style lang="scss" scoped>
.infinity-loading {
    stroke-dashoffset: 0;
    animation: strokeDash 1s infinite linear both;
}

@keyframes strokeDash {
    to {
        stroke-dashoffset: 320;
    }
}
</style>
