<!-- eslint-disable sonarjs/no-duplicate-string -->
<script setup lang="ts">
import OpenPacket from '../components/OpenPacket.vue';
import ClosePacket from '../components/ClosePacket.vue';
import type { ClosePacketProps } from '../components/ClosePacket.vue';
import type { OpenPacketProps } from '../components/OpenPacket.vue';
import { ref, type Ref } from 'vue';

const showOpenPopup = ref(false);
const showClosePopup = ref(false);
const openPopupData: Ref<OpenPacketProps | null> = ref(null);
const closePopupData: Ref<ClosePacketProps | null> = ref(null);

const openPacketNormal = (openType: 'none' | 'click' | 'count' = 'none', styleType?: 'red' | 'gold') => {
    openPopupData.value = {
        title: '限时获得现金挑战快手送你现金快手送你现金快手送你现金',
        subTitle: '快手送你现金快手送你现金快手送你现金快手送你现金',
        // blessing: '祝你蛇年行大运 财源滚滚财源滚滚财源滚滚财源滚滚',
        // subTitleDesc: '标题下方的描述文案titleDesc标题下方的描',
        // openBlessing: '打开的祝福语openBlessing开的祝福语op',
        sponsorLogo: 'https://cdnfile.corp.kuaishou.com/kc/files/a/sf21-warmup-internal/s2.b9c1986e11b713d5.png',
        titleContext: {
            desc: '挑战成功',
            userList: [
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1111111',
                },
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1112222',
                },
            ],
        },
        prizeDetail: [
            {
                prizeType: 'amount' as const,
                amount: '1.02',
                numberDesc: '折合现金0.00001元',
                descIcon: 'https://static.yximgs.com/udata/pkg/fe/packet-assets/wepay.png',
                // desc: '可提现至微信零钱微信零钱微信零钱微信零钱',
                unit: '金币',
                tag: '最高',
            },
        ],
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
        subButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
        openType,
        styleType,
    };
    showOpenPopup.value = true;
};

const openClosePacketNormal = (styleType?: 'gold') => {
    closePopupData.value = {
        title: '限时获得现金挑战',
        // blessing: '限时送你现金',
        subTitle: '快手送你现金现金现金现金现金现金',
        subTitleIcon:
            'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        prizeDetail: [
            {
                prizeType: 'amount' as const,
                amount: '120',
                descIcon: 'https://static.yximgs.com/udata/pkg/fe/packet-assets/wepay.png',
                unit: '元',
                tag: '最高',
            },
        ],
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
        styleType,
    };
    showClosePopup.value = true;
};

const openClosePacketBlessing = () => {
    closePopupData.value = {
        sponsorLogo: 'https://ali2.a.yximgs.com/kos/nlav10395/activity/hugesignin/sign-in-diannao.png',
        title: '限时获得现金挑战限时获得现金挑战限时获得现金挑战限时获得现金挑战',
        blessingTitle: '快手送你现金',
        blessing: '祝你蛇年行大运 财源滚',
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
    };
    showClosePopup.value = true;
};

const onCoinPacketBtnClick = (prizeDetail?: string[], type = 'click') => {
    openPopupData.value = {
        title: '限时获得现金挑战',
        subTitle: '快手送你现金',
        blessing: '祝你蛇年行大运 财源滚滚',
        prizeDetail: [
            {
                prizeType: 'amount' as const,
                amount: '10000',
                // descIcon: 'https://static.yximgs.com/udata/pkg/fe/packet-assets/wepay.png',
                // desc: '可提现至微信零钱',
                unit: '金币',
                numberDesc: '折合现金为1元',
            },
        ],
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
        openType: type as any,
    };

    showOpenPopup.value = true;
};

const onPicturePacketBtnClick = () => {
    openPopupData.value = {
        title: '限时获得现金挑战',
        subTitle: '快手送你现金',
        subTitleDesc: '折合现金为1元',
        blessing: '祝你蛇年行大运 财源滚滚',
        prizeDetail: [
            {
                prizeType: 'picture' as const,
                src: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
            },
        ],
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
        openType: 'count',
    };

    showOpenPopup.value = true;
};

const onOpenAllClick = () => {
    openPopupData.value = {
        title: '标题最长323超长换行处理标题最长323超长换行处理标题最长323超长换行处理',
        titleContext: {
            desc: '挑战成功',
            userList: [
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1111111',
                },
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1112222',
                },
            ],
        },
        subTitleDesc: '标题下方的描述文案titleDesc',
        openBlessing: '打开的祝福语openBlessing',
        subTitle: '快手送你现金',
        prizeDetail: [
            {
                prizeType: 'amount' as const,
                amount: '12.5',
                numberDesc: '折合0.1元钱',
                descIcon: 'https://static.yximgs.com/udata/pkg/fe/packet-assets/wepay.png',
                desc: '可提现至微信零钱',
                unit: '元',
                tag: '最高',
            },
            {
                prizeType: 'picture' as const,
                src: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
            },
            {
                prizeType: 'luckPicture' as const,
                src: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
            },
            {
                prizeType: 'adPicture' as const,
                src: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
            },
        ],
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
    };

    showOpenPopup.value = true;
};

const onOpenAmount = (num: number, type = 'amount') => {
    let amount = '0';
    let numberDesc = '';
    switch (num) {
        case 3:
            amount = type === 'coin' ? '100' : '0.2';
            break;
        case 4:
            amount = type === 'coin' ? '3300' : '0.02';
            break;
        case 5:
            amount = type === 'coin' ? '39300' : '10.12';
            break;
        case 6:
            amount = type === 'coin' ? '100000' : '120.88';
            break;
        default:
            break;
    }
    if (type === 'coin' && num > 3) {
        numberDesc = '折合现金0.00001元';
    }
    openPopupData.value = {
        title: '限时获得现金挑战快手送你现金快手送你现金快手送你现金',
        subTitle: '快手送你现金快手送你现金快手送你现金快手送你现金',
        sponsorLogo: 'https://cdnfile.corp.kuaishou.com/kc/files/a/sf21-warmup-internal/s2.b9c1986e11b713d5.png',
        titleContext: {
            desc: '挑战成功',
            userList: [
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1111111',
                },
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1112222',
                },
            ],
        },
        prizeDetail: [
            {
                prizeType: 'amount' as const,
                amount,
                numberDesc,
                descIcon: 'https://static.yximgs.com/udata/pkg/fe/packet-assets/wepay.png',
                // desc: '可提现至微信零钱微信零钱微信零钱微信零钱',
                unit: type === 'coin' ? '金币' : '元',
                tag: '最高',
            },
        ],
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
        subButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
    };

    showOpenPopup.value = true;
};

const onOpenBtn = (btnNum: number, desc = false) => {
    const data = {
        title: '限时获得现金挑战快手送你现金快手送你现金快手送你现金',
        subTitle: '快手送你现金快手送你现金快手送你现金快手送你现金',
        sponsorLogo: 'https://cdnfile.corp.kuaishou.com/kc/files/a/sf21-warmup-internal/s2.b9c1986e11b713d5.png',
        titleContext: {
            desc: '挑战成功',
            userList: [
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1111111',
                },
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1112222',
                },
            ],
        },
        prizeDetail: [
            {
                prizeType: 'amount' as const,
                amount: '1.12',
                numberDesc: '',
                descIcon: 'https://static.yximgs.com/udata/pkg/fe/packet-assets/wepay.png',
                // desc: '可提现至微信零钱微信零钱微信零钱微信零钱',
                unit: '元',
                tag: '最高',
            },
        ],
        mainButton: {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        },
    };

    if (btnNum === 2) {
        // @ts-expect-error
        data.subButton = {
            linkType: 'UNKNOWN',
            linkText: '做任务',
            linkUrl: 'string',
            icon: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
        };
    }

    if (desc) {
        // @ts-expect-error
        data.bottomInfo = {
            bottomDesc: '点击这里可以去',
            bottomButton: {
                linkText: '去钱包',
            },
        };
    }

    openPopupData.value = data;

    showOpenPopup.value = true;
};

const onOpenPacketAfterLeave = () => {
    openPopupData.value = null;
};

const onOpenPacketCloseClick = () => {
    showOpenPopup.value = false;
};

const onClosePacketAfterLeave = () => {
    closePopupData.value = null;
};

const onClosePacketCloseClick = () => {
    showClosePopup.value = false;
};
</script>
<template>
    <div class="page">
        <div class="wrapper">
            <hr />
            <div>openPopup/openClosePopup</div>
            <div>
                <button @click="openPacketNormal('none')">开红包</button>
                <button class="golden" @click="openPacketNormal('none', 'gold')">开金包</button>
                <button @click="openPacketNormal('count')">封皮开红包</button>
                <button class="golden" @click="openPacketNormal('click', 'gold')">封皮开金包</button>
            </div>
            <div>
                <button @click="openClosePacketNormal()">关红包</button>
                <button class="golden" @click="openClosePacketNormal('gold')">关金包</button>
                <button @click="openClosePacketBlessing()">关红包祝福</button>
            </div>
            <div>
                <button @click="onCoinPacketBtnClick()">金币</button>
                <button @click="onCoinPacketBtnClick(['coupon'])">金额+券</button>
            </div>
            <div>
                <button @click="onPicturePacketBtnClick">道具+券</button>
            </div>
            <div>
                <button @click="onOpenAllClick">最全</button>
            </div>
            <div>金币</div>
            <button @click="onOpenAmount(3, 'coin')">3字符</button>
            <button @click="onOpenAmount(4, 'coin')">4字符</button>
            <button @click="onOpenAmount(5, 'coin')">5字符</button>
            <button @click="onOpenAmount(6, 'coin')">6字符</button>
            <div>金额</div>
            <button @click="onOpenAmount(3)">3字符</button>
            <button @click="onOpenAmount(4)">4字符</button>
            <button @click="onOpenAmount(5)">5字符</button>
            <button @click="onOpenAmount(6)">6字符</button>
            <div>按钮</div>
            <button @click="onOpenBtn(1)">单按钮</button>
            <button @click="onOpenBtn(1, true)">单按钮+钱包</button>
            <button @click="onOpenBtn(2)">双按钮</button>
            <button @click="onOpenBtn(2, true)">双按钮+钱包</button>
        </div>
        <OpenPacket
            v-if="openPopupData"
            :show="showOpenPopup"
            v-bind="openPopupData"
            @after-leave="onOpenPacketAfterLeave"
            @close="onOpenPacketCloseClick"
            @main-click="onOpenPacketCloseClick"
            @mid-click="onOpenPacketCloseClick"
        ></OpenPacket>
        <ClosePacket
            v-if="closePopupData"
            :show="showClosePopup"
            v-bind="closePopupData"
            @after-leave="onClosePacketAfterLeave"
            @close="onClosePacketCloseClick"
            @main-click="onClosePacketCloseClick"
            @mid-click="onClosePacketCloseClick"
        ></ClosePacket>
    </div>
</template>

<style lang="scss" scoped>
.page {
    font-size: 16px;
    width: 100vw;
    height: 100vh;
}

.wrapper {
    padding-top: 40px;
}

button {
    height: 20px;
    min-width: 80px;
    background-color: #f7211d;
    margin-right: 5px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.golden {
    background-color: #ffdcb4;
}
</style>
