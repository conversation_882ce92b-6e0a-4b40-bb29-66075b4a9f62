<script lang="ts" setup>
import LlwardItem from './LlwardItem.vue';
import { type RenderllwardType } from '../manager/config';
const props = defineProps<{
    llward?: RenderllwardType;
}>();

const grids = computed(() => {
    return (
        props.llward?.map((grid) => {
            const position = grid.position;
            return {
                ...grid,
                styles: {
                    transform: `translate(${position.x / 100}rem, ${position.y / 100}rem)`,
                    zIndex: position.zIndex,
                },
                step: position.step,
            };
        }) ?? []
    );
});
</script>
<template>
    <div v-for="grid in grids" :key="grid.step" class="llward-group" :style="grid.styles">
        <LlwardItem
            v-if="grid.gridIcon"
            :icon="grid.gridIcon"
            :step="grid.step"
            :type="grid.type"
            :display-amount="grid.displayAmount"
            :show-amount="grid.showAmount"
        />
    </div>
</template>
<style lang="scss" scoped>
.llward-group {
    position: absolute;
    width: 100px;
    height: 80px;
    top: -15px;
    left: -45px;
    z-index: 89;
}
</style>
