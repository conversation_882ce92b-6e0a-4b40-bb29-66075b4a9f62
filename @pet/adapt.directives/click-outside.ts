import type { App, DirectiveBinding, ObjectDirective } from 'vue';
import { isBrowser } from '@pet/yau.core/consts';

interface ElementExtend extends Element {
    handleOutsideClick?: any;
}

interface DOMEvent<T extends EventTarget> extends Event {
    readonly target: T;
}

export const clickOutside: ObjectDirective<any, (ev: TouchEvent | MouseEvent) => void> = {
    created(el: ElementExtend, binding: DirectiveBinding) {
        const dom = el;
        dom.handleOutsideClick = (event: DOMEvent<HTMLElement>) => {
            if (
                !(dom === event.target || dom.contains(event.target) || Boolean(dom.parentNode?.contains(event.target)))
            ) {
                binding.value(event);
            }
        };
        if (isBrowser) {
            document.documentElement.addEventListener('touchmove', el.handleOutsideClick, false);
            // iOS低版本(目前发现的12.4.1)在某些场景感觉click被stop了，所以增加了一个touchstart事件来处理
            document.documentElement.addEventListener('touchstart', el.handleOutsideClick, false);
            document.documentElement.addEventListener('click', el.handleOutsideClick, false);
        }
    },
    unmounted(el: ElementExtend) {
        const dom = el;
        if (isBrowser) {
            document.documentElement.removeEventListener('touchmove', el.handleOutsideClick, false);
            document.documentElement.removeEventListener('touchstart', el.handleOutsideClick, false);
            document.documentElement.removeEventListener('click', el.handleOutsideClick, false);
        }
        delete dom.handleOutsideClick;
    },
};

export const vClickOutside = clickOutside;

export default {
    install: (Vue: App) => {
        Vue.directive('click-outside', clickOutside as any);
    },
};
