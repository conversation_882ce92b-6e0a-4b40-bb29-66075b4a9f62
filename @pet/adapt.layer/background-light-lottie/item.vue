<script setup lang="ts">
import { ref, watch } from 'vue-demi';
import { paint } from '@vision/runtime';
const bgLottie = ref<HTMLElement | null>(null);

watch(bgLottie, async (val) => {
    if (!val) {
        return;
    }
    const effectData = await import('./config').then((res) => res.default);
    const effectInstance = paint(val, effectData, {
        options: {
            ratio: 2,
            // 为了更好控制播放状态，不使用内置的自动播放
            autoplay: false,
            // 同上
            loop: true,
        },
    });
    effectInstance.play();
});
</script>

<template>
    <div ref="bgLottie" class="item"></div>
</template>

<style lang="scss" scoped>
.item {
    width: 100%;
    height: 100%;
}
</style>
