import { isAtSearchTab, isAtFourTab } from '@pet/yau.core';
import { invoke } from '@yoda/bridge';
import { onBeforeUnmount, onMounted } from 'vue';

const isFourTabMode = isAtFourTab();
const isSearchTabMode = isAtSearchTab();

const addEventListener = async (eventType: string, listenerCb: (data: any) => void) => {
    try {
        await invoke('event.addEventListener' as any, {
            type: eventType,
            listener: listenerCb,
        });
    } catch {}
};

const removeEventListener = async (eventType: string, listenerCb: (data: any) => void) => {
    try {
        await invoke('event.removeEventListener' as any, {
            type: eventType,
            listener: listenerCb,
        });
    } catch {}
};

const dispatchEvent = async (eventType: string, data: any) => {
    try {
        await invoke('event.dispatchEvent' as any, {
            type: eventType,
            data: JSON.stringify(data),
        });
    } catch {}
};

const exitWebView = async () => {
    try {
        await invoke('webview.exitCurrentWebView');
    } catch {}
};

export function closeOtherPage() {
    const eventType = 'h5-25summer-home-page-auto-close';

    // 第4tab、搜索态不执行相关逻辑
    if (isSearchTabMode || isFourTabMode) {
        return;
    }

    onMounted(async () => {
        try {
            await dispatchEvent(eventType, {});
            await addEventListener(eventType, exitWebView);
        } catch (e) {}
    });
    onBeforeUnmount(() => {
        removeEventListener(eventType, exitWebView);
    });
}
