import mockjs from 'mockjs';
const mockInpushTask = {
    popupType: 'LS_LLCH_LLREWD',
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    title: '快手官方送钱啦',
    titleContext: null,
    subTitle: '明日打卡必得',
    subTitleDesc: null,
    mainButton: {
        linkType: 'COMMON_TASK',
        linkText: '设置提醒得金币',
        linkSubText: '必得126金币',
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    bottomButton: null,
    blessing: null,
    icon: null,
    showInTaskList: false,
    goldenStyle: false,
    stationDayIndex: 2,
    llpeDetail: [
        {
            llpeType: 'LLCN',
            amount: 1688,
            displayAmount: '1688',
            displayUnit: '金币',
            bottomDesc: null,
            // taskExtra: {
            //     taskId: 17482,
            //     title: '订阅打卡提醒',
            //     description: '完成订阅，金币+126',
            //     iconUrls: [
            //         'http://blobstore-nginx.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1751429424951-kFO2O8.png',
            //     ],
            //     completeConditionAmount: 1,
            //     completedAmount: 0,
            //     completeMaxTimes: 1,
            //     completedTimes: 0,
            //     taskStatus: 'COMPLETING_TASK',
            //     displayText: '去订阅',
            //     jumpLink: '',
            //     jumpType: 'report',
            //     prizeName: '25暑期 向前冲-金币',
            //     prizeCount: 126,
            //     prizeId: 44707,
            //     extParams: {
            //         taskType: 'INPUSH_SUBSCRIBE',
            //     },
            //     completeToastText: '',
            //     timeLimitedStart: 0,
            //     timeLimitedEnd: 0,
            //     timeLimitExpireTime: 0,
            //     timeLimitedType: 0,
            //     takeTime: 1751535553036,
            //     delayQueryStatus: false,
            //     widgetParam: '',
            //     taskShowStyle: null,
            //     apkName: '',
            //     apkAddr: '',
            //     iosStoreAddr: '',
            //     iosSchema: '',
            //     shareSubBiz: '',
            //     unsupportedToast: '',
            //     taskPriority: 0,
            //     taskContentId: '完成订阅，金币+${prizeCount}',
            //     taskToken: '6cZ78EFqZvg8Q1UzdRINsm_lWJwdUL38hqHHs2oKWpo',
            //     subBizId: 13625,
            //     takeType: 'DISPLAY_TAKE',
            //     takeStatus: 'TAKED',
            //     assistUserInfo: null,
            //     relationChainInfo: [],
            //     relationChainTypes: null,
            //     prizeIconUrls: [],
            //     reservationCalendarConfig: {
            //         remindPeriod: 0,
            //         scheduleStartTimeStamp: 0,
            //         scheduleEndTimeStamp: 0,
            //         calendarTitle: '',
            //         calendarDescription: '',
            //         kwaiLink: '',
            //         nebulaLink: '',
            //         dailyTimeStampList: [],
            //     },
            //     dynamicExtParam: {},
            //     titleCornerText: '',
            //     descriptionLabel: '',
            //     taskPropertyKey: '',
            //     backgroundColor: '',
            //     backgroundUrl: '',
            //     titleCornerUrl: '',
            //     bannerUrl: '',
            //     cornerBubbleText: '',
            //     pendantIconUrl: '',
            // },
        },
    ],
};

export default mockjs.mock({
    result: 1,
    message: '@string',
    data: {
        progress: {
            currentStep: 1,
            expectTotalStep: 12,
            signed: false,
            gridTaskDegrade: '@boolean',
            currentTime: '@dateTime',
            lastStation: false,
        },
        luckRushSudokuView: [mockInpushTask],
    },
    timestamp: '@integer(1, 100)',
    hostname: '@string',
    error_msg: '@string',
});

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
