<script lang="ts" setup>
import { computed, defineProps, onMounted, ref, type ImgHTMLAttributes } from 'vue-demi';
import { type CDNLevelType, type DowngradeType, useCdnLevel } from './useCdnLevel';

interface ImgProps extends /* @vue-ignore */ ImgHTMLAttributes {
    /** cdn 降级等级 */
    cdnLevel?: CDNLevelType;
    src?: string;
    /** 降级类型 */
    downgradeType?: DowngradeType;
    /** 兜底图 */
    emptyImage?: string;
}

export type ImageEmits = {
    /**
     * 加载完成
     */
    (event: 'load'): void;
    /**
     * 加载失败
     */
    (event: 'error'): void;
};

const emit = defineEmits<ImageEmits>();

const props = defineProps<ImgProps>();

const { downgradeState } = useCdnLevel();

const isDowngrade = computed(() => downgradeState.value[props.cdnLevel ?? 'NONE']);

const emptyImage = computed(
    () =>
        props.emptyImage ||
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mN89B8AAskB44g04okAAAAASUVORK5CYII=',
);

const isError = ref(false);
const srcImg = computed(() => (isDowngrade.value || isError.value ? emptyImage.value : props.src));

function onLoad() {
    emit('load');
}

function onError() {
    isError.value = true;
    emit('error');
}

// 降级或报错时，根据 downgradeType 来判断是否隐藏
const imgShow = computed(() => !((isDowngrade.value || isError.value) && props.downgradeType === 'hidden'));
</script>

<template>
    <img v-if="imgShow" v-bind="{ ...props }" :src="srcImg" @error="onError" @load="onLoad" />
</template>

<style lang="scss" scoped></style>
