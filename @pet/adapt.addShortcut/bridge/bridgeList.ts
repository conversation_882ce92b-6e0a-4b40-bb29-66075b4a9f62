import { invoke } from '@yoda/bridge';
/**
 * 以系统浏览器方式打开某链接
 * @param params {Object} 打开参数
 * @returns 返回值
 */
export const loadUrlByOpenBrowser = async (params: { url: string }) => {
    return invoke('tool.openBrowser', params);
};
/**
 * 添加桌面快捷方式
 * @param params {Object} 打开参数
 * @returns 返回值
 */
export const addShortcut = async (params: { uri: string; shortcutId: string; name: string; icon: string }) => {
    return invoke('tool.addShortcut', params);
};

/**
 * 打开新的webview页面
 * @param params {Object} 打开参数
 * @returns 返回值
 */

export const loadUrlOnNewPageBridge = async (params: {
    url: string;
    type: 'back' | 'close';
    ignoreHalfScreenDisplay?: 0 | 1;
    leftTopBtnType: 'back' | 'close';
}) => {
    return invoke('tool.loadUrlOnNewPage', params).catch(() => {
        window.open(params.url, '_blank');
    });
};
