<script setup lang="ts">
import Headding from '../index.vue';
</script>

<template>
    <div class="demo">
        <h1>这里是 demo</h1>
        <p>注意：主副标题只能同时设置文案，或者同时设置切图。</p>
        <div class="container">
            <h4>一行标题文案</h4>
            <Headding title="一行标题文案" class="block" show-lottie></Headding>
            <h4>一行标题文案+子标题</h4>
            <Headding title="一行标题文案+子标题" sub-title="子标题" class="block"></Headding>
            <h4>两行标题文案</h4>
            <Headding :title="['恭喜', '恭喜获得惊喜奖励']" class="block"></Headding>
            <h4>两行标题文案+子标题文案</h4>
            <Headding :title="['获得奖励!', '恭喜获得惊喜奖励']" sub-title="子标题" class="block"></Headding>
            <h4>一行标题切图）</h4>
            <Headding
                title-img="https://static.yximgs.com/udata/pkg/KS-GROWTH-OPERATION-CDN/one-title_img.png"
                class="block"
            ></Headding>
            <h4>一行标题切图）+子标题切图</h4>
            <Headding
                sub-title-img="https://static.yximgs.com/udata/pkg/KS-GROWTH-OPERATION-CDN/sub-title-bg.png"
                title-img="https://static.yximgs.com/udata/pkg/KS-GROWTH-OPERATION-CDN/one-title_img.png"
                class="block"
            ></Headding>
            <h4>两行标题切图</h4>
            <Headding
                title-img="https://static.yximgs.com/udata/pkg/KS-GROWTH-OPERATION-CDN/title_img.png"
                class="block"
            ></Headding>
            <h4>两行标题切图+子标题切图</h4>
            <Headding
                sub-title-img="https://static.yximgs.com/udata/pkg/KS-GROWTH-OPERATION-CDN/sub-title-bg.png"
                title-img="https://static.yximgs.com/udata/pkg/KS-GROWTH-OPERATION-CDN/title_img.png"
                class="block"
            ></Headding>
            <h4>slots</h4>
            <Headding class="block">
                <template #title> 主标题slots</template>
                <template #subTitle> 子标题subTitle slots</template>
            </Headding>
        </div>
    </div>
</template>

<style lang="scss" scoped>
div {
    font-size: 16px;
}
.container {
    --adapt-heading-sub-title-font-color: #888;
}
.block {
    border-style: solid;
    border-width: 1px;
    border-radius: 10px;
    border-color: #eee;
    background-color: rgba($color: #000000, $alpha: 0.85);
}
</style>
