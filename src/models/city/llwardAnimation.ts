import { createUseModel } from '@gundam/model';
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';

import { GridllwardType } from '@/components/city/charge-forward/manager/config';

import { SoundType, useAudioModel } from '../audioModel';
// 格子金币
const LlwardGridAni = defineAsyncComponent(
    () => import('../../components/city/charge-forward/assets/effects/llwardPilot/index.vue'),
);

// 格子现金
const LlwardCHAni = defineAsyncComponent(
    () => import('../../components/city/charge-forward/assets/effects/llwardCHPilot/index.vue'),
);

export type LlwardAniName =
    | GridllwardType.CITY_LLCH_GRID
    | GridllwardType.CITY_LLCN_GRID
    | GridllwardType.CITY_TASK_GRID;
export const useLlwardAnimation = createUseModel(() => {
    const { playSound } = useAudioModel();
    const { effectShowStatus } = useDowngradeLevel();

    const downgradeL1 = computed(() => {
        return !effectShowStatus.value.L1;
    });
    const downgradeL2 = computed(() => {
        return !effectShowStatus.value.L2;
    });
    const animationPromise = ref<Promise<any>>();
    const resolver = ref<(value?: any) => void>();
    const show = ref(false);
    const lottieName = ref<LlwardAniName | undefined>();
    const showWalletCoinAni = ref(false);
    const aniComp = shallowRef();
    const walletDelay = ref(1300);

    const playSoundStation = async () => {
        await playSound(SoundType.STATION_COIN);
        setTimeout(() => {
            playSound(SoundType.FLY);
        }, 800);
    };

    const playSoundGrid = () => {
        setTimeout(() => {
            playSound(SoundType.GRID_COIN);
        }, 200);
    };

    const endAni = () => {
        lottieName.value = undefined;
        show.value = false;
        resolver.value?.();
        aniComp.value = null;
        animationPromise.value = undefined;
        if (showWalletCoinAni.value) {
            setTimeout(() => {
                showWalletCoinAni.value = false;
            }, walletDelay.value);
        }
    };

    const startAni = (name: LlwardAniName) => {
        lottieName.value = name;
        animationPromise.value = new Promise((res, reject) => {
            resolver.value = res;
            show.value = true;
        });
        switch (lottieName.value) {
            // 格子奖励：现金
            case GridllwardType.CITY_LLCH_GRID:
                playSoundStation();
                aniComp.value = LlwardCHAni;
                break;
            // 格子奖励：金币、任务
            case GridllwardType.CITY_LLCN_GRID:
            case GridllwardType.CITY_TASK_GRID:
                walletDelay.value = 1300;
                setTimeout(() => {
                    showWalletCoinAni.value = !downgradeL2.value;
                }, 400);
                playSoundGrid();
                aniComp.value = LlwardGridAni;
                break;

            default:
                endAni();
                aniComp.value = null;
                break;
        }
        return animationPromise.value;
    };

    return {
        show,
        name: lottieName,
        startAni,
        endAni,
        comp: aniComp,
        showWallet: showWalletCoinAni,
    };
});
