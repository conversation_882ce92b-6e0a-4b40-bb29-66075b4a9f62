<script lang="ts">
export default {
    name: 'AdaptItemButton',
};
</script>

<script setup lang="ts">
import { computed } from 'vue-demi';

interface Props {
    /**
     * 禁止点击
     */
    disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    disabled: false,
});

const emit = defineEmits<{
    /**
     * 按钮点击
     */
    (event: 'click'): void;
}>();

function clickHandle() {
    if (!props.disabled) {
        emit('click');
    }
}

const buttonClass = computed(() => props.disabled && 'item-button-disabled');
</script>

<template>
    <div class="item-button" :class="buttonClass" role="button" @click="clickHandle">
        <span class="item-button-icon-wrapper">
            <slot name="icon" />
        </span>
        <span class="item-button-text">
            <slot />
        </span>
        <span v-if="$slots.addon" class="item-button-addon">
            <slot name="addon" />
        </span>
    </div>
</template>
<style>
:root {
    --adapt-item-button-height: 50px;
    --adapt-item-button-icon-size: 24px;
    --adapt-item-button-text-marginLeft: 8px;
    --adapt-item-button-text-font-size: 14px;
    --adapt-item-button-text--color: #000;
    --adapt-item-button-padding: 0 12px;
    --adapt-item-button-opacity: 0.5;
}
</style>
<style lang="scss" scoped>
.item-button {
    display: flex;
    align-items: center;
    color: var(--adapt-item-button-text--color);
    height: var(--adapt-item-button-height);
    padding: var(--adapt-item-button-padding);
    cursor: pointer;
}
.item-button-disabled {
    opacity: var(--adapt-item-button-opacity);
    cursor: not-allowed;
}
.item-button-icon-wrapper {
    width: var(--adapt-item-button-icon-size);
    height: var(--adapt-item-button-icon-size);
    display: inline-block;
    transform: translate(0);
    & + .item-button-text {
        margin-left: var(--adapt-item-button-text-marginLeft);
        font-size: var(--adapt-item-button-text-font-size);
    }
}
.item-button-icon-wrapper :deep(svg),
.item-button-icon-wrapper :deep(img) {
    width: 100%;
    height: 100%;
    display: block;
}
.item-button-addon {
    height: 100%;
    margin-left: auto;
    display: flex;
    align-items: center;
}
</style>
