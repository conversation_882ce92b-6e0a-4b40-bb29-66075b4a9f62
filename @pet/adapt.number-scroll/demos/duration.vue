<pet-info lang="json">
{
    "title": "时间控制",
    "description": "控制滚动时间"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const numberValueData = ref(100);

function numberChange() {
    const num = 100;
    numberValueData.value += num * Math.random();
}
</script>

<template>
    <div class="demo">
        <h5>原始数值{{ numberValueData }}</h5>
        <div class="container">
            <h6>:animation-duration="200"</h6>
            <NumberScroll :number-value="numberValueData" :animation-duration="200" />
        </div>
        <button @click="numberChange">增加数值</button>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 48px 0 24px;
}
</style>
