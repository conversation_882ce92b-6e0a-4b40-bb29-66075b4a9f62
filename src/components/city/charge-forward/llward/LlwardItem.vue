<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import AdaptStrokeText from '@pet/adapt.stroke-text/index.vue';

import { useBeginnerGuideModel } from '@/models/city/beginnerGuideModel';
import type { CityGridInfoView } from '@/services/open-api-docs/home/<USER>/schemas';

import { GridllwardType } from '../manager/config';
import { usePopupModel } from '@/models/popup.model';
import { useConfigModel } from '@/models/city/configModel';

const { effectShowStatus } = useDowngradeLevel();
const EndGridEffects = defineAsyncComponent(() => import('../assets/effects/endGrid/EffectIndex.vue'));

type GridLlrewdAmount = CityGridInfoView['displayAmount'];
type ShowAmount = CityGridInfoView['showAmount'];

const props = defineProps<{
    icon: string;
    step: number;
    type: GridllwardType;
    showAmount: ShowAmount;
    displayAmount: GridLlrewdAmount;
}>();

const { showGirdIcon, showEndGirdIcon } = useBeginnerGuideModel();
const { kconfConfig, defaultKconfConfig, kconfUEConfig } = useConfigModel();
const { prizeShowOpenPopup } = usePopupModel();

const downgrade = computed(() => {
    return !effectShowStatus.value.L3;
});

const numClass = computed(() => {
    if (props.type === GridllwardType.CITY_TASK_GRID) {
        return 'num-center';
    }
    return 'num-bottom';
});

const gridText = computed(() => {
    if ([GridllwardType.CITY_TASK_GRID, GridllwardType.CITY_LLCN_GRID].includes(props.type)) {
        return `+${String(props.displayAmount)}`;
    }
    if (props.type === GridllwardType.CITY_LLCH_GRID) {
        return `${String(props.displayAmount)}元`;
    }
    return props.displayAmount;
});
const handlePrizeShowOpenPopup = () => {
    prizeShowOpenPopup({
        sponsorLogo: kconfUEConfig.value?.gridGiftPopupLogo,
        icon: kconfUEConfig.value?.gridGiftPopupIcon,
        ...(kconfConfig.value?.gridGiftPopupText ?? defaultKconfConfig?.gridGiftPopupText ?? {}),
    });
};
</script>
<template>
    <div class="llward-item">
        <div
            v-if="type === GridllwardType.CITY_END_GRID"
            class="llward-end-container"
            :class="{ animated: showEndGirdIcon }"
            v-show-log="{ action: 'GRID_GIFT_ICON', params: {} }"
            v-click-log="{ action: 'GRID_GIFT_ICON', params: {} }"
            @click="handlePrizeShowOpenPopup"
        >
            <img v-if="downgrade" :src="icon" class="llward-icon" />
            <EndGridEffects v-else class="llward-icon"></EndGridEffects>
        </div>
        <div v-else class="llward-icon-container" :class="{ animated: showGirdIcon }">
            <img :src="icon" class="llward-icon" />
            <!-- 如果是任务格子，黑盒展示最高xx -->
            <template v-if="type === GridllwardType.CITY_TASK_GRID && !showAmount">
                <div class="llward-num llward-num-task">
                    <AdaptStrokeText text="最高"></AdaptStrokeText>
                    <AdaptStrokeText :text="`${displayAmount}`"></AdaptStrokeText>
                </div>
            </template>
            <!-- 如果是其他格子，明盒才展示金额 -->
            <template v-else>
                <div v-if="showAmount && displayAmount" class="llward-num" :class="numClass">
                    <AdaptStrokeText :text="`${gridText}`"></AdaptStrokeText>
                </div>
            </template>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.llward-item {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    .llward-end-container {
        position: absolute;
        top: -10px;
        left: -2px;
        bottom: 0;
        right: 0;
        margin: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        opacity: 0;
        .llward-icon {
            width: 100px;
            height: 80px;
            object-fit: contain;
        }
        &.animated {
            transform-origin: 50px 40px;
            animation: hash9de54326_kf 640ms 0ms linear forwards;
        }
    }
    .llward-icon-container {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        width: 40px;
        height: 42px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        opacity: 0;
        transition: opacity 600ms linear;
        &.animated {
            opacity: 1;
        }
        .llward-icon {
            width: 40px;
            height: 40px;
            object-fit: contain;
        }
        .llward-num {
            width: max-content;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
            --adapt-stroke-text-font-weight: 900;
            --adapt-stroke-text-font-family: MiSans-Heavy;
            --adapt-stroke-text-font-size: 9px;
            --adapt-stroke-text-color: #fe3666;
            --adapt-stroke-text-stroke-width: 0.89px;
            --adapt-stroke-text-line-height: 9px;
            --adapt-stroke-text-skew: skew(-5deg);
            &-task {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            &.num-bottom {
                bottom: 0;
                line-height: 12px;
            }
            &.num-center {
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }
    .llward-bottom {
        position: absolute;
        width: 76px;
        height: 35px;
        left: 0;
        bottom: 18px;
        right: 0;
        margin: auto;
        transform-origin: 38px 17.5px;
        // animation: llward-bottom-ani 3000ms 0ms steps(10) infinite;
        @include bg('../assets/bg/llwdBottom.png');
        background-size: 100% 100%;
        z-index: -1;
    }
    @keyframes hash9de54326_kf {
        0% {
            opacity: 0;
            transform: matrix3d(0.99, -0.139, 0, 0, 0.139, 0.99, 0, 0, 0, 0, 1, 0, -6.75, 0, 0, 1);
        }

        15.6% {
            opacity: 0.805;
            transform: matrix3d(0.995, -0.096, 0, 0, 0.096, 0.995, 0, 0, 0, 0, 1, 0, -2.112, -13.575, 0, 1);
        }

        31.3% {
            opacity: 1;
            transform: matrix3d(1, -0.007, 0, 0, 0.007, 1, 0, 0, 0, 0, 1, 0, -1.625, -15, 0, 1);
        }

        46.9% {
            opacity: 1;
            transform: matrix3d(0.998, 0.07, 0, 0, -0.07, 0.998, 0, 0, 0, 0, 1, 0, -0.963, -10.208, 0, 1);
        }

        62.5% {
            opacity: 1;
            transform: matrix3d(0.997, 0.081, 0, 0, -0.081, 0.997, 0, 0, 0, 0, 1, 0, 0.813, 2.644, 0, 1);
        }

        78.1% {
            opacity: 1;
            transform: matrix3d(0.999, 0.033, 0, 0, -0.033, 0.999, 0, 0, 0, 0, 1, 0, 0.783, 3.132, 0, 1);
        }

        93.8% {
            opacity: 1;
            transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0.105, 0.42, 0, 1);
        }

        100% {
            opacity: 1;
            transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
        }
    }
}
</style>
