<pet-info lang="json">
{ "title": "自定义", "description": "定位尖角处于气泡的方向" }
</pet-info>

<script setup lang="ts">
import type { ArrDirection, ArrAlign } from '@pet/adapt.tool-tip/types';
import { transViewValue } from '@pet/core.mobile';
import { ref, computed } from 'vue-demi';

import Popover from '../index.vue';
import type { AnimationType } from '../types';
const show = ref(false);
function handleShow() {
    show.value = !show.value;
}
function popoverClose() {
    console.log('close');
}

function popoverLeave() {
    console.log('leave');
}

const arrDirection: ArrDirection = 'bottom';
const arrAlign: ArrAlign = 'center';
const arrDistance = ref(-1);
const bgColor = ref('');
const color = ref('');
const animationType: AnimationType = 'scale-in-out';
</script>

<template>
    <div class="demo">
        <div class="popover-wrapper">
            popover: {{ show }}
            <Popover
                :show="show"
                :animation-type="animationType"
                :arr-direction="arrDirection"
                :arr-align="arrAlign"
                :arr-distance="+arrDistance"
                :background="bgColor"
                :text-color="color"
                :enter-duration="120"
                :leave-duration="60"
                :arrow-info="{
                    width: transViewValue(16),
                    height: transViewValue(8),
                }"
                class="popover-main tip-color-border"
                @close="popoverClose"
                @after-leave="popoverLeave"
            >
                显示文案内容
                <template #arrow>
                    <span class="custom-css-arr">
                        <div class="up-arrow"></div>
                        <div class="up-arrow-border"></div>
                    </span>
                </template>
            </Popover>
            <button @click="handleShow">{{ show ? '隐藏' : '显示' }}</button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
    font-size: 16px;
    padding-top: 100px;
}
label {
    display: inline-flex;
    justify-content: center;
}
.popover-wrapper {
    position: relative;
    height: 30px;
}
.popover-main {
    left: 160px;
    top: -50px;
}
.tip-color-border {
    color: #000;
    --adapt-tool-tip-border: 1px solid blue;
    font-size: 14px;
}
.up-arrow {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid transparent;
    border-bottom: 10px solid #ccc;
}
.up-arrow-border {
    position: absolute;
    top: 2.5px;
    left: 50%;
    transform: translate(-50%, 0);
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 9px solid transparent;
    border-bottom: 9px solid #fff;
}
.custom-css-arr {
    position: relative;
    top: -20px;
}
</style>
