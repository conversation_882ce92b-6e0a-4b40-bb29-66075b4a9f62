import { isInIOS, isInNebula } from '@pet/yau.core/device';

export const NoLoginCode = [109];

export const LoginExceptionCode = [6001, 6002];

export const CityAccessCode = [190651, 190652, 190653];

// 双绑异常 errorCode for 25summer
export const BindErrorCode = [150600, 150601];

export enum ErrorCode {
    /** 活动未开始 */
    NotStart = 150003,
    /** 活动已结束 */
    End = 150004,
    /** 非活动地区 */
    ProhibitedArea = 100107,
    /** 用户被封禁 */
    UserBand = 100111,
    /** 用户被社交封禁 */
    UserSocialBand = 100112,
    /** 用户客户端版本过低 */
    LowClientVersion = 100105,
    /** 用户系统版本过低 */
    LowSystemVersion = 100106,
    /** 跳一跳 设备不支持 */
    JumpLowDevice = 150009,
    /** 青少年模式禁止 */
    ParentControl = 100108,
    /** 纯血鸿蒙不支持 */
    PureHarmonyOS = 100109,
    /** 网络异常 */
    NetworkError = 500,
    /** 活动火爆 */
    ActivityHot = 5,
    /** -1tab预热切除夕的兜底页 */
    WARMUP_TO_EVE = 9999999,
    /** 25暑期新增 */
    UserRequestLock = 100002,
    SystemError = 100004,
    KpnNotSupport = 100113,
    OutSideBanned = 100114,
    TestEnd = 100115,
    StationUpdateError = 150223,
}

export const URLS = {
    unbanPage: 'https://app.m.kuaishou.com/unban/index.html#/activate',
    apps: {
        kwai: {
            iOS: 'itms-apps://itunes.apple.com/app/440948110',
            android: 'market://details?id=com.smile.gifmaker',
        },
        nebula: {
            iOS: 'itms-apps://itunes.apple.com/app/1472502819',
            android: 'market://details?id=com.kuaishou.nebula',
        },
    },
};

export function getAppMarketUrl(config = URLS.apps) {
    const appEnv = isInNebula() ? 'nebula' : 'kwai';
    const system = isInIOS() ? 'iOS' : 'android';
    return config[appEnv][system];
}

export const LoginSource = 51;
