<script setup lang="ts">
import { computed } from 'vue-demi';
import NumberScroll from '@pet/adapt.number-scroll/index.vue';
import RelationLink from '../relation-link/relation-link.vue';
import Progress from '../progress/progress.vue';
import HighLightText from '../high-light-text/high-light-text.vue';
import type { PacketProps } from '../types';

const props = defineProps<PacketProps>();

const amountFixed = computed(() => {
    const amountString = String(props?.details?.amount ?? 0);
    const decimalIndex = amountString.indexOf('.');

    if (decimalIndex === -1) {
        return 0;
    } else {
        const decimalDigits = amountString.length - decimalIndex - 1;
        return Math.min(decimalDigits, 2);
    }
});

const amountSize = computed(() => {
    return props?.details?.amount?.length && props?.details?.amount.length < 6 ? 70 : 60;
});

const emits = defineEmits<{
    (e: 'group-click'): void;
    (e: 'invite-avatar-click'): void;
}>();
</script>

<template>
    <div class="main-content">
        <HighLightText v-if="title" class="inner-title" :pre-text="title"></HighLightText>
        <div v-if="details?.amount" class="amount-wrapper">
            <div class="amount-box">
                <NumberScroll
                    class="amount"
                    :font-size="amountSize"
                    :line-height="74"
                    :number-value="Number(details.amount)"
                    :to-fixed="amountFixed"
                />
                <span v-if="details.unit" class="unit">{{ details.unit }}</span>
            </div>
            <div v-if="details.amountDesc" class="mid-desc">{{ details.amountDesc }}</div>
        </div>
        <RelationLink
            v-if="details?.relationInfo?.totalCount || details?.relationInfo?.userList.length"
            :info="details.relationInfo"
            @group-click="emits('group-click')"
            @invite-avatar-click="emits('invite-avatar-click')"
        />
        <div v-if="details?.midPic" class="pic-item" :style="{ backgroundImage: `url(${details.midPic})` }"></div>
        <Progress v-if="details?.progressInfo" :info="details.progressInfo"></Progress>
        <slot />
    </div>
</template>

<style lang="scss" scoped>
.main-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: var(--adapt-packet—inner-top-margin);
    z-index: 1;

    .inner-title {
        font-size: 18px;
        font-weight: bold;
        color: var(--adapt-packet-inner-title-color);
        --adapt-highlight-text-color: #ff0b24;
    }

    .amount-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        height: 106px;
        margin-top: var(--adapt-packet-inner-amount-margin-top);
        margin-bottom: var(--adapt-packet-inner-amount-margin-bottom);

        .amount-box {
            display: flex;
            justify-content: center;
            align-items: end;
            height: 74px;

            .amount {
                font-family: var(--adapt-packet-inner-amount-font-family);

                :deep(.number-node) {
                    background: var(--adapt-packet-inner-amount-color);
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
            }

            .unit {
                display: inline-block;
                font-size: 18px;
                font-weight: bold;
                color: var(--adapt-packet-inner-amount-unit-color);
                padding-bottom: 6px;
                margin-left: 1px;
            }
        }
    }

    .pic-item {
        width: var(--adapt-packet-inner-mid-pic-width);
        height: var(--adapt-packet-inner-mid-pic-height);
        margin-bottom: var(--adapt-packet-inner-mid-pic-margin-bottom);
        border-radius: var(--adapt-packet-inner-mid-pic-radius);
        background: no-repeat center/100%;
    }

    .mid-desc {
        margin-top: var(--adapt-packet-inner-amount-desc-margin-top);
        font-size: 14px;
        font-family: 'PingFang SC', sans-serif;
        color: var(--adapt-packet-inner-amount-desc-color);
    }
}
</style>
