export enum CalendarRes {
    SUCCESS = 1,
    IOS_NOT_FOUND = -1,
    ADR_NOT_FOUND = 412,
}

export enum CalendarMethod {
    DELETE = 'delete',
    ADD = 'add',
    SEARCH = 'search',
}

export interface CalendarParams {
    title?: string;
    note?: string;
    url?: string;
    startDay?: number;
    endDay?: number;
    type: 1 | 2 | 3;
}

export interface CalendarEvent {
    eventId: number | string;
    calendarEventList: Array<CalendarParams>;
}
