import { sleep } from '@pet/yau.core';
import { invoke } from '@yoda/bridge';
import { LoginSource, LoginExceptionCode } from './const';

export function loginApp() {
    let logging = false;
    // let hasTryRelogin = false;
    async function doLogin(landingUrl?: string) {
        if (logging) {
            return;
        }
        logging = true;
        try {
            await invoke('social.login', {
                checkFromServer: true,
                loginSource: LoginSource,
            });
            await sleep(100);
            if (landingUrl) {
                window.location.replace(landingUrl);
            } else {
                window.location.reload();
            }
        } catch (e) {
            // @ts-expect-error
            const resultCode = e?.code ?? 0;
            // 命中6001、6002token失效
            if (LoginExceptionCode.includes(resultCode)) {
                // 如果没尝试重新刷登录的话执行登出然后再登录
                // if (!hasTryRelogin) {
                //     await invoke('social.logout');
                //     doLogin(landingUrl);
                //     hasTryRelogin = true;
                // } else {
                //     await invoke('ui.showToast', {
                //         type: 'normal',
                //         text: '登录失败，请退出页面重新登录后进入活动',
                //     });
                // }
                await invoke('ui.showToast', {
                    type: 'normal',
                    text: '登录失败，请退出页面重新登录后进入活动',
                });
            } else {
                await invoke('ui.showToast', {
                    type: 'normal',
                    text: '登录失败，请重新登录',
                });
            }
        } finally {
            logging = false;
        }
    }

    return {
        doLogin,
    };
}
