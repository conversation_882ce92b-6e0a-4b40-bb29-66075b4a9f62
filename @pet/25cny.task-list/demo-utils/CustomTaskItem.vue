<script lang="ts" setup>
import { computed, toRef, watch } from 'vue-demi';

import Button from '@pet/adapt.button/index.vue';
import type { TaskTimeLimitedInfo } from '@pet/work.task-list-core/utils/useTimeLimitedTask';
import { useTimeLimitedTaskCountdown } from '@pet/work.task-list-core/utils/useTimeLimitedTask';

import TaskItem from '../components/TaskItem.vue';
import { DEFAULT_ICON } from '@pet/work.task-list-core/utils/const';
import type { TaskInfo, TaskActionEventInfoSet } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';

const props = defineProps<{
    task: TaskInfo;
    isLoading: boolean;
    disabled: boolean;
    doTask: (eventInfo?: TaskActionEventInfoSet) => void;
    taskList: TaskInfo[];
    index: number;
    timeLimitedInfo: TaskTimeLimitedInfo;
}>();

const iconUrl = computed(() => {
    return Boolean(props.task.icons[0]) ? props.task.icons[0] : DEFAULT_ICON;
});

const handleClick = () => {
    props.doTask();
};

const { countDownInfo, isTimeLimitedTask } = useTimeLimitedTaskCountdown(toRef(props, 'timeLimitedInfo'));
const countDownText = computed(
    () => `${countDownInfo.hour.value}:${countDownInfo.minute.value}:${countDownInfo.second.value}`,
);

function handleDoTask(eventInfo?: TaskActionEventInfoSet) {
    props.doTask(eventInfo);
}
</script>

<template>
    <div :key="task.id" :class="{ 'normal-task-card-wrapper': !isTimeLimitedTask }">
        <div v-if="isTimeLimitedTask" class="task-item">
            <img class="task-item-icon" :src="iconUrl" alt="" />
            <div class="task-item-title">{{ task.title }}</div>
            <div class="task-item-desc">{{ task.description }}</div>
            <Button class="task-item-action" :disabled="disabled" @click="handleClick">
                {{ task.btnText }}
            </Button>
            <div v-if="isTimeLimitedTask">限时任务倒计时{{ countDownText }}</div>
        </div>
        <TaskItem
            v-else
            :time-limited-info="timeLimitedInfo"
            :disabled="disabled"
            :is-loading="isLoading"
            :task="task"
            :index="index"
            @doTask="handleDoTask"
        ></TaskItem>
    </div>
</template>

<style lang="stylus" scoped>
.normal-task-card-wrapper
    width 100%
.task-item
    display flex
    flex-direction column
    width 400px
    flex-shrink 0
    align-items center
    justify-content center
    margin 0 !important
    & + &
        margin-right 20px !important

    &-icon
        width 100px
        height 100px

    &-title
        font-size 30px
        font-weight 600
        color #333
        white-space nowrap
        text-overflow ellipsis
        overflow hidden

    &-desc
        font-size 20px
        color #666
        white-space nowrap
        text-overflow ellipsis
        overflow hidden

    &-short
        width 50%
    &-long
        width 100%
</style>
