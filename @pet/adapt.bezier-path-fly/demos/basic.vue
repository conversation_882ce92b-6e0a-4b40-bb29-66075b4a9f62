<pet-info lang="json">
{ "title": "基本使用", "description": "默认参数的展现形式" }
</pet-info>
<script setup lang="ts">
import { ref, nextTick } from 'vue-demi';
import Pilot from '../index.vue';

type PilotType = {
    boot: () => void;
};
const show = ref(true);
const targetId = ref('');
const pilotRef = ref<PilotType | null>(null);

const fly = (id: string) => {
    targetId.value = id;
    show.value = true;
    nextTick(() => {
        pilotRef.value?.boot();
    });
};

const flyEnd = () => {
    show.value = false;
    setTimeout(() => {
        show.value = true;
    }, 1000);
};
</script>

<template>
    <div class="container">
        <div>不传bezier参数时，使用内置的bezier参数，按以下规则</div>
        <div>200px内，使用飞左、右的bazier-path路径参数</div>
        <button @click="fly('top-left')">飞左上</button>
        <button @click="fly('top-center')">飞上中</button>
        <button @click="fly('top-right')">飞右上</button>
        <button @click="fly('left')">飞左中</button>
        <button @click="fly('right')">飞右中</button>
        <button @click="fly('bottom-left')">飞左下</button>
        <button @click="fly('bottom-center')">飞下中</button>
        <button @click="fly('bottom-right')">飞右下</button>
        <div class="wrapper">
            <Pilot v-if="show" ref="pilotRef" :fly-to-target="targetId" @end="flyEnd">
                <div class="start">起始点</div>
            </Pilot>
        </div>
        <div id="top-left" class="target top-left">左上</div>
        <div id="top-center" class="target top-center">上中</div>
        <div id="top-right" class="target top-right">右上</div>
        <div id="left" class="target left">左</div>
        <div id="right" class="target right">右</div>
        <div id="bottom-left" class="target bottom-left">左下</div>
        <div id="bottom-center" class="target bottom-center">下中</div>
        <div id="bottom-right" class="target bottom-right">右下</div>
    </div>
</template>

<style>
html,
body {
    margin: 0;
}
</style>

<style lang="scss" scoped>
.container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

div {
    font-size: 16px;
}

.start,
.target {
    width: 200px;
    height: 200px;
}

.wrapper {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.start {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: aqua;
}

.target {
    font-size: 10px;
    background-color: sandybrown;
    position: absolute;
    width: 20px;
    height: 20px;
}

.top-left {
    top: 80px;
    left: 60px;
}

.top-right {
    top: 80px;
    right: 60px;
}

.top-center {
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
}

.left {
    top: 450px;
    left: 20px;
}

.right {
    top: 450px;
    right: 20px;
}

.bottom-left {
    bottom: 60px;
    left: 60px;
}

.bottom-right {
    bottom: 60px;
    right: 60px;
}

.bottom-center {
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
}
</style>
