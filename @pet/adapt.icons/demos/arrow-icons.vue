<pet-info lang="json">
{ "title": "箭头", "description": "所有箭头", "priority": 77 }
</pet-info>

<script setup lang="ts">
import ArrowIcon from '../arr-svg.vue';
import ArrowSolidIcon from '../arr-solid-svg.vue';
import ArrowLineIcon from '../arr-line-svg.vue';
</script>

<template>
    <div>
        <div class="item">
            <ArrowIcon class="icon icon-big-1" />
            <h4>
                <p>arr-svg.vue</p>
            </h4>
        </div>
        <div class="item">
            <ArrowSolidIcon class="icon icon-big-2" />
            <h4>
                <p>arr-solid-svg.vue</p>
            </h4>
        </div>
        <div class="item">
            <ArrowLineIcon class="icon icon-big-2" />
            <h4>
                <p>arr-line-svg.vue</p>
            </h4>
        </div>
    </div>
</template>

<style src="../assets/css/demo.scss" lang="scss" scoped></style>

<style lang="scss" scoped>
.icon-big-1 {
    width: 12px;
    height: 12px;
}

.icon-big-2 {
    width: 20px;
    height: 20px;
}
</style>
