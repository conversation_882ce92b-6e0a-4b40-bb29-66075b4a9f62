<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import StrokeText from '@pet/adapt.stroke-text/index.vue';
import Picture from '@pet/quantum.PictureImg';
import { computed } from 'vue';

import tailIcon1 from '@/assets/forward-rush/bubble-progress-icon-1.png?preset=modern';
import tailIcon2 from '@/assets/forward-rush/bubble-progress-icon-2.png?preset=modern';
import iconPrize from '@/assets/progress/icon-gift.png';
import { useConfigModel } from '@/models/configModel';
import { useHomeModel } from '@/models/homeModel';

import { GoalStatusEnum } from './config';
const EffectIndex = defineAsyncComponent(() => import('@effect/effect_9537/EffectIndex.vue'));

const props = defineProps<{ mainText: string; stepCount: number; progressDuration: number; progress: string }>();

const { homeData } = useHomeModel();
const { effectShowStatus } = useDowngradeLevel();
const { bubbleOptimize } = useConfigModel();

const product = computed(() => homeData.value?.signInHomeView?.product);

const productIcon = computed(() => product.value?.productIcon ?? iconPrize);

/** 步数高亮提取 */
const stepDesc = computed(() => {
    const text = props.mainText;
    const matched = text.match(/\{0\}/g)?.[0];
    if (matched) {
        const matchedIndex = text.indexOf(matched);
        return {
            pre: text.slice(0, matchedIndex),
            matched: props.stepCount + '步',
            post: text.slice(matchedIndex + matched.length),
        };
    }
    return {
        pre: text,
        matched: '',
        post: '',
    };
});

// 动画
const aniWidth = computed(() => `width ${props.progressDuration ?? 360}ms cubic-bezier(0.333, 0.167, 0.166, 0.833)`);
const status = ref(GoalStatusEnum.Loop);
onMounted(() => {
    status.value = GoalStatusEnum.Appear;
});
watch(productIcon, () => {
    status.value = GoalStatusEnum.Appear;
});
</script>
<template>
    <div class="common-bg step-bubble">
        <div class="bubble-rush-text">
            <StrokeText :text="bubbleOptimize.bubbleTitle ?? ''" class="bubble-rush-text-title" />
            <EffectIndex v-if="effectShowStatus.L1" v-model:status="status" :goal-url="productIcon" />
            <img v-else :src="productIcon" class="bubble-rush-product" />
        </div>
        <div class="bubble-progress">
            <div class="bubble-progress-text">
                <span class="bubble-text">{{ stepDesc.pre }}</span>
                <span class="bubble-text bubble-text-highlight">{{ stepDesc.matched }}</span>
                <span class="bubble-text">{{ stepDesc.post }}</span>
            </div>
            <div class="bubble-progress-bar">
                <div class="bubble-progress-real" :style="{ width: progress, transition: aniWidth }">
                    <div class="tail">
                        <Picture class="tail-icon-1" :src="tailIcon1" />
                        <Picture class="tail-icon-2" :src="tailIcon2" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
@use './bubble.scss';
@use './bubble-optimize.scss';

.step-bubble {
    width: 212px;
    height: 119px;
    display: flex;
    flex-direction: column;
    position: relative;
    bottom: -8px;
    /* 文字字体 */
    --adapt-stroke-text-font-family: KuaiYuanHuiTi;
    /* 文字字号 */
    --adapt-stroke-text-font-size: 14px;
    /* 文字描边宽度 */
    --adapt-stroke-text-stroke-width: 0px;
    /* 文字颜色 */
    --adapt-stroke-text-color: linear-gradient(271.07deg, #000000 0.92%, #4e010a 76.82%, #ab2132 100.26%);
    /* 文字描边色 */
    --adapt-stroke-text-stroke-color: transparent;
    /* 文字倾斜角度 */
    --adapt-stroke-text-skew: matrix(1, 0, -0.21, 0.98, 0, 0);
    /* 文字间距 */
    --adapt-stroke-text-letter-spacing: 0;
    --adapt-stroke-text-line-height: 100%;
    .bubble-rush-text {
        position: relative;
        display: flex;
        width: 100%;
        flex-direction: row;
        height: 34px;
        background: linear-gradient(290.57deg, #e0ff95 24.4%, #6aff00 97.94%);
        border-radius: 18px 18px 0 0;
        align-items: center;
        .bubble-rush-text-title {
            margin: auto 0 auto 13px;
        }
        .bubble-rush-product {
            width: 60px;
            height: 60px;
            position: absolute;
            right: 2px;
            top: -17px;
        }
    }
    .bubble-progress {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        height: 79px;
        position: relative;
        @include bg('@/assets/forward-rush/bubble-bg-optimize.png');
        background-position: center bottom;
        background-size: 100%;
        z-index: 1;
        .bubble-progress-text {
            display: flex;
            flex-direction: row;
            justify-content: center;
            width: 100%;
            position: relative;
            top: 10px;
        }
        .bubble-progress-bar {
            position: relative;
            top: 22px;
            width: 162px;
            height: 10px;
            border-radius: 12px;
            background-color: #0000001a;
            overflow: hidden;
            .bubble-progress-real {
                width: 100%;
                height: 10px;
                border-radius: 12px;
                background: linear-gradient(273.58deg, #ffcd4f 7.43%, #fe3666 47.79%, #fe3666 94.81%);
                position: relative;
                overflow: hidden;
                .tail {
                    width: 21px;
                    height: 10px;
                    background: linear-gradient(271.95deg, #f1fa88 1.85%, rgba(241, 250, 136, 0) 71.7%);
                    position: absolute;
                    right: 0;
                    top: 0;
                    .tail-icon-1 {
                        position: absolute;
                        width: 6px;
                        height: 5px;
                        right: 1px;
                        top: 0;
                    }
                    .tail-icon-2 {
                        position: absolute;
                        width: 3px;
                        height: 2.5px;
                        top: 4px;
                        right: 2px;
                    }
                }
            }
        }
    }
}
</style>
