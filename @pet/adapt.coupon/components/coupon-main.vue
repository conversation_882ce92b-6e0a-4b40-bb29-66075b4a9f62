<script setup lang="ts">
import { computed } from 'vue-demi';

import type { CouponSize } from '../types';
import CardButton from './coupon-button.vue';

interface CardMainProps {
    title?: string;
    desc?: string;
    size?: CouponSize;
    icon?: string;
    attach?: string;
    showBtn?: boolean;
    overdue?: boolean;
    btnText?: string;
    btnDisabled?: boolean;
    btnLoading?: boolean;
    value?: string;
    ogValue?: string;
}
const props = withDefaults(defineProps<CardMainProps>(), {
    size: 'small',
    showBtn: false,
    btnText: '立即使用',
});
const emit = defineEmits<{
    (event: 'btn-click'): void;
}>();

const mainSize = computed(() => `main-${props.size === 'mini' ? 'mini' : 'normal'}`);
const withBtn = computed(() => props.showBtn && 'has-btn');
const isOut = computed(() => props.overdue === true && 'is-out');
function handleClick() {
    emit('btn-click');
}
</script>

<template>
    <div class="coupon-card-main" :class="[mainSize, withBtn, isOut]">
        <h4 class="main-title">
            <span v-if="icon" class="main-title-icon">
                <img :src="icon" />
            </span>
            <div class="text-cut">
                {{ title }}
            </div>
        </h4>
        <div v-if="props.value" class="main-desc">
            <span class="price-text">
                <span class="price-unit">￥</span><span class="price-num">{{ props.value }}</span>
            </span>
            <span class="price-text-og"><span class="price-unit-og">￥</span>{{ props.ogValue }}</span>
        </div>
        <div v-else class="main-desc text-cut">{{ desc }}</div>
        <div v-if="attach || showBtn" class="main-attach">
            <CardButton
                v-if="showBtn"
                type="line"
                :size="size"
                :btn-text="btnText"
                :btn-disabled="btnDisabled"
                :btn-loading="btnLoading"
                @btn-click="handleClick"
            />
            <div v-else class="text-cut">{{ attach }}</div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
$main-title-color: #222;
$main-title-icon-border-color: #e6e6e6;
$main-out-color: #9c9c9c;
$coupon-value-color: #ff1b46;
$coupon-value-og: #b5b5b6;
.coupon-card-main {
    text-align: left;
    overflow: hidden;
    color: $main-title-color;
    padding: 1px 0;
    &.is-out {
        color: $main-out-color;
    }
}
.main-title {
    display: flex;
    align-items: center;
    margin: 0;
    font-weight: 700;
}
.text-cut {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.main-title-icon {
    margin-right: 4px;
    flex-shrink: 0;
    font-size: 0;
    img {
        display: block;
        box-sizing: border-box;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 0.5px solid $main-title-icon-border-color;
        object-fit: cover;
    }
}
.main-desc {
    &:empty {
        display: none;
    }
}
.main-normal {
    .main-title {
        font-size: 14px;
        line-height: 18px;
    }
    .main-desc,
    .main-attach {
        font-size: 10px;
        line-height: 14px;
    }
}
.main-mini {
    .main-title {
        font-size: 12px;
        line-height: 16px;
    }
    .main-desc,
    .main-attach {
        font-size: 8px;
        line-height: 12px;
        // color: $main-desc-color;
    }
}
/* 内部子元素继承 */
/* stylelint-disable-next-line no-duplicate-selectors */
.coupon-card-main {
    .main-desc {
        margin-top: 8px;
    }
    .main-desc + .main-attach {
        margin-top: 4px;
    }
    &.has-btn {
        .main-desc {
            margin-top: 4px;
        }
        .main-desc + .main-attach {
            margin-top: 6px;
        }
    }
}

.price-text-og {
    color: $coupon-value-og;
    font-size: 14px;
    line-height: 1;
    font-weight: 700;
    text-decoration: line-through;
}
.price-unit-og {
    margin-right: -2px;
}

.price-text {
    color: $coupon-value-color;
    font-weight: 700;
    & + .price-text-og {
        margin-left: 4px;
    }
}
.price-unit {
    font-size: 14px;
    line-height: 1;
}
.price-num {
    font-family: 'DIN Alternate', sans-serif;
    font-size: 20px;
}
</style>
