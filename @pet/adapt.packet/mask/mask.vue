<script setup lang="ts">
import { useEventListener } from '@vueuse/core';
import { computed, ref } from 'vue-demi';

interface Props {
    transparent?: boolean;
    background?: string;
}

const props = withDefaults(defineProps<Props>(), {
    transparent: false,
});

const emit = defineEmits<{
    (event: 'click'): void;
}>();

const maskStyle = computed(() => {
    return {
        background: props.transparent ? 'transparent' : props.background,
    };
});

const maskMain = ref<HTMLElement | null>(null);

function preventTouchMove(e: Event) {
    e.preventDefault();
}

useEventListener(maskMain, 'touchmove', preventTouchMove, { passive: false });
</script>

<template>
    <div ref="maskMain" class="mask" :style="maskStyle" @click="$emit('click')"></div>
</template>

<style lang="scss" scoped>
.mask {
    position: fixed;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: rgba(0, 0, 0, 0.85);
}
</style>
