import { createUseModel } from '@gundam/model';

import { useCityInpushApiModel } from '../api/city/inpushApiModel';

export const useCityInpushModel = createUseModel(() => {
    const { data: inpushData, refreshInpush, pausePolling, resumePolling } = useCityInpushApiModel();

    const inpushList = computed(() => inpushData?.inpushList ?? []);

    return {
        inpushData,
        refreshInpush,
        pausePolling,
        resumePolling,
        inpushList,
    };
});
