import { createModel, useModel } from '@gundam/model';
import { toast } from '@pet/adapt.toast';
import { isAndroid, isIOS } from '@pet/yau.core';

import { useLogger } from '@/init/logger';
import { addOneCalendar, changeEventForCalendar, checkCalendarAuth } from '@/utils/calendar';

import { useHomeApiModel } from './api/homeApiModel';
import { calendarModel } from './calendarModel';
import { CalendarMethod, CalendarRes } from './calendarModel.type';
import { homeModel } from './homeModel';
import { TaskModel } from './taskModel';

const delimiter = '#';
export const localCalendarModel = createModel(({ getModelInstance }) => {
    const isChecked = ref(false);
    const isLoading = ref(false);
    const localEventIds = ref('');
    const init = ref(false);
    const clickedFlag = ref(false);
    const { sendClick } = useLogger();

    const { data } = useHomeApiModel();
    const calendarEvents = computed(() => data.value?.signInHomeView?.calendarEventViewList ?? []);

    // 添加日历提醒
    // eslint-disable-next-line sonarjs/cognitive-complexity
    const addCalendar = async (options?: { needRefreshHome?: boolean; needRefreshTask?: boolean }) => {
        // 默认刷新主接口
        const needRefreshHome = options?.needRefreshHome ?? true;
        const needRefreshTask = options?.needRefreshTask ?? true;
        // 临时写法，等客户端能力支持后优化：获取用户权限失败，则不触发后续写入事件
        const firstEventId = await addOneCalendar(calendarEvents.value[0]);
        if (!firstEventId) {
            console.log('add calendar fail---firstEventId');
            return false;
        }
        return Promise.all(
            calendarEvents.value.slice(1).map((event: any) => {
                return changeEventForCalendar({
                    method: CalendarMethod.ADD,
                    event: {
                        type: 1,
                        ...event,
                        endDay:
                            typeof event?.endDay === 'number' && isAndroid() && event?.endDay !== event?.startDay
                                ? event?.endDay - 1000 * 60 * 60 * 24
                                : event?.endDay,
                    },
                });
            }),
        ).then(async (bridgeResults) => {
            const [succBridgeResults, failBridgeResults] = bridgeResults.reduce(
                ([succs, fails], bridgeResult) => {
                    console.log('bridgeResult', bridgeResult);
                    if (Boolean(bridgeResult)) {
                        const { result, eventId } = bridgeResult;
                        console.log('result', result, !eventId, result !== CalendarRes.SUCCESS);
                        const fail = result !== CalendarRes.SUCCESS || !eventId;
                        if (fail) {
                            console.log('fail', result, eventId);
                            fails.push(bridgeResult);
                        } else {
                            succs.push(bridgeResult);
                        }
                        return [succs, fails];
                    }
                    return [succs, fails];
                },
                [[] as typeof bridgeResults, [] as typeof bridgeResults],
            );

            if (failBridgeResults.length) {
                // bridge有添加失败的情况，将添加成功的删除
                [firstEventId, ...succBridgeResults.map((item: any) => item.eventId)].forEach((eventId) => {
                    changeEventForCalendar({
                        method: CalendarMethod.DELETE,
                        event: {
                            type: 1,
                            eventId,
                        },
                    });
                });
                return false;
            }
            const eventIds = [firstEventId, ...bridgeResults.map(({ eventId }) => eventId)].join(delimiter);
            const { result } =
                (await getModelInstance(calendarModel)?.saveCalendarEventId({
                    eventId: eventIds,
                })) ?? {};
            if (result === 1) {
                if (needRefreshHome) {
                    getModelInstance(homeModel)?.refreshHome('LUCK_SHAKE_SUDOKU');
                }
                if (needRefreshTask) {
                    // 因为服务端更新了添加日历的状态，这里要刷新一下任务列表更新下添加日历任务
                    getModelInstance(TaskModel)?.tasksRefetch();
                }
                localEventIds.value = eventIds;
            } else {
                // 服务端有存储失败的情况（比如获取did为空），将添加成功的删除
                [firstEventId, ...succBridgeResults.map((item: any) => item.eventId)].forEach((eventId) => {
                    changeEventForCalendar({
                        method: CalendarMethod.DELETE,
                        event: {
                            type: 1,
                            eventId,
                        },
                    });
                });
            }
            return result === 1;
        });
    };

    const showAddCalendarToast = (addSucc: boolean, isAuth?: boolean) => {
        const failedToast =
            isIOS() && !isAuth
                ? '开启打卡提醒失败，请去【设置】内开启日历权限后再开启'
                : '开启打卡提醒失败，请稍后再试';
        toast(addSucc ? '已开启打卡提醒' : failedToast);
    };

    // 添加日历提醒(避免重复)
    const checkAndAddCalendar = async (options: {
        needRefreshHome: boolean;
        needRefreshTask?: boolean;
        checkIOSAuth?: boolean;
    }) => {
        if (!isChecked.value) {
            const isAuth = await checkCalendarAuth();
            // const checkIOSAuth = options.checkIOSAuth ?? true;

            // let addSucc = false;
            // if (checkIOSAuth) {
            //     // iOS下，如果没有授权，则不触发后续写入事件
            //     addSucc = isIOS() ? isAuth && (await addCalendar(options)) : await addCalendar(options);
            // } else {
            //     addSucc = await addCalendar(options);
            // }
            const addSucc = await addCalendar(options);

            showAddCalendarToast(addSucc, isAuth);
            if (addSucc) {
                sendClick('OP_ACTIVITY_CALENDER_SUCCESS', {});
            }
            isChecked.value = addSucc;
            isLoading.value = false;
            return addSucc;
        } else {
            sendClick('OP_ACTIVITY_CALENDER_SUCCESS', {});
            // 兜底逻辑, 直接弹已开启 toast
            showAddCalendarToast(true, true);
            return true;
        }
    };

    return {
        init,
        isChecked,
        isLoading,
        localEventIds,
        clickedFlag,
        checkAndAddCalendar,
    };
});

export const useLocalCalendarModel = () => useModel(localCalendarModel);
