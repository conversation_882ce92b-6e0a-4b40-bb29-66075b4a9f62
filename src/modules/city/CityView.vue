<script lang="ts" setup>
import { useGuideState } from '@pet/25cny.guide-directive';
import Frame from '@pet/adapt.frame/index.vue';
import { useTopGap } from '@pet/adapt.status-bar/hooks';
import TaskComponentsProvide from '@pet/adapt.task-queue/TaskComponentsProvide.vue';
import { isAtFourTab, isAtSearchTab } from '@pet/yau.core';
import { whenever } from '@vueuse/core';

import ChargeForward from '@/components/city/charge-forward/index.vue';
import EarnCard from '@/components/city/earn-card/index.vue';
import Footer from '@/components/city/footer/FooterView.vue';
import CityHeader from '@/components/city/header/index.vue';
import SlotWidgetView from '@/components/city/slot/SlotWidgetView.vue';
import { useAudioModel } from '@/models/audioModel';
import { useConfigModel } from '@/models/city/configModel';
import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';
import { useCityInpushModel } from '@/models/city/inpushModel';
import { useProcessModel } from '@/models/city/processModel';
import { usePopupModel } from '@/models/popup.model';
import { summerLocalStore } from '@/utils/localStore';
import { inHeadless } from '@/utils/ssg';

const { themeConfig } = useConfigModel();
const { addEventTask, resetQueue } = usePopupModel();
const isFourTabMode = isAtFourTab();
const isSearchTabMode = isAtSearchTab();

useCityInpushModel(); // 处理inpush

const { initBgm, clearAllMusic } = useAudioModel();
const { autoScroll, backgroundStyle } = useForwardRushGridModel();
const { initGuideDirective } = useGuideState();

const { statusBarHeight } = useTopGap(); // 处理顶部间隙

useProcessModel(); // 主流程

// 弱引导初始化
initGuideDirective({ store: summerLocalStore, addEventTask, isDebug: true });

onMounted(() => {
    if (autoScroll.value) {
        document.documentElement.style.overflow = 'auto';
        document.documentElement.style.height = `${
            (isFourTabMode ? 8.13 : isSearchTabMode ? 7.67 : 7.36) *
            (Number(window.getComputedStyle(document.documentElement).getPropertyValue('font-size').split('px')[0]) * 1)
        }px`;
        // 滚动到 forward-btn 元素的下边界
        const forwardBtnElement = document.querySelector('.forward-btn');
        if (forwardBtnElement) {
            const rect = forwardBtnElement.getBoundingClientRect();
            const scrollToPosition = rect.bottom - window.innerHeight;
            document.documentElement.scrollTo({
                top: scrollToPosition,
                behavior: 'smooth',
            });
        }
    }
    initBgm();
});

// 设置 css 变量
whenever(
    themeConfig,
    (theme) => {
        const setCssVar = (name: string, value: string) => {
            document.documentElement.style.setProperty(name, value);
        };
        setCssVar('--status-bar-height', `${statusBarHeight}px`);
        setCssVar('--city--theme-color', theme.themeColor);
        setCssVar('--city--header-back-mask-background', theme.headerBackMaskBackground);
        setCssVar('--city--footer-back-mask-background', theme.footerBackMaskBackground);
        setCssVar('--city--header-back-mask-background--offset', !isFourTabMode && isSearchTabMode ? '-30px' : '-1px');
    },
    { immediate: true },
);

onUnmounted(() => {
    clearAllMusic();
    resetQueue();
});
</script>

<template>
    <Frame
        :class="[
            'app-frame',
            autoScroll
                ? isFourTabMode
                    ? 'app-frame-full-height-fTab'
                    : isSearchTabMode
                      ? 'app-frame-full-height-sTab'
                      : 'app-frame-full-height'
                : '',
        ]"
    >
        <div
            v-if="autoScroll"
            class="top-gap"
            :style="{
                height: `${statusBarHeight}px`,
            }"
        ></div>
        <!-- 顶部：按钮、标题 -->
        <div class="header-container">
            <CityHeader />
        </div>
        <!-- 收益卡片 -->
        <EarnCard />

        <!-- 主要游戏区 -->
        <div class="main-container">
            <!-- 顶部状态栏占位，和顶部背景遮罩 -->
            <div class="placeholder">
                <!-- 头部的渐变遮罩 -->
                <div class="header-back-mask" />
            </div>
            <!-- 槽位 -->
            <SlotWidgetView :style="backgroundStyle" />
            <div class="game-container">
                <ChargeForward />
            </div>
        </div>
        <!-- 底部：按钮操作区 -->
        <Footer></Footer>
    </Frame>
    <TaskComponentsProvide v-if="!inHeadless()" />
</template>

<style lang="scss" scoped>
// 预期页面不能滚动
.app-frame {
    position: relative;
    box-sizing: border-box;
    overflow: hidden;

    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    .top-gap {
        background-color: var(--city--theme-color);
        width: 414px;
        position: fixed;
        z-index: 101;
        top: 0px;
    }
    .header-container {
        position: relative;
        width: 100%;
        z-index: 100;

        .floating-view {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
    }

    .main-container {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;

        .placeholder {
            background-color: var(--city--theme-color);

            // 头部背景遮罩
            .header-back-mask {
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: var(--city--header-back-mask-background--offset);
                    z-index: 98;

                    width: 100%;
                    height: 267px;
                    background: var(--city--header-back-mask-background);
                }
            }
        }

        // 游戏区
        .game-container {
            // box-sizing: border-box;
            // border: 2px solid red;
            position: relative;
            flex: 1;
            overflow: hidden;
        }
    }

    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 218px;
        background: var(--city--footer-back-mask-background);
        z-index: 99;
    }
}
.app-frame-full-height-sTab {
    height: 767px !important;
}
.app-frame-full-height-fTab {
    height: 813px !important;
}
.app-frame-full-height {
    height: 736px !important;
}
</style>
