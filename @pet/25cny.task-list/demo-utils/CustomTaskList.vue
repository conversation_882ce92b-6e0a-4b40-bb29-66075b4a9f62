<script lang="ts" setup>
import { computed } from 'vue-demi';

import { useTask } from '@pet/work.task-list-core/useTask';
import { createExecutor } from '@pet/work.task-list-core/utils/framework/DefaultImpl/TriggerFlowModules/ExecuteModule/createExecutor';
import type { TaskActionShare, TaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';

const taskListConfig = {
    subBizId: 3,
    executor: createExecutor({
        emit: async (event, taskInfo) => {
            console.log('emit');
            console.log(event, taskInfo);
            return;
        },
        share: async (event, taskInfo) => {
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
        shareByAvatar: async (event, taskInfo) => {
            // 关系链任务点击头像，可能是直接拉微信，可能是拉分享面板
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
    }),
};

const taskListCollection = useTask(taskListConfig);
const taskList = computed(() => taskListCollection.taskList.value);

function doTask(task: TaskInfo) {
    console.log('Doing task id: ' + task.id.toString());
    taskListCollection.doTask(task);
}
</script>

<template>
    <div>
        <div v-for="task in taskList" :key="task.id">
            <div>id: {{ task.id }}</div>
            <div>name: {{ task.title }}</div>
            <div>status: {{ task.status }}</div>
            <div>
                task polling:
                {{ taskListCollection.pendingTaskIds.value.includes(task.id) }}
            </div>
            <div>
                <button @click="doTask(task)">
                    {{ task.toastText || '去完成' }}
                </button>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
