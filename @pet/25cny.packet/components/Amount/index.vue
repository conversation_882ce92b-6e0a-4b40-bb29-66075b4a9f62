<script setup lang="ts">
import { computed } from 'vue-demi';
import { px2rem } from '@pet/core.mobile';

export interface AmountParams {
    prizeType: 'amount';
    amount: string;
    unit: string;
    numberDesc?: string;
    tag?: string;
    desc?: string;
    descIcon?: string;
}

const props = defineProps<AmountParams>();

// 是否有数字的描述，要用两套样式
const hasNumberDesc = computed(() => {
    return !!props.numberDesc;
});

const amountStyle = computed(() => {
    let amountSize;
    if (props.amount.length < 4) {
        amountSize = 80;
    } else if (props.amount.length === 4) {
        amountSize = hasNumberDesc.value ? 64 : 80;
    } else if (props.amount.length === 5) {
        amountSize = hasNumberDesc.value ? 62 : 72;
    } else {
        amountSize = hasNumberDesc.value ? 56 : 62;
    }

    return {
        amountSize,
        amountStyle: `amount-style-${amountSize}`,
    };
});
</script>

<template>
    <div class="amount-wrapper">
        <div
            :class="['amount-box', hasNumberDesc && 'with-number-desc', amountStyle.amountStyle]"
            :style="{
                height: `${px2rem(hasNumberDesc ? 64 : 80)}`,
            }"
        >
            <div
                class="amount"
                :style="{
                    fontSize: `${px2rem(amountStyle.amountSize)}`,
                }"
            >
                {{ amount }}
            </div>
            <div class="relative-unit">
                <div v-if="tag" class="tag">
                    <span>{{ tag }}</span>
                </div>
                <div class="unit">{{ unit }}</div>
            </div>
        </div>
        <div v-if="numberDesc" class="number-desc">{{ numberDesc }}</div>
        <div v-if="desc" class="desc">
            <span v-if="descIcon" class="icon" :style="{ backgroundImage: `url(${descIcon})` }"></span
            ><span class="text">{{ desc }}</span>
        </div>
    </div>
</template>

<style>
:root {
    --biz-base-packet-amount-color: #f7211d;
    --biz-base-packet-amount-tag-bg-color: #ffffff;
    --biz-base-packet-amount-desc-color: #550000;
}
</style>

<style lang="scss" scoped>
.amount-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 20px;

    .amount-style-80 {
        .unit {
            bottom: 6px;
            left: 3px;
        }

        .tag {
            bottom: 14px;
            left: 3px;
        }
    }

    .amount-style-72 {
        .unit {
            bottom: 10px;
            left: -2px;
        }

        .tag {
            bottom: 12px;
        }
    }
    .amount-style-64 {
        .unit {
            bottom: 10px;
        }

        .tag {
            bottom: 14px;
        }
    }
    .amount-style-62 {
        .unit {
            bottom: 14px;
            left: 3px;
        }

        .tag {
            bottom: 14px;
            left: 3px;
        }
    }

    .amount-style-56 {
        .unit {
            bottom: 14px;
            left: 1px;
        }

        .tag {
            bottom: 14px;
            left: 1px;
        }
    }

    .amount-box {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        .amount {
            overflow: hidden;
            color: var(--biz-base-packet-amount-color);
            font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
            font-weight: bold;
            transform: skewX(-7deg);
            line-height: 80px;
        }

        .relative-unit {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            height: 80px;
            margin-left: 4px;

            .tag {
                position: relative;
                display: flex;

                span {
                    display: flex;
                    height: 20px;
                    align-items: center;
                    font-size: 11px;
                    padding: 0 6px;
                    color: var(--biz-base-packet-amount-tag-bg-color);
                    background-color: var(--biz-base-packet-amount-color);
                    border-radius: 16px;
                    border-bottom-left-radius: 0;
                    font-weight: bold;
                }
            }

            .unit {
                position: relative;
                font-weight: bold;
                font-size: 22px;
                color: var(--biz-base-packet-amount-color);
            }
        }
    }

    .number-desc {
        width: 140px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 22px;
        line-height: 22px;
        font-size: 14px;
        opacity: 0.6;
        color: #550000;
    }

    .with-number-desc {
        height: 64px;
        margin-top: -6px;

        .amount {
            line-height: 64px;
        }

        .relative-unit {
            .unit {
                font-size: 18px;
            }
        }

        .amount-style-62 {
            .unit {
                bottom: 14px;
                left: 3px;
            }
        }
    }

    .desc {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 20px;
        font-size: 14px;
        color: var(--biz-base-packet-amount-desc-color);

        .text {
            display: inline-block;
            max-width: 240px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .icon {
            display: inline-block;
            height: 16px;
            width: 16px;
            margin-right: 4px;
            background: no-repeat center/100%;
        }
    }
}
</style>
