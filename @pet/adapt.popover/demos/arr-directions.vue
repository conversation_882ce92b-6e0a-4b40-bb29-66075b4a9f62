<pet-info lang="json">
{ "title": "尖角方向", "description": "定位尖角处于气泡的方向" }
</pet-info>

<script setup lang="ts">
import type { ArrDirection, ArrAlign } from '@pet/adapt.tool-tip/types';
import { ref, computed } from 'vue-demi';

import Popover from '../index.vue';
import type { AnimationType } from '../types';
const show = ref(false);
function handleShow() {
    show.value = !show.value;
}

const arrDirection: ArrDirection = 'bottom';
const arrAlign: ArrAlign = 'center';
const arrDistance = ref(-1);
const bgColor = ref('');
const color = ref('');
const animationType: AnimationType = 'scale-in-out';
const arrDirections = ['top', 'right', 'bottom', 'left'];
</script>

<template>
    <div class="demo">
        <div class="popover-wrapper">
            popover: {{ show }}
            <Popover
                :show="show"
                :animation-type="animationType"
                :arr-direction="arrDirection"
                :arr-align="arrAlign"
                :arr-distance="+arrDistance"
                :background="bgColor"
                :text-color="color"
                class="popover-main"
                @close="handleShow"
            >
                显示文案内容
            </Popover>
            <button @click="handleShow">{{ show ? '隐藏' : '显示' }}</button>
        </div>

        <section>
            <h5>arr-direction: {{ arrDirection }}</h5>
            <label v-for="direction in arrDirections" :key="direction">
                <input v-model="arrDirection" type="radio" name="direction" :value="direction" />{{ direction }}
            </label>
        </section>
    </div>
</template>

<style lang="scss">
body {
    font-size: 32px;
}
</style>

<style lang="scss" scoped>
.demo {
    text-align: center;
    font-size: 16px;
    padding-top: 100px;
}
label {
    display: inline-flex;
    justify-content: center;
}
.popover-wrapper {
    position: relative;
    height: 30px;
}
.popover-main {
    left: 160px;
    top: -40px;
}
</style>
