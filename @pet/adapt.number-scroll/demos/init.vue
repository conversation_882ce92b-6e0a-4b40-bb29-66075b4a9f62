<pet-info lang="json">
{
    "title": "初始效果",
    "description": "初始是不是展示数值"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const notShowValue = ref(true);

function numberChange() {
    notShowValue.value = !notShowValue.value;
}
</script>

<template>
    <div class="demo">
        <div class="container">
            <h6>:number-value="numberValueData"</h6>
            <NumberScroll
                :not-show-value="notShowValue"
                :font-size="36"
                :number-value="666"
                :animation-duration="300"
            />
        </div>
        <button @click="numberChange()">展示数值</button>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 48px 0 24px;
    font-size: 12px;
    text-align: center;
}
h5 {
    position: absolute;
}
</style>
