<pet-info lang="json">
{ "title": "吸底导航", "description": "预留提供一个吸底位置" }
</pet-info>
<script lang="ts" setup>
import { ref } from 'vue-demi';
import Button from '@pet/adapt.button/index.vue';
import Frame from '../index.vue';

const showFrame = ref(false);
</script>
<template>
    <div>
        <div v-if="showFrame" class="fake-view">
            <Frame :full-screen="false" :has-status-bar="false">
                <div class="main-content">中间高度自适应</div>
                <template #fixedFooter>
                    <div class="fixed-footer">吸底内容</div>
                </template>
            </Frame>
        </div>
        <Button :class="showFrame && 'button'" @click="showFrame = !showFrame">
            {{ !showFrame ? '显示布局' : '隐藏布局' }}
        </Button>
    </div>
</template>

<style scoped>
.fake-view {
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #fff;
    overflow: auto;
}

.main-content {
    height: 200vh;
    background: #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
}

.button {
    position: relative;
    z-index: 1;
}

.fixed-footer {
    width: 100%;
    height: 44px;
    background: #222;
    color: #fff;
    text-align: center;
    line-height: 44px;
}
</style>
