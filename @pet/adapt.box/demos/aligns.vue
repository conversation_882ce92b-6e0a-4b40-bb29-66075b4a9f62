<pet-info lang="json">
{ "title": "对齐", "description": "" }
</pet-info>
<script setup lang="ts">
import Box from '../box.vue';
import type { DirectionType, JustifyType, AlignType } from '../types';
const justifyValues: JustifyType[] = ['start', 'end', 'center', 'between', 'around', 'evenly'];
const alignValues: AlignType[] = ['start', 'end', 'center', 'baseline', 'stretch'];
</script>

<template>
    <div>
        <h4>主轴</h4>
        <span v-for="justify in justifyValues" :key="`justify-${justify}`" class="demo-wrapper">
            <h4>justify="{{ justify }}"</h4>
            <Box :justify="justify" class="box">
                <img
                    src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                    class="img"
                />
                <img
                    src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                    class="img"
                />
            </Box>
        </span>
        <h4>次轴</h4>
        <span v-for="align in alignValues" :key="`algin-${align}`" class="demo-wrapper">
            <h4>align="{{ align }}"</h4>
            <Box :align="align" class="box">
                <img
                    src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                    class="img"
                    :class="align === 'stretch' && 'stretch'"
                />
                <img
                    src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                    class="img"
                    :class="align === 'stretch' && 'stretch'"
                />
            </Box>
        </span>
    </div>
</template>

<style lang="scss" scoped>
img {
    &.stretch {
        width: auto;
        height: auto;
    }
}
.box {
    width: 200px;
    height: 200px;
    background: #f5f5f5;
    position: relative;
    overflow: hidden;
    &::before {
        position: absolute;
        top: 0;
        left: 0;
        content: 'flex-box';
        background: #999;
        color: #fff;
        font-size: 12px;
        padding: 2px 4px;
        border-radius: 0 0 5px 0;
    }
    & > img {
        position: relative;
        width: 60px;
        height: 60px;
    }
}

h4 {
    font-size: 18px;
    line-height: 28px;
    margin: 16px 0;
}
</style>
