{"$schema": "https://static.yximgs.com/udata/pkg/WEB-LIVE/pet/pet-config.schema-v1.json", "name": "@pet/25cny.task-list", "description": "", "version": "0.0.2", "type": "mobile", "targets": ["vue3"], "dependencies": {"@pet/adapt.avatar": "0.0.0", "@pet/adapt.button": "0.0.0", "@pet/adapt.card": "0.0.0", "@pet/adapt.empty-status": "0.0.0", "@pet/base.guide-directive": "1.0.0", "@pet/adapt.loading": "0.0.0", "@pet/adapt.toast": "0.0.0", "@pet/core.mobile": "0.0.0", "@ks-share/share": "^2.0.0", "@types/js-cookie": "^3.0.3", "@vueuse/core": "^9.3.0", "@yoda/bridge": "^2.0.1", "@yoda/bridge-types": "^2.0.8", "js-cookie": "^3.0.1", "sass": "^1.63.2", "sass-loader": "^13.3.1", "stylus": "^0.59.0", "stylus-loader": "^7.1.2", "vue": "^3.4", "vue-demi": "^0.14.0", "@pet/work.task-list-core": "0.0.2"}}