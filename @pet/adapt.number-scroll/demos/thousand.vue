<pet-info lang="json">
{
    "title": "千分位展示",
    "description": "是否展示千分位分隔符"
}
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const numberValueData = ref(90000);

function numberChange() {
    const num = Math.random() * 100000;
    numberValueData.value += num;
}
</script>

<template>
    <div class="demo">
        <h5>原始数值{{ numberValueData }}</h5>
        <div class="container">
            <h6>thousands-separator</h6>
            <NumberScroll :number-value="numberValueData" thousands-separator />
        </div>
        <button @click="numberChange">增加数值</button>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 48px 0 24px;
    font-size: 12px;
    text-align: center;
}
h5 {
    position: absolute;
}
</style>
