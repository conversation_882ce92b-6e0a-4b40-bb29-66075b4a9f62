import Cookies from 'js-cookie';

export function setC<PERSON>ie(
    key: string,
    value: string,
    {
        expires,
        domain,
    }: {
        expires?: number | Date;
        domain?: string;
    } = {},
) {
    Cookies.set(key, value, {
        expires,
        domain,
    });
}

export function getCookie(key: string) {
    return Cookies.get(key);
}

export function removeCookie(key: string) {
    Cookies.remove(key);
}

export function hasCookie(key: string) {
    return !!getCookie(key);
}
