<script lang="ts">
export default {
    name: 'AdaptInfinityLoading',
};
</script>

<script lang="ts" setup>
import { computed } from 'vue-demi';

import InfinityLoading from './assets/infinity-svg.vue';

interface Props {
    /**
     * loading
     */
    loading?: boolean;
    /**
     * 是不是占整屏
     */
    fullscreen?: boolean;
    /**
     * 自定义颜色
     */
    customColor?: string;
}

const props = withDefaults(defineProps<Props>(), {
    loading: true,
});

const loadingType = computed(() => [props.fullscreen === true && 'loading-fullscreen loading-center']);
const setCustomColor = computed(() => {
    return props.customColor
        ? {
              '--adapt-infinityLoading-color': props.customColor,
          }
        : {};
});
</script>

<template>
    <div :class="loadingType" :style="setCustomColor" class="loading-wrapper">
        <InfinityLoading class="infinity" :loading="loading" />
    </div>
</template>
<style>
:root {
    /* loading颜色 */
    --adapt-infinityLoading-color: #500;
    /* loading尺寸 */
    --adapt-infinityLoading-size: 40px;
    /* 全屏loading背景色 */
    --adapt-infinityLoading-fllscreen-background-color: rgba(0, 0, 0, 0.8);
}
</style>
<style lang="scss" scoped>
.loading-wrapper {
    color: var(--adapt-infinityLoading-color);
}

.infinity {
    display: inline-block;
    vertical-align: middle;
    width: var(--adapt-infinityLoading-size);
    height: var(--adapt-infinityLoading-size);
}

.loading-fullscreen {
    --adapt-infinityLoading-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: var(--adapt-infinityLoading-fllscreen-background-color);
}
</style>
