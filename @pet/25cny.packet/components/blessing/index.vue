<script setup lang="ts">
defineProps<{
    blessing: string;
}>();
</script>
<template>
    <div class="blessing">
        <div class="blessing_icon" />
        <span class="blessing_content">{{ blessing }}</span>
    </div>
</template>

<style>
:root {
    --adapt-dialog-blessing-icon: unset;
}
</style>
<style scoped lang="scss">
.blessing {
    display: flex;
    align-items: center;

    &_icon {
        width: 20px;
        height: 20px;
        background-size: 100%;
        background-position: 100%;
        background-repeat: no-repeat;
        background-image: var(--adapt-dialog-blessing-icon);
    }

    &_content {
        line-height: 20px;
        color: #550000;
        font-size: 14px;
        margin-left: 4px;
    }
}
</style>
