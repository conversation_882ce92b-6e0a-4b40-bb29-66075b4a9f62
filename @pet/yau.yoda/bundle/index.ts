import { invoke } from '@yoda/bridge';

export interface BundlePreloadOptions {
  /** 延迟时间（毫秒） */
  delayMs?: number;
  /** 超时时间（毫秒） */
  timeoutMs?: number;
}

export class BundlePreloader {
  private timeouts: Map<string, ReturnType<typeof setTimeout>> = new Map();
  private loadedBundles: Set<string> = new Set();

  /**
   * 预加载bundle
   * @param bundleId bundle标识
   * @returns 是否成功
   */
  async preloadBundle(bundleId: string): Promise<boolean> {
    try {
      // @ts-expect-error yoda bridge类型定义
      const res = await invoke('tool.preloadBundle', { bundleId });
      console.log(`预加载bundle成功: ${bundleId}`, res);
      this.loadedBundles.add(bundleId);
      return true;
    } catch (err) {
      console.log(`预加载bundle失败: ${bundleId}`, err);
      return false;
    }
  }

  /**
   * 卸载bundle
   * @param bundleId bundle标识
   * @returns 是否成功
   */
  async removeBundle(bundleId: string): Promise<boolean> {
    try {
      // @ts-expect-error yoda bridge类型定义
      const res = await invoke('tool.removePreloadBundle', { bundleId });
      console.log(`卸载bundle成功: ${bundleId}`, res);
      this.loadedBundles.delete(bundleId);
      return true;
    } catch (err) {
      console.log(`卸载bundle失败: ${bundleId}`, err);
      return false;
    }
  }

  /**
   * 延迟预加载bundle
   * @param bundleId bundle标识
   * @param delayMs 延迟时间（毫秒）
   */
  schedulePreload(bundleId: string, delayMs: number): void {
    // 清除之前的定时器
    this.clearSchedule(bundleId);
    
    const timeout = setTimeout(() => {
      this.preloadBundle(bundleId);
    }, delayMs);
    
    this.timeouts.set(bundleId, timeout);
  }

  /**
   * 清除指定bundle的预加载计划
   * @param bundleId bundle标识
   */
  clearSchedule(bundleId: string): void {
    const timeout = this.timeouts.get(bundleId);
    if (timeout) {
      clearTimeout(timeout);
      this.timeouts.delete(bundleId);
    }
  }

  /**
   * 检查bundle是否已加载
   * @param bundleId bundle标识
   * @returns 是否已加载
   */
  isLoaded(bundleId: string): boolean {
    return this.loadedBundles.has(bundleId);
  }

  /**
   * 清理所有定时器和已加载的bundle记录
   */
  cleanup(): void {
    this.timeouts.forEach((timeout) => clearTimeout(timeout));
    this.timeouts.clear();
    this.loadedBundles.clear();
  }

  /**
   * 批量预加载bundles
   * @param bundleConfigs bundle配置数组
   */
  async batchPreload(bundleConfigs: Array<{ bundleId: string; delayMs?: number }>): Promise<void> {
    bundleConfigs.forEach(({ bundleId, delayMs = 0 }) => {
      if (delayMs > 0) {
        this.schedulePreload(bundleId, delayMs);
      } else {
        this.preloadBundle(bundleId);
      }
    });
  }
}

/**
 * 全局bundle预加载器实例
 */
export const bundlePreloader = new BundlePreloader();
