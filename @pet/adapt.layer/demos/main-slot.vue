<pet-info lang="json">
{ "title": "自定义内容", "description": "" }
</pet-info>
<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import { ref } from 'vue-demi';

import Layer from '../index.vue';

const show = ref(false);
</script>

<template>
    <div>
        <Layer
            v-model:show="show"
            custom-logo="https://w1-pro.kskwai.com/kos/nlav11066/2022_common_static/business/modal_logo_shanzhashuxia_v1.png"
            title="此处为主标题"
            sub-title="此处为副标题，可用于辅助信息"
            main-btn-text="立即使用"
            bottom-text="文字啊啊啊啊"
        >
            <div class="custom-area">
                <img
                    class="icon"
                    src="https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2021-12-16/1639634522781.3e4249c181e76b81.png"
                />
                <div class="coupon"></div>
            </div>
        </Layer>
        <Button class="item-btn" @click="show = true">打开蒙层</Button>
    </div>
</template>

<style scoped>
.item-btn {
    margin: 0 2px;
}

.custom-area {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.icon {
    width: 150px;
    height: 150px;
    margin-top: 24px;
    margin-bottom: 12px;
}

.coupon {
    width: 246px;
    height: 86px;
    border-radius: 6px;
    background: #fff;
    margin: auto;
    margin-bottom: 24px;
}
</style>
