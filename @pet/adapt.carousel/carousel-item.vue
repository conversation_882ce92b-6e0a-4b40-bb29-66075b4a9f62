<script lang="ts">
export default {
    name: 'AdaptCarouselItem',
};
</script>

<script setup lang="ts">
import Avatar from '@pet/adapt.avatar/index.vue';
import { ref, nextTick, watch } from 'vue-demi';

const props = withDefaults(
    defineProps<{
        /**
         * 图标
         */
        head?: string;
        /**
         * 标题
         */
        name?: string;
        /**
         * 描述
         */
        desc?: string;
        /**
         * 完整动效
         */
        fullEffect?: boolean;
    }>(),
    {
        fullEffect: true,
    },
);

const rightRef = ref<HTMLDivElement>();
const rightBoxRef = ref<HTMLDivElement>();

watch(
    [rightRef, rightBoxRef, () => props.fullEffect],
    ([val, val2, effect]) => {
        if (val && val2 && effect) {
            nextTick(() => {
                const { offsetWidth } = val;
                const el = val2;
                el.style.width = `${offsetWidth}px`;
            });
        }
    },
    {
        flush: 'post',
    },
);
</script>
<template>
    <div class="carousel-item" :class="fullEffect && 'box-animation'">
        <Avatar v-if="head" :width="32" class="carousel-item-avatar" :src="head" />
        <div ref="rightBoxRef" class="carousel-item-main">
            <div ref="rightRef" class="carousel-item-content">
                <div v-if="name" class="item-title">
                    {{ name }}
                </div>
                <div v-if="$slots.title" class="item-title">
                    <slot name="title"></slot>
                </div>
                <div v-if="desc" class="item-desc">{{ desc }}</div>
                <div v-if="$slots.desc" class="item-desc"><slot name="desc"></slot></div>
                <slot />
            </div>
        </div>
    </div>
</template>
<style>
:root {
    /* 每条高度 */
    --adapt-carousel-item-height: 40px;
    /* 头像距离左边padding */
    --adapt-carousel-item-padding: 4px;
    /* 圆角 */
    --adapt-carousel-item-border-radius: 36px;
    /* 背景色 */
    --adapt-carousel-item-background: rgba(0, 0, 0, 0.3);
    /* 背景色渐变动效起始色值 */
    --adapt-carousel-item-start-background: rgba(0, 0, 0, 0);
    /* 字色 */
    --adapt-carousel-item-color: #fff;
    /* 文案内容区域右pading  */
    --adapt-carousel-item-content-padding-right: 14px;
    /* 文案内容区域左pading  */
    --adapt-carousel-item-content-padding-left: 4px;
    /* 内容区第一行文案字号 */
    --adapt-carousel-item-title-font-size: 9px;
    /* 内容区第一行文案行高 */
    --adapt-carousel-item-title-line-height: 12px;
    /* 内容区第一行文案字重 */
    --adapt-carousel-item-title-font-weight: normal;
    /* 内容区第一行文案透明度 */
    --adapt-carousel-item-title-opcity: 0.6;
    /* 内容区第一行文案与第二行之间间距 */
    --adapt-carousel-item-title-desc-gap: 4px;
    /* 内容区第二行文案字号 */
    --adapt-carousel-item-desc-font-size: 11px;
    /* 内容区第二行文案行高 */
    --adapt-carousel-item-desc-line-height: 16px;
    /* 内容区第二行文案字重 */
    --adapt-carousel-item-desc-font-weight: 700;
    /* 内容区第二行文案透明度 */
    --adapt-carousel-item-desc-opcity: 1;
    /* item滚动消失时的动效参数 */
    --adapt-carousel-item-animation: fade 267ms linear 33ms both;
    /* 头像的宽度 */
    --adapt-carousel-item-avatar-width: 32px;
    /* 头像的高度 */
    --adapt-carousel-item-avatar-height: var(--adapt-carousel-item-avatar-width);
}
</style>
<style lang="scss" scoped>
/* stylelint-disable plugin/no-low-performance-animation-properties */
@keyframes avatar-scale {
    0% {
        transform: scale(0.2);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
@keyframes fade {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes item-bg-opacity {
    0% {
        background: var(--adapt-carousel-item-start-background);
    }
    100% {
        background: var(--adapt-carousel-item-background);
    }
}
.carousel-item {
    font-size: 12px;
    box-sizing: border-box;
    padding: var(--adapt-carousel-item-padding);
    height: var(--adapt-carousel-item-height);
    display: inline-flex;
    align-items: center;
    border-radius: var(--adapt-carousel-item-border-radius);
    background: var(--adapt-carousel-item-background);
    animation: var(--adapt-carousel-item-animation);
    .carousel-item-avatar {
        opacity: 1;
    }
    .carousel-item-content {
        white-space: nowrap;
        padding-right: var(--adapt-carousel-item-content-padding-right);
        padding-left: var(--adapt-carousel-item-content-padding-left);
        display: inline-flex;
        flex-direction: column;
        height: 100%;
        justify-content: center;
        box-sizing: border-box;
        vertical-align: top;
        color: var(--adapt-carousel-item-color);
    }
}

/* 全动效下暂时不开放token进行动效参数调整 */
.box-animation.carousel-item {
    animation: item-bg-opacity 267ms linear 33ms both;
    .carousel-item-avatar {
        animation: avatar-scale 223ms cubic-bezier(0.38, 0, 0.35, 1) 5ms both;
    }
    .carousel-item-content {
        animation: fade 200ms linear 300ms both;
    }
    .carousel-item-main {
        overflow: hidden;
        width: 0px;
        transition: width 300ms cubic-bezier(0.25, 0, 0.3, 1);
    }
}
.item-title {
    font-size: var(--adapt-carousel-item-title-font-size);
    line-height: var(--adapt-carousel-item-title-line-height);
    font-weight: var(--adapt-carousel-item-title-font-weight);
    opacity: var(--adapt-carousel-item-title-opcity);
}
.item-desc {
    margin-top: var(--adapt-carousel-item-title-desc-gap);
    font-size: var(--adapt-carousel-item-desc-font-size);
    line-height: var(--adapt-carousel-item-desc-line-height);
    font-weight: var(--adapt-carousel-item-desc-font-weight);
    opacity: var(--adapt-carousel-item-desc-opcity);
}
</style>
