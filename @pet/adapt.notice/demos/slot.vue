<pet-info lang="json">
{ "title": "通知容器", "description": "" }
</pet-info>
<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import { ref } from 'vue-demi';

import Notice from '../index.vue';

const show = ref(false);
function handleClick() {
    show.value = true;
}
</script>

<template>
    <div>
        <Notice v-model:show="show">
            <div class="demo-box">自己自定内容</div>
        </Notice>
        <Button class="button" @click="handleClick">内容自定</Button>
    </div>
</template>

<style scoped>
@import '@pet/adapt.reset/reset.css';
.demo-box {
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
}
</style>
