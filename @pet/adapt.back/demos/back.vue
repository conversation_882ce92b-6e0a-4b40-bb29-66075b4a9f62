<pet-info lang="json">
{
    "title": "back",
    "description": "在客户端内，当 URL 参数中包含`useRouterPush`时，使用前端路由回退。其他情况使用 bridge 的`popBack`方式回退。",
    "priority": 20
}
</pet-info>
<script setup lang="ts">
import Back from '../index.vue';

import { toast } from '@pet/adapt.toast';
import '@pet/adapt.reset/reset.css';
const customBack = () => {
    toast('custom back');
};
const beforeBack = () => {
    toast('before back');
};
</script>
<script lang="ts">
export default {
    name: 'BackDemo',
};
</script>

<template>
    <div class="demo">
        <h4>可在客户端内尝试</h4>
        <div class="container">
            自定义返回逻辑：
            <Back class="back" :custom-back="customBack" :before-back="beforeBack">返回</Back>
        </div>
        <div class="container">
            router返回逻辑：
            <Back class="back" :before-back="beforeBack">返回</Back>
        </div>
    </div>
</template>
<style lang="scss" scoped>
h4 {
    font-size: 18px;
    line-height: 28px;
    margin: 16px 0;
}
.back {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    margin: 0 auto;
    cursor: pointer;
}
</style>
