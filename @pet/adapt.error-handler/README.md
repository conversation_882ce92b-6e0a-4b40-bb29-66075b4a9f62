# @pet/adapt.error-handler

[设计规范](https://www.figma.com/design/o3HEGwJiet8GxgLkW7Dg5e/%E5%A2%9E%E9%95%BF%E8%AE%BE%E8%AE%A1%E5%85%A8%E5%B1%80%E8%A7%84%E8%8C%83?node-id=163-14634&t=9gIIJi8NVdbq0Mnk-0)

main.ts

引入 store

```ts
import Vue from "vue";
import { apolloStore } from "@/store";

apolloStore.install(Vue, app);
```

router.ts

引入路由错误页

```ts
import { createRouter, createWebHashHistory } from "vue-router";

import ErrorPage from "./ErrorPage.vue";
import { ErrorPageName } from "./index";

export const RouterName = {
    Home: "home",
};

export const router = createRouter({
    history: createWebHashHistory(),
    routes: [
        {
            path: "/",
            name: RouterName.Home,
            component: () => import("./demos/home.vue"),
        },
        {
            path: "/error",
            name: ErrorPageName,
            component: ErrorPage,
        },
    ],
});
```

引入项目中的 @/views/ErrorPage.vue

```vue
<script setup lang="ts">
import { computed, defineAsyncComponent } from "vue-demi";
import { ErrorPage } from "@pet/adapt.error-handler";

import MusicIcon from "@pet/adapt.icons/music.svg.vue";

interface Props {}
const props = withDefaults(defineProps<Props>(), {});
const WalletIcon = defineAsyncComponent(() => import("@pet/adapt.icons/wallet.svg.vue"));

const icons = computed(() => {
    return {
        109: WalletIcon,
    };
});
</script>

<template>
    <ErrorPage :icon-components="icons"></ErrorPage>
</template>

<style lang="scss" scoped></style>
```

store.ts
全局处理错误逻辑

-   blacklist 黑名单接口列表会直接进如大兜底，未登录的状态用户会有对应的登录引导。
-   needLoginList 登录配置会创建透明登录遮罩，配置自动登录等。

```ts
import { createStore, createClient } from "vue-apollo-model";
import type { RestClientParams, GQLClientParams } from "vue-apollo-model/dist/operations/types";
import { errorHandler, ErrorPageName } from "@/views/adapt.error-handler";
import { router } from "@/views/router";
export type ResponseType = {
    data: {
        result: number;
        data?: Record<string, any> | Array<any>;
        error_msg?: any;
        message?: any;
    };
    status: number;
    statusText: string;
    config: RequestInfo;
};

export const apolloStore = createStore();

const client = createClient("REST", {
    method: "GET",
    timeout: 10 * 1000,
});

client.interceptors.response.use(async (data) => {
    if (data.status !== 200) {
        return Promise.reject(data);
    }
    const dataContent = data.data;
    if (dataContent.result !== 1) {
        return Promise.reject(dataContent);
    }
    return dataContent.data;
});

const clientRequest = client.request;
client.request = async <T>(params: RestClientParams | GQLClientParams) => {
    try {
        return await clientRequest<T>(params);
    } catch (e) {
        // 异常处理逻辑
        errorHandler(
            params,
            e as unknown as ResponseType["data"],
            router,
            ErrorPageName,
            // 错误配置项
            {
                // 全部进入大兜底，即便是没有登录
                blacklist: ["rest/wd/cny2022/warmup/card/home"],
                // 这个列表是会创建透明遮罩
                needLoginList: ["rest/wd/cny2022/warmup/card/home"],
                // 主题配色
                themeInfos: {
                    /**
                     * key为当前拦截路由的name
                     * color 文字色
                     * bgColor 背景色
                     * customClass 自定义class
                     */
                    test: {
                        color: "#ab2132",
                        bgColor: ["#ffefc7", "#fff9eb"],
                    },
                },
                // 自定义错误码配置
                // key 错误码 value 图标名称 (可比较清晰知道一个错误码对应的icon)
                // 这里的自定义图标，是使用传入一个自定义组件实现的 可见 view/ErrorPage.vue
                errorCodeInfos: {
                    10086: "walletIcon",
                },
            },
        );
        return await Promise.reject(e);
    }
};

apolloStore.registerClient("REST", client);
```
