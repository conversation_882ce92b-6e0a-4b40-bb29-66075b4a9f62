<script setup lang="ts">
import { whenever } from '@vueuse/core';
import { computed, nextTick, ref } from 'vue';

export type AniType = 'begin-shine-breath' | 'shine-breath' | 'shine' | 'breath' | 'none';
interface ButtonProps {
    /**
     * 禁用状态
     */
    disabled?: boolean;
    /**
     * 动效类型 shine-breath:呼吸+光效 shine:光效 breath:呼吸
     */
    aniType?: AniType;
    /**
     * 动效降级情况
     */
    effectStatus?: boolean;
    /**
     * css降级情况
     */
    cssStatus?: boolean;
    /**
     * 动效暂停
     */
    pause?: boolean;
    /**
     * 在进行中
     */
    inProcessing?: boolean;
}
const props = withDefaults(defineProps<ButtonProps>(), {
    disabled: false,
    looksLikeDisabled: false,
    loading: false,
    effectStatus: true,
    cssStatus: true,
    pause: false,
    inABTest: false,
});
const emit = defineEmits<{
    (e: 'click', params: Event): void;
}>();
const aniTypeEnum = {
    beginShineBreath: 'begin-shine-breath',
    shineBreath: 'shine-breath',
    shine: 'shine',
    breath: 'breath',
    none: 'none',
};

const lightEffectRef1 = ref<any>(null);
const showBeginAni = computed(() => {
    return props.aniType === aniTypeEnum.beginShineBreath;
});
const showShineAni = computed(() => {
    return [aniTypeEnum.shineBreath, aniTypeEnum.shine, aniTypeEnum.beginShineBreath].includes(
        props.aniType ?? aniTypeEnum.none,
    );
});
const showBreachAni = computed(() => {
    return (props.aniType === aniTypeEnum.shineBreath || props.aniType === aniTypeEnum.breath) && !props.pause;
});

// 暂定动画
function pauseAni() {
    if ((showShineAni.value || showBeginAni.value) && lightEffectRef1.value?.pause) {
        lightEffectRef1.value?.pause();
    }
}

// 恢复动画
function resumeAni() {
    if ((showShineAni.value || showBeginAni.value) && lightEffectRef1.value?.resume) {
        lightEffectRef1.value?.resume();
    }
}

const touchStart = ref(false);
const touchEnd = ref(false);

const handleTouchStart = (e: Event) => {
    if (props.disabled) {
        return;
    }
    e.preventDefault();
    touchEnd.value = false;
    touchStart.value = true;
    pauseAni();
};

watch(
    () => !!props.pause,
    (newVal) => {
        if (newVal) {
            pauseAni();
        } else {
            resumeAni();
        }
    },
);

const handleTouchEnd = (e: Event) => {
    if (props.disabled) {
        return;
    }
    emit('click', e);
    if (!props.inProcessing) {
        touchStart.value = false;
    }
    touchEnd.value = true;
    resumeAni();
};

whenever(
    () => !props.inProcessing,
    () => {
        touchStart.value = false;
    },
);

const handleAnimationEnd = (e: AnimationEvent) => {
    if (e.animationName.includes('btn_click_end')) {
        nextTick(() => {
            touchEnd.value = false;
        });
    }
};

// 正常状态的按压动画
const showTouchStartAni = computed(() => {
    return touchStart.value;
});
const showTouchEndAni = computed(() => {
    return touchEnd.value;
});
</script>

<template>
    <div
        class="main-button"
        :class="{
            'main-button--breath': showBreachAni && cssStatus,
            'main-button--pause': pause,
            'main-button--begin': showBeginAni && effectStatus,
            'main-button--begin-degrade': showBeginAni && !effectStatus,
            'touch-start': showTouchStartAni,
            'touch-end': showTouchEndAni && !inProcessing,
        }"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
        @animationend="handleAnimationEnd"
        @click="emit('click', $event)"
    >
        <div class="button-bg"></div>
        <slot />
        <div
            v-if="showShineAni && effectStatus"
            class="shine-button"
            :class="{ 'shine-button--pause': pause || touchStart }"
        ></div>
        <div class="click-mask" />
    </div>
</template>

<style lang="scss" scoped>
.main-button {
    position: relative;
    padding: 0;
    .button-bg {
        width: 275.5px;
        height: 91px;
        @include bg('@/assets/forward-rush/button-main-bg.png');
        background-size: 100% 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
    }
    .begin-button {
        position: absolute;
        width: 245.5px;
        height: 106px;
        top: -14px;
        left: -20px;
    }
    .shine-button {
        width: 300px;
        height: 120px;
        overflow: hidden;
        left: -12.5px;
        top: -13.5px;
        position: absolute;
        @include bg('./assets/img_3-2.png');
        background-size: 100% 100%;
        z-index: 1;
        opacity: 0;
        pointer-events: none;
        animation: hash65a15e54_0_keyframe_0 2.5s 500ms cubic-bezier(0.333, 0, 0.667, 1) infinite;
        animation-play-state: running;
    }
    .shine-button--pause {
        animation-play-state: paused;
    }
    &--breath {
        animation: btn_breath 0.8s 0s steps(16) infinite;
        animation-play-state: running;
    }
    &--begin {
        transform: scale(0.9);
        animation:
            btn_pong 0.6s 0s cubic-bezier(0.33, 0, 0.67, 1) forwards,
            btn_pong_end 0.6s 6.6s cubic-bezier(0.33, 0, 0.67, 1) forwards;
        animation-iteration-count: 11, 1;
        animation-play-state: running;
    }
    &--begin-degrade {
        animation: hashe1510d04_0_keyframe_0 0.32s 0s cubic-bezier(0.333, 0, 0.667, 1) infinite;
    }
    &--pause {
        animation-play-state: paused;
    }

    .click-mask {
        position: absolute;
        top: 2px;
        left: 0;
        width: 275.5px;
        height: 91px;
        z-index: 2;
        background: url('./assets/img_2.png') no-repeat;
        background-size: 100% 100%;
        opacity: 0;
        pointer-events: none;
    }
    &.touch-start {
        animation: btn_click_start 167ms 0s cubic-bezier(0.11, 0, 0.67, 1) forwards;

        .click-mask {
            opacity: 0;
            animation: mask_touch_start 167ms 0s cubic-bezier(0.11, 0, 0.67, 1) forwards;
        }
    }
    &.touch-end {
        transform: scale(0.9);
        animation: btn_click_end 200ms 0ms cubic-bezier(0.33, 0, 0.93, 1) forwards;
        .click-mask {
            opacity: 1;
            animation: mask_touch_end 200ms 0ms cubic-bezier(0.33, 0, 0.93, 1) forwards;
        }
    }

    @keyframes btn_pong {
        0% {
            transform: scale(0.9);
        }
        46.67% {
            transform: scale(1.08);
        }
        100% {
            transform: scale(0.9);
        }
    }

    @keyframes hash65a15e54_0_keyframe_0 {
        0% {
            opacity: 0;
        }
        25% {
            opacity: 1;
        }
        50% {
            opacity: 0;
        }
        75% {
            opacity: 1;
        }
        100% {
            opacity: 0;
        }
    }

    @keyframes btn_pong_end {
        0% {
            transform: scale(0.9);
        }
        46.67% {
            transform: scale(1.08);
        }
        100% {
            transform: scale(1);
        }
    }

    @keyframes btn_breath {
        0% {
            transform: scale(0.9);
        }
        50% {
            transform: scale(1);
        }
        100% {
            transform: scale(0.9);
        }
    }
    @keyframes btn_click_start {
        0% {
            transform: scale(1);
        }
        100% {
            transform: scale(0.9);
        }
    }
    @keyframes btn_click_end {
        0% {
            transform: scale(0.9);
        }
        100% {
            transform: scale(1);
        }
    }
    @keyframes mask_touch_start {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }
    @keyframes mask_touch_end {
        0% {
            opacity: 1;
        }
        100% {
            opacity: 0;
        }
    }
}

@keyframes hashe1510d04_0_keyframe_0 {
    0% {
        transform: scale(0.9, 0.9);
    }
    50.013% {
        transform: scale(1.1, 1.1);
    }
    100% {
        transform: scale(0.9, 0.9);
    }
}
@keyframes hash11d946bc_0_keyframe_0 {
    0% {
        transform: scale(1, 1);
    }
    49.988% {
        transform: scale(1.2, 1.2);
    }
    100% {
        transform: scale(1, 1);
    }
}
</style>
