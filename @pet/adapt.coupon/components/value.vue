<script setup lang="ts">
import AdaptImage from '@pet/adapt.image/index.vue';
import { computed } from 'vue-demi';

import defaultThumb from '../assets/default.png';
import type { CouponDisplayType, CouponSize } from '../types';
interface CouponValueProps {
    size?: CouponSize;
    main?: string;
    desc?: string;
    image?: string;
    type?: CouponDisplayType;
    overdue?: boolean;
}
const props = withDefaults(defineProps<CouponValueProps>(), {
    size: 'small',
});

const valueSize = computed(() => `value-${props.size === 'mini' ? 'mini' : 'normal'}`);
const numbers = computed(() => props.main?.split('.'));
const textGap = computed(() => (props.type === 'benefit' ? 'gap-text' : 'gap-number'));
const isOut = computed(() => props.overdue === true && 'is-out');
</script>

<template>
    <div class="coupon-value" :class="[valueSize, isOut]">
        <AdaptImage
            v-if="type === 'default' && image"
            :src="image"
            :fallback-src="defaultThumb"
            class="coupon-value-image"
        />
        <template v-else>
            <h4 class="coupon-value-main">
                <span v-if="type === 'benefit'" class="coupon-value-main-title">{{ main }}</span>
                <span v-else class="coupon-value-main-numbers">
                    <span v-if="type === 'rebate'" class="coupon-value-main-prefix">￥</span>
                    <span v-if="numbers" class="coupon-value-number">
                        <span class="coupon-value-number-large">{{ numbers[0] }}</span>
                        <span v-if="numbers[1]" class="coupon-value-number-small">.{{ numbers[1] }}</span>
                    </span>
                    <span v-if="type === 'discount'" class="coupon-value-main-suffix">折</span>
                </span>
            </h4>
            <div v-if="desc" class="coupon-value-desc" :class="textGap">
                {{ desc }}
            </div>
        </template>
    </div>
</template>

<style lang="scss" scoped>
$coupon-value-image-mini: 50px;
$coupon-value-image-normal: 62px;
$coupon-value-out-color: #939393;
.coupon-value {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    &.is-out {
        color: $coupon-value-out-color;
        .coupon-value-image {
            /* stylelint-disable-next-line declaration-block-no-duplicate-properties */
            filter: gray;
            filter: grayscale(1);
            opacity: 0.8;
        }
    }
}
/* stylelint-disable-next-line no-descending-specificity */
.coupon-value-image {
    border-radius: 4px;
}
.coupon-value-main {
    width: 100%;
    text-align: center;
    margin: 0;
    line-height: 1;
}
.coupon-value-number {
    font-family: 'DIN Alternate', sans-serif;
}
.coupon-value-main-title,
.coupon-value-main-numbers {
    display: inline-block;
    vertical-align: top;
    white-space: nowrap;
    overflow: hidden;
}
.coupon-value-main-title {
    max-width: 5em;
}
.coupon-value-main-numbers {
    width: 100%;
}
.coupon-value-desc {
    display: block;
    max-width: 8em;
    white-space: nowrap;
    overflow: hidden;
}
.value-mini {
    /* stylelint-disable-next-line no-descending-specificity */
    .coupon-value-image {
        width: $coupon-value-image-mini;
        height: $coupon-value-image-mini;
    }
    .coupon-value-desc {
        font-size: 8px;
        line-height: 12px;
    }
    .coupon-value-main-title {
        font-size: 14px;
        line-height: 18px;
    }
    .coupon-value-main-prefix {
        font-size: 14px;
    }
    .coupon-value-main-suffix {
        font-size: 10px;
        margin-left: 2px;
    }
    .coupon-value-number-large {
        font-size: 20px;
    }
    .coupon-value-number-small {
        font-size: 14px;
    }
    .gap-text {
        margin-top: 4px;
    }
    .gap-number {
        margin-top: 4px;
    }
}
.value-normal {
    /* stylelint-disable-next-line no-descending-specificity */
    .coupon-value-image {
        width: $coupon-value-image-normal;
        height: $coupon-value-image-normal;
    }
    .coupon-value-desc {
        font-size: 10px;
        line-height: 14px;
    }
    .coupon-value-main-title {
        font-size: 16px;
        line-height: 24px;
    }
    .coupon-value-main-prefix {
        font-size: 16px;
    }
    .coupon-value-main-suffix {
        font-size: 10px;
        margin-left: 2px;
        vertical-align: 1px;
    }
    .coupon-value-number-large {
        font-size: 24px;
    }
    .coupon-value-number-small {
        font-size: 16px;
    }
    .gap-text {
        margin-top: 6px;
    }
    .gap-number {
        margin-top: 4px;
    }
}
</style>
