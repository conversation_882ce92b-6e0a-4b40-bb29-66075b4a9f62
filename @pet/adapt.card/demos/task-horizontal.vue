<pet-info lang="json">
{
    "title": "任务面板内-横向",
    "description": "单个卡片内容：头像、描述、按钮等横向排列,常规通用列表，适用于任务面板，一般承载在sheet组件内",
    "priority": 10
}
</pet-info>
<script lang="ts" setup>
import Card from '../index.vue';
const taskInfo = {
    figure: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '抽新年福签',
    desc: '抽福签得礼物，愿望金+10',
    attachedText: '额外信息',
    isLoading: false,
    isDisabled: false,
};
const taskInfo2 = {
    figure: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '抽新年福签',
    desc: '抽福签得礼物，愿望金+10',
    buttonText: '去冲鸭',
    isLoading: false,
    isDisabled: false,
};
</script>

<template>
    <div class="page">
        <h5>size="normal"</h5>
        <div class="card-wrapper">
            <Card class="card-item" v-bind="taskInfo2"></Card>
            <Card class="card-item" v-bind="taskInfo"></Card>
        </div>
        <h5>size="small"，间距字号比normal小一点， 一般出现在集卡会场</h5>
        <div class="card-wrapper">
            <Card class="card-item" v-bind="taskInfo2" size="small"></Card>
            <Card class="card-item" v-bind="taskInfo" size="small"></Card>
        </div>
    </div>
</template>

<style scoped>
h5 {
    font-size: 16px;
}
.page {
    width: 100%;
    background: #fdf3df;
    padding: 14px;
    margin: 0 -10pt;
}
.card-wrapper {
    padding: 12px;
    border-radius: 16px;
    border: 2px solid #fff;
    background: #fefbf6;
}

.card-item + .card-item {
    margin-top: 12px;
}
</style>
