import { useNonInteractive } from '@pet/yau.yoda';
import { until } from '@vueuse/core';
import { onMounted, ref } from 'vue';

export const useWeakGuideInteractive = () => {
    const intervalAwaitSuccess = ref(false);
    const nonIntervalFinish = ref(true);
    const interval = ref(0);

    const { pause, resume } = useNonInteractive(
        () => {
            intervalAwaitSuccess.value = true;
            nonIntervalFinish.value = true;
            interval.value = 0;
        },
        { interval },
    );

    /** 当前无操作间隔取消 */
    const nonIntervalCancel = () => {
        intervalAwaitSuccess.value = false;
        nonIntervalFinish.value = true;
        interval.value = 0;
    };

    /** 等待无操作间隔 */
    const nonIntervalAwait = async (timeout: number) => {
        nonIntervalFinish.value = false;
        interval.value = timeout;
        await until(nonIntervalFinish).toBeTruthy();
        interval.value = 0;

        return intervalAwaitSuccess.value;
    };

    return {
        nonIntervalAwait,
        nonIntervalCancel,
    };
};
