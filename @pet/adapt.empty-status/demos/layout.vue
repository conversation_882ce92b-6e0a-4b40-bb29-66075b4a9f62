<pet-info lang="json">
{ "title": "组合形态", "description": "所有展示组合的形态枚举" }
</pet-info>
<script lang="ts" setup>
import EmptyStatus from '../index.vue';

function handleClick() {
    console.log('click');
}
</script>

<template>
    <div class="layout-box">
        <h4>图+文</h4>
        <EmptyStatus icon="notStart"> 此处为兜底文案，此处为兜底文案 </EmptyStatus>
        <h4>图+文+按钮</h4>
        <EmptyStatus icon="network" button button-text="按钮按钮" @btn-click="handleClick">
            此处为兜底文案，此处为兜底文案
        </EmptyStatus>
        <h4>图+文（主/副标题）+按钮</h4>
        <EmptyStatus icon="anomaly" button button-text="按钮按钮" class="icon-sub">
            此处为主标题，此处为主标题
            <template #subMessage> 此处为副标题此处为副标题此处为副标题此处为副标题，此处为副标题 </template>
        </EmptyStatus>
    </div>
</template>

<style lang="scss" scoped>
.layout-box {
    font-size: 16px;
}
</style>
