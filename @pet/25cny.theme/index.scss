body {
    --cny25-custom-font: <PERSON><PERSON><PERSON><PERSON><PERSON>uiTi;
    --cny25-main-text-color: #500;
    --cny25-main-btn-background-color: #ff1212;
    --cny25-main-btn-background-image: linear-gradient(
            268.94deg,
            rgba(239, 74, 178, 49%) -1.39%,
            rgba(255, 37, 77, 0) 19.71%
        ),
        linear-gradient(180deg, rgba(255, 255, 255, 0.296) 0%, rgba(255, 255, 255, 0) 50%),
        linear-gradient(172deg, #ff0000 31.7%, #ffa600 128.24%);
    --cny25-main-btn-font-linear: linear-gradient(0deg, #fff4cf 25%, #fffbeb 66.07%);
    --cny25-common-title-skew: matrix(1, 0, -0.14, 0.99, 0, 0);
    --cny25-button-background-color: #ff3721;
    /* 全局主文字色 */
    --adapt-global-font-color: var(--cny25-main-text-color);
    /* 主按钮背景色 */
    --adapt-button-primary-background-color: var(--cny25-button-background-color);
    /* switch选中色*/
    --adapt-toggle-switch-toggle-on-background: var(--cny25-button-background-color);
    /* 特殊字体标题 */
    --adapt-heading-title-font-family: var(--cny25-custom-font), sans-serif;
    --adapt-heading-title-font-style: var(--cny25-common-title-skew);
    --adapt-heading-title-font-color: linear-gradient(235.64deg, #fffaf0 36.75%, #fffbd5 69.96%);
    --adapt-heading-title-letter-spacing: 0.01em;

    --adapt-layer-main-button-primary-background-color: var(--cny25-main-btn-background-color);
    --adapt-layer-main-button-primary-background-image: var(--cny25-main-btn-background-image);
    --adapt-layer-main-button-primary-font-family: var(--cny25-custom-font), sans-serif;
    --adapt-layer-main-button-primary-font-linear: var(--cny25-main-btn-font-linear);
    --adapt-layer-main-button-primary-letter-spacing: 0.01em;

    /* dialog */
    --adapt-dialog-main-button-primary-background-color: var(--cny25-main-btn-background-color);
    --adapt-dialog-main-button-primary-background-image: var(--cny25-main-btn-background-image);
    --adapt-dialog-main-button-primary-font-family: var(--cny25-custom-font), sans-serif;
    --adapt-dialog-main-button-primary-font-linear: var(--cny25-main-btn-font-linear);
    --adapt-dialog-main-button-primary-letter-spacing: 0.01em;
    --adapt-dialog-second-button-font-color: #f7211d;
    --adapt-dialog-second-button-border-color: #f7211d;
    --adapt-dialog-plus-color: #9c9c9c;
    --adapt-dialog-plus-opcity: 1;
    --adapt-dialog-back-background: #fff9f4;
    @include bg("./assets/dialog-background.png", --adapt-dialog-head-background);
    --adapt-dialog-title-font-family: var(--cny25-custom-font);
    --adapt-dialog-title-font-color: linear-gradient(87.21deg, #9c3f17 21.6%, #000000 75.52%);
    @include bg("./assets/dialog-cartoon-icon.png", --adapt-lay-cartoon);
    @include bg("./assets/dialog-blessing-icon.png", --adapt-dialog-blessing-icon);
    --adapt-lay-cartoon-height: 100px;
    --adapt-lay-cartoon-margin-bottom: 2px;
    --adapt-dialog-lay-cartoon-z-index: -1;
    --adapt-dialog-sub-title-color: rgba(85, 0, 0, 0.5);

    /* sheet */
    @include bg("./assets/sheet-cartoon.png", --adapt-sheet-lay-cartoon);
    --adapt-sheet-lay-cartoon-background-position: right top;
    --adapt-sheet-lay-cartoon-height: 150px;
    --adapt-sheet-lay-cartoon-margin-bottom: 93px;
    --adapt-sheet-title-font-family: var(--cny25-custom-font);
    --adapt-sheet-title-background: linear-gradient(85deg, #8a1a10 2.9%, #3f0604 49.75%, #1e0000 90.05%);
    --adapt-sheet-title-transform: matrix(1, 0, -0.12, 0.99, 0, 0);
    --adapt-sheet-subTitle-color: rgba(85, 0, 0, 0.6);
    --adapt-sheet-screen-background: #fff;
    @include bg("./assets/sheet-background.png", --adapt-sheet-cover-background);
    --adapt-sheet-cover-background-size: 100% 180px;
    /* tooltip */
    --adapt-tool-tip-color: var(--cny25-main-text-color);
    /* error page */
    --adapt-error-handler-background: #FFE6DE;
    /* 4tab下的兜底背景色 */
    .warmup-at-four-tab {
        --adapt-error-handler-background: linear-gradient(
            180deg,
            #e10101 10.08%,
            #ff694b 20.95%,
            #fcb38a 32.02%,
            #fcd6b0 45.16%,
            #fff4cd 58.81%,
            #f9f2da 72.46%
        );
    }
    .warmup-at-search-tab {
        --adapt-error-handler-background: linear-gradient(
            180.01deg,
            #e5150e 0.01%,
            #ff694b 15.01%,
            #fcb38a 30.29%,
            #fcd6b0 48.42%,
            #fff4cd 67.26%,
            #f9f2da 86.1%
        );
    }
    .eve-at-four-tab,
    .eve-at-search-tab {
        --adapt-error-handler-background: linear-gradient(
            179.87deg,
            #13046e 0.12%,
            #35128e 6.08%,
            #5b29b3 15.76%,
            #8b47c7 26.73%,
            #f6a2cc 42.18%,
            #ffc7b8 50.37%,
            #fddabb 61.65%,
            #feebcc 74.94%,
            #fffae3 99.89%
        );
    }

    .adapt-empty-status {
        .btn-plain {
            --adapt-button-plain-background-color: #ff3721;
            --adapt-button-plain-border-color: #ff3721;
            --adapt-button-plain-font-color: #fff;
        }

        --adapt-empty-status-icon-opacity: 1;
        --adapt-empty-status-icon-message-gap: 0;

        .status-icon {
            --adapt-empty-status-icon-width: 148px;
            background-size: 100% 100%;
            background-repeat: no-repeat;

            * {
                display: none;
            }
        }

        .icon-network,
        .icon-protect,
        .icon-anomaly {
            @include bg("./assets/empty-icons/ku.png", background-image);
        }
        .icon-notStart {
            @include bg("./assets/empty-icons/baojinbi.png", background-image);
        }
        .icon-band,
        .icon-download {
            @include bg("./assets/empty-icons/yanjingshanguang.png", background-image);
        }
        .icon-end,
        .icon-activityHot,
        .icon-noBody,
        .icon-user,
        .icon-noMessage,
        .icon-empty,
        .icon-noGift {
            @include bg("./assets/empty-icons/zuodianzi.png", background-image);
        }
    }

    .close-packet-skin-red {
        --cny-base-close-packet-main-bg-img: url("./assets/close-packet-bg.png");
        --cny-base-close-packet-amount-color: #fef6ca;
        --cny-base-close-packet-amount-desc-color: #fef6ca;
        --cny-base-close-packet-amount-tag-bg-color: rgba(255, 11, 36, 0.8);
        --cny-base-close-packet-sub-title: #fff4ca;
        --cny-base-close-packet-sub-title-desc: #fef6ca;
        --cny-base-close-packet-sub-title-avatar-border-color: #ffecc5;
        @include bg("./assets/close-packet-icon-lock.png", --cny-base-close-packet-mid-lock-icon);
        @include bg("./assets/close-packet-icon-open.png", --cny-base-close-packet-mid-open-icon);
        @include bg("./assets/close-packet-btm-btn.png", --cny-base-close-packet-btm-btn-bg-img);
        --cny-base-close-packet-btm-btn-font-color: #ff0b24;
    }

    .close-packet-skin-gold {
        // @include bg("./assets/golden-close-packet-bg.png", --cny-base-close-packet-main-bg-img);
        --cny-base-close-packet-amount-color: #ba3608;
        --cny-base-close-packet-amount-desc-color: #ba3608;
        --cny-base-close-packet-amount-tag-bg-color: rgba(255, 255, 255, 0.5);
        --cny-base-close-packet-sub-title: #ba3608;
        --cny-base-close-packet-sub-title-desc: #ba3608;
        --cny-base-close-packet-sub-title-avatar-border-color: #ba3608;
        @include bg("./assets/golden-close-packet-icon-lock.png", --cny-base-close-packet-mid-lock-icon);
        @include bg("./assets/golden-close-packet-icon-open.png", --cny-base-close-packet-mid-open-icon);
        @include bg("./assets/golden-close-packet-btm-btn.png", --cny-base-close-packet-btm-btn-bg-img);
        --cny-base-close-packet-btm-btn-font-color: #fff5e6;
    }

    .open-packet-skin-red {
        @include bg("./assets/open-packet-cover-top.png", --cny-base-open-packet-cover-top-img);
        --cny-base-open-packet-cover-top-sub-title-color: #fef6ca;
        --cny-base-open-packet-cover-top-blessing-color: #fef6ca;
        --cny-base-open-packet-top-background: linear-gradient(180deg, #ffffff 30.24%, #fff9f3 57.09%, #ffe4dd 80.38%);
        --cny-base-open-packet-inner-sub-title-color: #550000;
        --cny-base-open-packet-inner-sub-title-desc-color: #550000;
        --cny-base-open-packet-inner-blessing-color: #550000;
        --cny-base-open-packet-btm-bg-img: url("./assets/open-packet-btm-bg.png");
        --cny-base-open-packet-btm-desc-color: #fef6ca;
        @include bg("./assets/open-packet-back-board.png", --cny-base-open-packet-back-board-img);
        @include bg("./assets/open-packet-single-btn.png", --cny-base-open-packet-single-btn-bg-img);
        --cny-base-open-packet-single-btn-font-color: #f7211d;
        --cny-base-open-packet-left-btn-font-color: #ffdcb4;
        @include bg("./assets/open-packet-right-btn.png", --cny-base-open-packet-right-btn-bg-img);
        --cny-base-open-packet-right-btn-font-color: #ff0b24;
        @include bg("./assets/close-packet-icon-open.png", --cny-base-open-packet-mid-open-icon);
    }
    .open-packet-skin-gold {
        @include bg("./assets/golden-open-packet-cover-top.png", --cny-base-open-packet-cover-top-img);
        --cny-base-open-packet-cover-top-sub-title-color: #ba3608;
        --cny-base-open-packet-cover-top-blessing-color: #ba3608;
        --cny-base-open-packet-top-background: linear-gradient(180deg, #ffffff 30.24%, #fff9f3 57.09%, #ffe4dd 80.38%);
        --cny-base-open-packet-inner-sub-title-color: #550000;
        --cny-base-open-packet-inner-sub-title-desc-color: #550000;
        --cny-base-open-packet-inner-blessing-color: #550000;
        // @include bg("./assets/golden-open-packet-btm-bg.png", --cny-base-open-packet-btm-bg-img);
        --cny-base-open-packet-btm-desc-color: #ba3608;
        @include bg("./assets/golden-open-packet-back-board.png", --cny-base-open-packet-back-board-img);
        @include bg("./assets/golden-open-packet-single-btn.png", --cny-base-open-packet-single-btn-bg-img);
        --cny-base-open-packet-single-btn-font-color: #fff5e6;
        --cny-base-open-packet-left-btn-font-color: #bd420d;
        @include bg("./assets/golden-open-packet-right-btn.png", --cny-base-open-packet-right-btn-bg-img);
        --cny-base-open-packet-right-btn-font-color: #fff5e6;
        @include bg("./assets/golden-close-packet-icon-open.png", --cny-base-open-packet-mid-open-icon);
    }
}
