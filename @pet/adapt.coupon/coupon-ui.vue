<script setup lang="ts">
import '@pet/style.utils/font_din.css';

import { computed } from 'vue-demi';

import CardButton from './components/coupon-button.vue';
import CardMain from './components/coupon-main.vue';
import CardStatusIcon from './components/status-icon.vue';
import CardValue from './components/value.vue';
import { getBtnText } from './config/utils';
import type { CouponDisplayType, CouponSize, UseStatus } from './types';

export type ButtonType = 'flat' | 'plain';

interface CouponCardProps {
    /**
     * 卡券展示类型
     * @values "rebate" | "discount" | "default" | "benefit" | "special"
     */
    type?: CouponDisplayType;
    /**
     * 卡券尺寸
     * @values "simple" | "mini" | "small" | "medium" | "large" | "full"
     */
    size?: CouponSize;
    noBkg?: boolean;
    /**
     * 使用状态
     * @values "not_use" | "has_used" | "out_of_date" | "review" | "review_fail" | "not_grant" | "granted"
     */
    status?: UseStatus;
    /**
     * 显示使用按钮
     */
    showUseBtn?: boolean;
    /**
     * 点击整个卡片生效
     */
    clickAll?: boolean;
    /**
     * 左侧值主要内容
     */
    valueMain?: string;
    /**
     * 左侧值说明
     */
    valueDesc?: string;
    /**
     * 左侧图链接
     */
    valueImage?: string;
    /**
     * 右侧标题
     */
    mainTitle?: string;
    /**
     * 右侧标题Icon
     */
    mainIcon?: string;
    /**
     * 右侧描述
     */
    mainDesc?: string;
    /**
     * 商品卡券`special`价格
     */
    mainValue?: string;
    /**
     * 商品卡券`special`原始价格
     */
    mainOgValue?: string;
    /**
     * 右侧补充描述
     */
    mainAttach?: string;
    /**
     * 按钮类型
     * @values "flat" | "plain"
     */
    btnType?: ButtonType;
    /**
     * 按钮文案
     */
    btnText?: string;
    /**
     * 按钮不可用
     */
    btnDisabled?: boolean;
    /**
     * 按钮加载中
     */
    btnLoading?: boolean;
    /**
     * 卡券标签
     */
    tag?: string;
    /**
     * 个数
     */
    count?: number;
}
const props = withDefaults(defineProps<CouponCardProps>(), {
    size: 'small',
    showBtn: false,
    status: 'not_use',
});
const emit = defineEmits<{
    /**
     * 卡券点击
     */
    (event: 'coupon-click'): void;
}>();

const couponSize = computed(() => `card-${props.size}`);
const cardMainBtn = computed(() => props.size !== 'large');
const statusText = computed(() => props.btnText ?? getBtnText(props.status));
const couponOverdue = computed(() => props.status === 'out_of_date');
const isBtnAble = computed(() => props.status === 'not_use' && props.btnDisabled !== true);
const couponBg = computed(() => props.noBkg === true && 'no-bg');
const iconStatus = computed(() => ['out_of_date', 'has_used'].includes(props.status));
const hasMoreCount = computed(() => {
    return Number(props.count ?? 0) > 1 && props.size === 'large';
});
const moreCount = computed(() => {
    return hasMoreCount.value && 'coupon-more-count';
});

function handleCouponClick() {
    emit('coupon-click');
}

function clickWholeCard() {
    if (props.clickAll === true) {
        handleCouponClick();
    }
}
</script>

<template>
    <div class="coupon-content" :class="[moreCount, couponBg]">
        <div class="coupon-card" :class="[couponSize, couponBg]" @click="clickWholeCard">
            <div class="coupon-side">
                <span v-if="tag" class="coupon-tag">
                    <b>{{ tag }}</b>
                </span>
                <span v-if="count && count > 1" class="coupon-count">{{ count }} 张</span>
                <CardValue
                    class="coupon-value"
                    :type="type"
                    :size="size"
                    :main="valueMain"
                    :desc="valueDesc"
                    :image="valueImage"
                    :overdue="couponOverdue"
                />
            </div>
            <div class="coupon-divider"></div>
            <CardMain
                :size="size"
                :show-btn="cardMainBtn && showUseBtn"
                class="coupon-main"
                :icon="mainIcon"
                :title="mainTitle"
                :desc="mainDesc"
                :value="mainValue"
                :og-value="mainOgValue"
                :attach="mainAttach"
                :overdue="couponOverdue"
                :btn-text="statusText"
                :btn-disabled="!isBtnAble"
                :btn-loading="btnLoading"
                @btn-click="handleCouponClick"
            />
            <div v-if="!cardMainBtn" class="coupon-ctrl">
                <CardStatusIcon v-if="iconStatus" class="coupon-status" :text="statusText" />
                <CardButton
                    v-else
                    :type="btnType"
                    :btn-text="statusText"
                    :btn-disabled="!isBtnAble"
                    :btn-loading="btnLoading"
                    @btn-click="handleCouponClick"
                />
            </div>
        </div>
        <div v-if="hasMoreCount" class="coupon-more-count-bg"></div>
    </div>
</template>

<style>
:root {
    /* 分隔线颜色 */
    --adapt-coupon-split-line-color: #ffdae4;
    /* 左侧文字色 */
    --adapt-coupon-value-color: #ff1b46;
}
</style>

<style lang="scss" scoped>
@use 'sass:map';
$coupon-size: (
    'mini': (
        'width': 204px,
        'height': 70px,
        'sideWidth': 70px,
        'background': url('./assets/bg_mini.png'),
    ),
    'small': (
        'width': 246px,
        'height': 86px,
        'sideWidth': 90px,
        'background': url('./assets/bg_normal.png'),
    ),
    'medium': (
        'width': 268px,
        'height': 86px,
        'sideWidth': 90px,
        'background': url('./assets/bg_normal.png'),
    ),
    'large': (
        'width': 382px,
        'height': 86px,
        'sideWidth': 90px,
        'background': url('./assets/bg_normal.png'),
    ),
);

$coupon-radius: 4px;
$coupon-split-line-color: var(--adapt-coupon-split-line-color);
$coupon-ctrl-right-gap: 16px;
$coupon-value-color: var(--adapt-coupon-value-color);
$coupon-tag-background: rgba($coupon-value-color, 0.1);

.coupon-card {
    position: relative;
    display: flex;
    background-repeat: no-repeat;
    background-size: auto 100%;
    border-radius: $coupon-radius;
    align-items: center;
    &.no-bg {
        background: none !important;
    }
    /* 卡片尺寸 */
    @each $size, $info in $coupon-size {
        &.card-#{$size} {
            width: map.get($info, 'width');
            height: map.get($info, 'height');
            background-image: map.get($info, 'background');
            .coupon-side {
                width: map.get($info, 'sideWidth');
            }
            .coupon-divider {
                left: map.get($info, 'sideWidth');
                transform: translateX(-50%);
                @if $size == mini {
                    top: 16px;
                    bottom: 16px;
                }
            }
        }
    }
}
.coupon-side {
    color: $coupon-value-color;
}
.coupon-tag {
    position: absolute;
    left: 0;
    top: 0;
    border-radius: $coupon-radius 0 $coupon-radius 0;
    font-weight: 400;
    font-size: 9px;
    line-height: 11px;
    padding: 2px 4px;
    overflow: hidden;
    &::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: $coupon-value-color;
        opacity: 0.1;
    }
    b {
        position: relative;
    }
}
.coupon-count {
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 0 $coupon-radius 0 $coupon-radius;
    font-weight: 400;
    font-size: 9px;
    line-height: 11px;
    padding: 2px 4px;
    background: $coupon-tag-background;
}
.coupon-divider {
    /* prettier-ignore */
    border-right: 1PX dashed $coupon-split-line-color;
    position: absolute;
    top: 6px;
    bottom: 6px;
}
.coupon-main {
    flex: 1 1 0%;
    margin-right: 12px;
    margin-left: 14px;
}
.coupon-ctrl {
    padding-right: $coupon-ctrl-right-gap;
    display: flex;
}
.coupon-status {
    vertical-align: top;
    margin-right: 0 - $coupon-ctrl-right-gap;
}

/* 24CNY加入多张券 */
.coupon-content:not(.no-bg) {
    width: fit-content;
    box-shadow: 0px 2px 8px 0px rgba(80, 0, 0, 0.04);
}

.coupon-more-count {
    position: relative;
    z-index: 1;
    margin-bottom: 20px;
}
.coupon-more-count-bg {
    position: absolute;
    top: calc(100% - 4px);
    width: 100%;
    height: 12px;
    background-image: url('./assets/bg_more.png');
    background-repeat: no-repeat;
    background-size: 100%;
    z-index: -1;
}
</style>
