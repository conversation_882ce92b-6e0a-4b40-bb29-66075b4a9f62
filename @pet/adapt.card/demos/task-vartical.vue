<pet-info lang="json">
{ "title": "任务面板内-纵向", "description": "常规通用列表，适用于任务面板", "priority": 9 }
</pet-info>
<script lang="ts" setup>
import BoxItem from '@pet/adapt.box/box-item.vue';
import Box from '@pet/adapt.box/box.vue';

import Card from '../index.vue';
const taskInfo = {
    figure: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '抽新年福签',
    desc: '抽福签得礼物，愿望金+10',
    buttonText: '去打开',
    isLoading: false,
    isDisabled: false,
};
</script>

<template>
    <div class="card-wrapper">
        <Box :items="2" :gap-col="10">
            <BoxItem>
                <Card v-bind="taskInfo" vertical></Card>
            </BoxItem>
            <BoxItem>
                <Card v-bind="taskInfo" vertical></Card>
            </BoxItem>
        </Box>
    </div>
</template>

<style scoped>
.card-wrapper {
    background: #f5f5f5;
    padding: 10px;
}
</style>
