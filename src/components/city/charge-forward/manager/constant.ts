import { GridShape, type BuildingConfig, type BuildingType, type GridllwardConfig, type MapConfig } from './config';
// import FuRao from '../assets/bg/furao.jpg';
// import <PERSON><PERSON><PERSON> from '../assets/bg/jinghua.jpg';
// import Xi<PERSON>e from '../assets/bg/xile.jpg';

// demo测试模拟数据
// demo地图配置
// export const JingHuaMapConfig: MapConfig = {
//     imgs: {
//         width: 414,
//         height: 1799,
//     },
//     offset: -1799 / 2 - 8,
//     maskHeight: 140 /** mask 完全不透明高度 */,
//     gridShape: GridShape.Square,
// };

// export const XiLeMapConfig: MapConfig = {
//     imgs: {
//         width: 414,
//         height: 1799,
//     },
//     offset: -1799 / 2 - 158 * 3 + 16,
//     maskHeight: 140,
//     gridShape: GridShape.Square,
// };

// export const FuRaoMapConfig: MapConfig = {
//     imgs: {
//         width: 414,
//         height: 1799,
//     },
//     offset: -1799 / 2 + 20 - 30,
//     maskHeight: 140,
//     gridShape: GridShape.Square,
// };

// export const FengShouMapConfig: MapConfig = {
//     imgs: {
//         width: 414,
//         height: 1799,
//     },
//     offset: -1799 / 2 - 8,
//     maskHeight: 140,
//     gridShape: GridShape.Square,
// };

export const CityMapConfig: MapConfig = {
    imgs: {
        width: 414,
        height: 896,
    },
    // offset: -1799 / 2 - 8,
    // maskHeight: 140,
    gridShape: GridShape.Square,
};

export enum MapKeyType {
    City = 'city',
    ChongQing = 'bd-chongQing',
    ShenYang = 'bd-shenYang',
    Default = 'default',
}

// station key -> map key
export enum StationMapEnum {
    'bigday-chongqing-1' = MapKeyType.ChongQing,
    'bigday-shenyang-1' = MapKeyType.ShenYang,
}

export const MapConfigData: Record<MapKeyType, MapConfig> = {
    [MapKeyType.City]: CityMapConfig,
    [MapKeyType.ChongQing]: CityMapConfig,
    [MapKeyType.ShenYang]: CityMapConfig,
    [MapKeyType.Default]: CityMapConfig,
};

export const RenderZIndex = {
    // station: 85,
    // IP: 90,
    // Llwd: 89,
    // Building: 87,
    // GridSkin: 80,
    CurrentGrid: 3, // 格子反馈
    GridSkin: 10, // lowest zIndex
    // Building: 20, // since building is sorted, this is not used
    HigherIP: 51, //base zIndex
    LowerIP: 48,
};

// demo格子奖励配置
// export const DemoGridllwardConfig: GridllwardConfig = {
//     demo: {
//         icon: '',
//         steps: [3, 4, 5, 10, 12, 14, 16, 17, 20],
//     },
// };

// 建筑格子奖励配置

// 每八个格子有三个空位，索引从0开始
// 假设每一组loop有9个空位,在一个loop里pos代表对应的建筑出现的空位索引
// export const DemoBuildingConfig: BuildingConfig = {
//     interval: 9,
//     data: {
//         demo2: {
//             type: 'decoration' as BuildingType,
//             iconUrl: 'https://p5-pro.kskwai.com/kcdn/cdn-kcdn112307/guestTree/she.png',
//             pos: [2, 3, 4],
//             width: 124,
//             height: 124,
//         },
//     },
// };
