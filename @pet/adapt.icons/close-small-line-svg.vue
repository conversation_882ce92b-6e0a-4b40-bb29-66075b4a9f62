<template>
    <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class="icon-close-small-line"
    >
        <circle cx="8" cy="8" r="7.5" stroke="currentColor" />
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M10.9251 5.07456C10.6559 4.80532 10.2194 4.80532 9.95015 5.07456L7.9999 7.0248L6.04976 5.07466C5.78052 4.80542 5.34399 4.80542 5.07476 5.07466C4.80552 5.3439 4.80552 5.78042 5.07476 6.04966L7.0249 7.9998L5.07515 9.94956C4.80591 10.2188 4.80591 10.6553 5.07515 10.9246C5.34439 11.1938 5.78091 11.1938 6.05015 10.9246L7.9999 8.9748L9.94976 10.9247C10.219 11.1939 10.6555 11.1939 10.9248 10.9247C11.194 10.6554 11.194 10.2189 10.9248 9.94966L8.9749 7.9998L10.9251 6.04956C11.1944 5.78032 11.1944 5.3438 10.9251 5.07456Z"
            fill="currentColor"
        />
    </svg>
</template>

<style>
:root {
    /* 箭头默认颜色 */
    --adapt-icon-close-small-line-color: #ff4185;
}
</style>

<style lang="scss" scoped>
.icon-close-small-line {
    color: var(--adapt-icon-close-small-line-color);
}
</style>
