<script lang="ts" setup>
import Img from './index.vue';
import { useCdnLevel } from './useCdnLevel';

const { switchCdnLevel } = useCdnLevel();
</script>

<template>
    <button @click="switchCdnLevel('NONE')">none</button>
    <button @click="switchCdnLevel('P2')">P2</button>
    <button @click="switchCdnLevel('P1')">P1</button>
    <button @click="switchCdnLevel('P0')">P0</button>
    <div>
        <!-- P2 降级 -->
        <span> p2: </span>
        <Img
            :class="'image'"
            :cdnLevel="'P2'"
            :src="'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-02-15/1676454395080.b22f5e2752b1bb23.png'"
        />
    </div>
    <div>
        <!-- P1 降级 -->
        <span> p1换图: </span>
        <Img
            :class="'image'"
            :cdnLevel="'P1'"
            :src="'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-02-15/1676454395080.b22f5e2752b1bb23.png'"
            :emptyImage="'https://ali-ad.a.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2023-10-13/1697182555145.4afab1d5c9764c34.png'"
        />
    </div>
    <div>
        <!-- P1 降级显示 -->
        <span> p1显示: </span>
        <Img
            :class="'image'"
            :cdnLevel="'P1'"
            downgradeType="hidden"
            :src="'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-02-15/1676454395080.b22f5e2752b1bb23.png'"
        />
    </div>
    <div>
        <!-- P0 降级 -->
        <span> p0文字: </span>
        <Img
            :class="'image'"
            :cdnLevel="'P0'"
            :alt="'快手'"
            :src="'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-02-15/1676454395080.b22f5e2752b1bb23.png'"
        />
    </div>
</template>

<style lang="scss" scoped>
span {
    font-size: 20px;
    display: inline;
}

.image {
    display: inline;
    height: 30px;
}
</style>
