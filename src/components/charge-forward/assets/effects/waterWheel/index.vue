<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Picture from "@pet/quantum.PictureImg";
import TVPlayer from '@pet/summer.tvplayer/index.vue';

import { useAnimationPause } from '@/models/animationVisibility';

import waterPng from './assets/fallback.png';
import waterDown from './assets/fallback.png?preset=modern';
import waterMov from './assets/water.mov?url';
import waterMp4 from './assets/water.mp4?url';
import waterWebm from './assets/water.webm?url';
const { effectShowStatus } = useDowngradeLevel();
const { isAnimationPause } = useAnimationPause()
const emit = defineEmits<{
    (e: 'ended'): void;
}>();

const urls = {
    mp4: waterMp4,
    mov: waterMov,
    webm: waterWebm,
}

</script>
<template>
    <TVPlayer v-if="effectShowStatus.L1" id="8357" class="tv-wrapper" :urls="urls" :autoplay="true" :loop="true" :placeholder="waterPng" :play="!isAnimationPause" @ended="emit('ended')"/>
    <Picture v-else class="tv-wrapper" :src="waterDown" />
</template>
<style lang="scss" scoped>
.tv-wrapper {
    position: absolute;
    width: 90px;
    height: 150px;
    overflow: hidden;
    left: -20px;
    top: -40px;
}
</style>
