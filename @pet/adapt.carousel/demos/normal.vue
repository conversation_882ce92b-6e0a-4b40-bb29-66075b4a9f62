<pet-info lang="json">
{ "title": "基本用法", "description": "跑马灯弹幕", "priority": 3 }
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';

import { carousel, carouselItem } from '../index';

const headUrl = 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg';

const data = ref(
    Array.from({ length: 10 }).map((x, i) => ({
        i,
        headUrl,
        name: '快手极速版',
        desc: `买了高级礼包`,
    })),
);
</script>

<template>
    <div class="demo">
        <div class="title">默认样式、展示两条、滚动时间2s、全动效</div>
        <div class="container">
            <carousel :list="data">
                <template #default="{ item }">
                    <carouselItem :head="item.headUrl" :name="item.name" :desc="`${item.desc}X${item.i}`">
                    </carouselItem>
                </template>
            </carousel>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.demo {
    .title {
        font-size: 16px;
    }
}
.container {
    margin-top: 100px;
}
</style>
