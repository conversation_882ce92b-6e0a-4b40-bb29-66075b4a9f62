<script lang="ts" setup></script>
<template>
    <svg class="invite-svg" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M14 18.9629C14 19.7913 13.3284 20.4629 12.5 20.4629C11.6716 20.4629 11 19.7913 11 18.9629C11 18.1345 11.6716 17.4629 12.5 17.4629C13.3284 17.4629 14 18.1345 14 18.9629ZM21 18.9629C21 19.7913 20.3284 20.4629 19.5 20.4629C18.6716 20.4629 18 19.7913 18 18.9629C18 18.1345 18.6716 17.4629 19.5 17.4629C20.3284 17.4629 21 18.1345 21 18.9629ZM26.5 20.4629C27.3284 20.4629 28 19.7913 28 18.9629C28 18.1345 27.3284 17.4629 26.5 17.4629C25.6716 17.4629 25 18.1345 25 18.9629C25 19.7913 25.6716 20.4629 26.5 20.4629Z"
            fill="currentColor"
        />
    </svg>
</template>
<style lang="scss" scoped>
.invite-svg {
    color: var(--adapt-avatar-icon-color);
    vertical-align: top;
    width: 100%;
    height: 100%;
}
</style>
