/* eslint-disable no-param-reassign,no-underscore-dangle */
/**
 * base.guide-directive 组件衍生
 * !! 使用时必须进行 initWeakGuideStore 存储初始化，以及 startGuideShow 开始弱引导
 * 可以参考 apps/eve-h5/src/modules/test/TestGuide.vue
 *
 * 用 showByFunc 标识 支持函数触发 showGuideById，不会自动触发
 * target 类型支持倒计时关闭
 * update 不会触发 guide show
 * autoScroll 支持滚动到锚点位置
 * everydayLimitCount 每日频控策略
 * totalLimitCount 总频控
 * important 强插
 * nonInterval 无操作间隔时间后再出，需要在 vue 实例化后 进行 initWeakGuideInstance
 */
import useLocalStorageUtils from './plugin/useLocalStorageUtils';
import { px2rem } from '@pet/core.mobile';
import { createGlobalState, until } from '@vueuse/core';
import type { Component, App, ObjectDirective } from 'vue';
import { createApp, ref } from 'vue';
import type { RawActivityStore } from '@pet/yau.yoda/local-storage';
import type { GuideType, GuideDirectiveOptions, GuideOptions, GuideShowOptions } from './type';
import type { EventData } from '@pet/adapt.queue-api/mainModules/eventTask/type';
import type { Task } from '@pet/adapt.queue-api/main/TaskQueue';
import { useWeakGuideTimeAwait } from './plugin/useWeakGuideTimeAwait';

const GUIDED_FREQUENCY_KEY = 'GUIDED_FREQUENCY_KEY';
type PluginFunction = (item: GuideDirectiveOptions) => boolean | void;
type InitGuideDirectiveType = {
    store: Pick<RawActivityStore, 'get' | 'set'>;
    addEventTask?: (params: EventData) => Task<void, void>;
    guideActionStart?: (key: string) => void;
    guideActionEnd?: (key: string) => void;
    plugin?: {
        guideBeforeAdd?: PluginFunction[];
        guideAfterAdded?: PluginFunction[];
        guideBeforeShow?: PluginFunction[];
        guideAfterShowed?: PluginFunction[];
        guideAfterHided?: PluginFunction[];
    };
    isDebug?: boolean;
};

// todo 验证流程，添加注释以及 debug 信息
export const useGuideState = createGlobalState(() => {
    /** 收集guide点位以及信息 */
    const guideMap = ref<Map<string, GuideOptions>>(new Map());

    /** 引导队列 */
    const queue = ref<Array<GuideOptions>>([]);

    /** 当前引导 */
    const currentGuide = ref<GuideOptions | null>(null);

    /** 是否初始化完毕 */
    const isInit = ref<boolean>(false);

    /** 是否开始引导 */
    const isStart = ref<boolean>(false);

    /** 引导存储实例（需要初始化） */
    const guideStore = ref<Pick<RawActivityStore, 'get' | 'set'>>();

    /** 弹窗队列添加 */
    const addGuideTask = ref<(params: EventData) => Task<void, void>>();
    // 检查频控函数
    const checkLimit = ref<((key: string, limit: number, totalLimit?: number) => Promise<boolean>) | null>(null);
    // 记录频控函数
    const recordOnce = ref<((key: string, limit: number, totalLimit?: number) => Promise<void>) | null>(null);

    /** 间隔等待暂停（需要初始化） */
    const nonIntervalCancelFn = ref(() => {});
    /** 等待间隔函数（需要初始化） */
    const nonIntervalAwaitFn = ref<(timeout: number) => Promise<boolean>>();
    /** 是否间隔等待 */
    const isInterval = ref<boolean>(false);

    /** 打点开始函数（需要初始化） */
    const keyActionStart = ref<(key: string) => void>(() => {});

    /** 打点结束函数（需要初始化） */
    const keyActionEnd = ref<(key: string) => void>(() => {});

    /** 生命周期（加入队列前置校验） */
    const beforeAdd = ref<PluginFunction[]>([]);
    /** 生命周期（加入队列后操作） */
    const afterAdded = ref<PluginFunction[]>([]);
    /** 生命周期（显示引导前操作） */
    const beforeShow = ref<PluginFunction[]>([]);
    /** 生命周期（显示引导后操作） */
    const afterShowed = ref<PluginFunction[]>([]);
    /** 生命周期（隐藏引导后操作） */
    const afterHided = ref<PluginFunction[]>([]);

    /** 调试模式 */
    const isDebugMode = ref<boolean>(false);

    /** 点击触发器 */
    const clickHandler = ref<(e: TouchEvent) => Promise<void>>(async () => {});

    /** 初始化引导（可以放在app.vue 下面执行） */
    const initGuideDirective: (props: InitGuideDirectiveType) => void = ({
        store,
        addEventTask,
        guideActionStart,
        guideActionEnd,
        plugin,
        isDebug,
    }) => {
        const { nonIntervalAwait, nonIntervalCancel } = useWeakGuideTimeAwait();
        nonIntervalAwaitFn.value = nonIntervalAwait;
        nonIntervalCancelFn.value = nonIntervalCancel;

        // 存储是必要的
        guideStore.value = store;

        if (guideActionStart && guideActionEnd) {
            keyActionStart.value = guideActionStart;
            keyActionEnd.value = guideActionEnd;
        }

        if (plugin) {
            beforeAdd.value = plugin.guideBeforeAdd ?? [];
            afterAdded.value = plugin.guideAfterAdded ?? [];
            beforeShow.value = plugin.guideBeforeShow ?? [];
            afterShowed.value = plugin.guideAfterShowed ?? [];
            afterHided.value = plugin.guideAfterHided ?? [];
        }

        const { checkGuideLimit, recordGuideOnce } = useLocalStorageUtils(guideStore.value);
        checkLimit.value = checkGuideLimit;
        recordOnce.value = recordGuideOnce;
        addGuideTask.value = addEventTask;
        isInit.value = true;
        isDebugMode.value = isDebug ?? false;
        isDebugMode.value && console.log('【guide init】初始化完毕:', { queue });
    };

    /** 开始引导（开关，不调用则不会有引导出现） */
    const startGuideShow = async () => {
        if (!isInit.value) {
            isDebugMode.value &&
                console.log('【guide start】开始引导失败:', { queue: queue.value, isInit: isInit.value });
            // 等到 引导init后重新出发 start
            await until(isInit).toBeTruthy();
            startGuideShow();
            return;
        }

        if (isStart.value) {
            return;
        }

        isStart.value = true;
        isDebugMode.value && console.log('【guide start】开始引导:', { queue: queue.value, isInit: isInit.value });
        checkGuideBeforeShow();
    };

    /** 记录信息并添加队列 */
    const addGuideInfo = async (item: GuideOptions) => {
        guideMap.value.set(item.id, item);

        addGuide({ ...item, onlyOne: true });
    };

    /** 更新引导信息 */
    const updateGuideInfo = async (item: GuideOptions) => {
        guideMap.value.set(item.id, item);

        const findIndex = queue.value.findIndex((queueItem) => item.id === queueItem.id);

        if (findIndex > 0) {
            isDebugMode.value &&
                console.log(`【guide update ${item.id}】更新引导信息触发引导:`, {
                    item,
                    queue: queue.value,
                });
            removeGuide(item.id);
            addGuide(item);
        }
    };

    /** 移除引导信息 */
    const removeGuideInfo = async (id: string) => {
        guideMap.value.delete(id);
        currentGuide.value?.el?.__destroyGuide?.();
        removeGuide(id);
    };

    /** 通过任务id直接控制指定任务出手势引导 */
    const showGuideById = (id: string, options?: GuideShowOptions) => {
        const item = guideMap.value.get(id);

        if (item) {
            addGuide({ ...item, ...options });
        }
    };

    /** 添加引导进入队列 */
    const addGuide = async (item: GuideDirectiveOptions) => {
        const guideInfo = { ...guideMap.value.get(item.id), ...item } as GuideOptions;

        // 是否能展示
        if (!item.enabled || !guideInfo.el) {
            // isDebugMode.value &&
            //     console.log(`【guide add ${guideInfo.id}】添加引导失败:`, {
            //         guide: guideInfo,
            //         queue: queue.value,
            //         currentGuide: currentGuide.value,
            //     });
            return;
        }

        // 频控校验
        if ((guideInfo.everydayLimitCount || guideInfo.totalLimitCount) && checkLimit.value) {
            const isOverLimit = await checkLimit.value(
                `${GUIDED_FREQUENCY_KEY}_${guideInfo.id}`,
                guideInfo.everydayLimitCount || guideInfo.totalLimitCount || 0,
                guideInfo.totalLimitCount,
            );

            if (isOverLimit) {
                isDebugMode.value &&
                    console.log(`【guide add ${item.id}】添加引导失败(频控上限):`, {
                        guide: item,
                        queue: queue.value,
                        currentGuide: currentGuide.value,
                    });
                guideInfo.overLimit?.()
                return;
            }
        }

        // plugin 调用
        if (beforeAdd.value.length) {
            // 循环执行函数,若返回 false 则退出 addGuide
            for (const fn of beforeAdd.value) {
                const result = fn(guideInfo);
                if (result === false) {
                    isDebugMode.value &&
                        console.log(`【guide add ${item.id}】添加引导失败:`, {
                            fn: fn,
                            result,
                            guide: item,
                            queue: queue.value,
                            currentGuide: currentGuide.value,
                        });
                    return;
                }
            }
        }

        if (item.onlyOne) {
            removeGuide(item.id);
        }

        // 优先级控制
        if (guideInfo?.important) {
            queue.value.unshift({ ...guideInfo, priority: Infinity });
            currentGuide.value && currentGuide.value?.el?.__destroyGuide?.();
        } else {
            queue.value.push(guideInfo);
            queue.value.sort((itemA, itemB) => itemB.priority - itemA.priority);
        }

        if (isStart.value) {
            // 如果已经开始引导了，且没有正在展示的引导，则开始展示
            checkGuideBeforeShow();
        }

        // plugin 调用
        if (afterAdded.value.length) {
            for (const fn of afterAdded.value) {
                fn(guideInfo);
            }
        }

        isDebugMode.value &&
            console.log(`【guide added ${item.id} 】引导加入队列:`, {
                item,
                queue: queue.value,
            });
    };

    /** 移除队列 */
    const removeGuide = (item: GuideDirectiveOptions | string) => {
        queue.value = queue.value.filter((oldItem) => oldItem.id !== item && oldItem !== item);
    };

    /** 前置校验(无操作间隔校验，避让逻辑) */
    const checkGuideBeforeShow = () => {
        const guide = queue.value?.[0];

        // 当前有等待间隔的任务
        if (isInterval.value && currentGuide.value !== guide) {
            isDebugMode.value &&
                console.log(`【guide check ${guide?.id} 】有优先级更高的引导需要插入无操作等待`, {
                    guide,
                    queue: queue.value,
                    currentGuide: currentGuide.value,
                });
            // 下一个要展示的任务和有间隔的任务不一致(优先级更高)，取消间隔等待，触发一下一个显示
            nonIntervalCancelFn.value();
        }
        setTimeout(() => {
            if (currentGuide.value || !isInit.value || !guide) {
                return;
            }

            if (!currentGuide.value) {
                currentGuide.value = guide;
            }

            isDebugMode.value &&
                console.log(`【guide check  ${guide.id} 】引导显示前置校验完毕`, { guide, queue: queue.value });

            if (addGuideTask.value) {
                addGuideTask.value({
                    event: () =>
                        new Promise((resolve, reject) => {
                            guideShow(guide, resolve).catch((err) => {
                                console.error('guide error: ' + err);
                                currentGuide.value?.el?.__destroyGuide?.();
                                // 没有触发 __destroyGuide 销毁则手动处理
                                if (currentGuide.value) {
                                    if (currentGuide.value?.el?.__guideComponent) {
                                        currentGuide.value?.el?.__guideComponent?.unmount();
                                    }
                                    currentGuide.value = null;
                                    checkGuideBeforeShow();
                                }
                                reject();
                            });
                        }),
                    options: {
                        queueTags: {
                            conflictTags: ['guide'],
                            avoidToTags: guide?.important ? [] : ['popup', 'sheet', 'event', 'task-sheet-popup'],
                        },
                        name: 'HAND_GUIDE',
                        // 引导的优先级最低，有弹窗就可以被延后
                        priority: -1,
                    },
                });
            } else {
                guideShow(guide);
            }
        }, 0);
    };

    /** 引导显示 */
    const guideShow = async (guideInfo: GuideOptions, resolve?: (value: void | PromiseLike<void>) => void) => {
        currentGuide.value = guideInfo;

        /** 等待间隔过程 */
        if (currentGuide.value?.nonInterval && nonIntervalAwaitFn.value) {
            isInterval.value = true;
            // 等待执行，失败则退出
            const intervalFail = !(await nonIntervalAwaitFn.value(currentGuide.value.nonInterval!));
            isInterval.value = false;
            if (intervalFail) {
                currentGuide.value = null;
                isDebugMode.value &&
                    console.log(`【guide show ${guideInfo.id} 】等待无操作失败（被强插）`, {
                        guideInfo,
                        queue: queue.value,
                    });
                resolve?.();
                return;
            }
        }

        // plugin
        if (beforeShow.value.length) {
            // 循环执行函数,若返回 false 则退出 addGuide
            for (const fn of beforeShow.value) {
                fn(guideInfo);
            }
        }

        if ((guideInfo.everydayLimitCount || guideInfo.totalLimitCount) && recordOnce.value) {
            try {
                await recordOnce.value(
                    `${GUIDED_FREQUENCY_KEY}_${guideInfo.id}`,
                    guideInfo.everydayLimitCount || guideInfo.totalLimitCount || 0,
                    guideInfo.totalLimitCount,
                );
            } catch {
                console.error('【guide show】recordOnce 失败');
            }
        }

        // 组件props
        await guideInfo.beforeShow?.();
        const component = await loadComponent(guideInfo.component);
        const mountRoot = document.createElement('div');
        const instance = createApp(component, {
            guideId: guideInfo.id,
            ...guideInfo.props,
            tips: guideInfo?.tipsText || (guideInfo?.props?.tips as string) || '',
        });
        instance.mount(mountRoot);

        // 滚动到目标dom
        guideInfo.autoScroll &&
            guideInfo.el.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
            });

        setGuideStyle(mountRoot, guideInfo);
        guideInfo.el.appendChild(mountRoot);

        guideInfo.el.__guideComponent = instance;

        isDebugMode.value &&
            console.log(`【guide show ${guideInfo.id}】引导显示`, {
                guideInfo,
                queue: queue.value,
                component,
            });

        /** 不管类型，都支持倒计时 */
        const timer =
            guideInfo.duration &&
            setTimeout(() => {
                guideInfo.el?.__destroyGuide?.();
            }, guideInfo.duration);

        // plugin
        if (afterShowed.value.length) {
            for (const fn of afterShowed.value) {
                fn(guideInfo);
            }
        }

        guideInfo.el.__destroyGuide = async () => {
            // 添加渐隐动画
            const fadeOutDuration = guideInfo?.fadeOutDuration ?? 0;
            // mountRoot.style.transition = `opacity ${fadeOutDuration as number}ms cubic-bezier(0.33, 0, 0.67, 1)`;
            // mountRoot.style.zIndex = '9';
            // mountRoot.style.opacity = '0';
            // 等待动画完成后再卸载
            await new Promise((resolve) => {
                setTimeout(resolve, fadeOutDuration);
            });
            instance.unmount();
            mountRoot.remove();
            // this.isShow = false;
            const index = queue.value.findIndex((guide) => guide.id === currentGuide.value?.id);
            if (index >= 0) {
                queue.value.splice(index, 1);
            }
            currentGuide.value = null;

            // 关闭上报打点
            keyActionEnd.value?.(guideInfo?.logName || `WEAK_GUIDE_${guideInfo.id}`);
            if (clickHandler.value) {
                if (guideInfo.type === 'mask') {
                    document.body.removeEventListener('touchend', clickHandler.value, true);
                } else {
                    guideInfo.el.removeEventListener('touchend', clickHandler.value, true);
                }
                clickHandler.value = async () => {};
            }
            timer !== undefined && clearTimeout(timer);
            try {
                await guideInfo.afterHide?.();
                // plugin
                if (afterHided.value.length) {
                    for (const fn of afterHided.value) {
                        fn(guideInfo);
                    }
                }
            } catch (err) {}
            // 结束
            resolve?.();
            isDebugMode.value &&
                console.log(`【guide end ${guideInfo.id}】引导关闭`, {
                    guideInfo,
                    queue: queue.value,
                    component,
                });
            checkGuideBeforeShow();
        };

        // 要在__destroyGuide后面执行，不然报错无法销毁
        guideInfo?.onShow && guideInfo.onShow();
        // 显示上报打点
        keyActionStart.value?.(guideInfo?.logName || `WEAK_GUIDE_${guideInfo.id}`);

        if (guideInfo.type === 'mask') {
            clickHandler.value = async (e: TouchEvent) => {
                guideInfo?.onClick && guideInfo.onClick();
                if (guideInfo.onMaskClick) {
                    if (await guideInfo.onMaskClick(e, guideInfo.el)) {
                        guideInfo.el?.__destroyGuide();
                    }
                } else {
                    guideInfo.el?.__destroyGuide();
                }
            };

            document.body.addEventListener('touchend', clickHandler.value, true);
        } else if (guideInfo.type === 'target') {
            clickHandler.value = async (e: TouchEvent) => {
                guideInfo?.onClick && guideInfo.onClick();
                if (guideInfo.onTargetClick) {
                    if (await guideInfo.onTargetClick(e)) {
                        guideInfo.el?.__destroyGuide();
                    }
                } else {
                    guideInfo.el?.__destroyGuide();
                }
            };

            guideInfo.el.addEventListener('touchend', clickHandler.value, true);
        }
    };

    const setGuideStyle = (el: HTMLDivElement, options: GuideOptions) => {
        el.style.position = 'absolute';
        el.style.zIndex = '100';
        (Object.keys(options.offset) as Array<'top' | 'left' | 'right' | 'bottom'>).forEach((key) => {
            if (typeof options.offset[key] !== 'undefined') {
                el.style[key] = px2rem(options.offset[key]!)!;
            }
        });
    };

    const targetElements = document.body;
    const blockGuide = () =>
        addGuideInfo({
            id: 'blockGuide',
            enabled: true,
            priority: 1,
            important: true,
            offset: {},
            component: defineAsyncComponent(() => import('./block.vue')),
            type: 'normal',
            el: targetElements,
        });

    const unblockGuide = () => removeGuideInfo('blockGuide');
    return {
        currentGuide,
        // 初始化
        initGuideDirective,
        // 开始
        startGuideShow,
        // 添加引导信息并触发 (用于挂载，添加后guide唯一)
        addGuideInfo,
        // 更新引导信息并触发（用于 directive update）
        updateGuideInfo,
        // 移除引导信息
        removeGuideInfo,
        removeGuide,
        // 函数式触发引导
        showGuideById,
        // 阻塞函数
        blockGuide,
        // 取消阻塞
        unblockGuide,
    };
});

declare global {
    interface HTMLElement {
        __guideComponent?: App;
        __destroyGuide: () => void;
    }
}

async function loadComponent(loader: (() => Promise<Component>) | Component): Promise<Component> {
    if (typeof loader === 'function') {
        const result = await (loader as () => Promise<Component>)();
        return 'default' in result ? result.default : result;
    }

    return loader as Component;
}

/** 从指令修饰符中获取 Type */
const getGuideType = (modifiers: Record<string, boolean>): GuideType =>
    modifiers.mask ? 'mask' : modifiers.target ? 'target' : 'normal';

const guideDirective: ObjectDirective<HTMLElement, GuideDirectiveOptions> = {
    mounted(el, { value, modifiers }) {
        const { addGuideInfo } = useGuideState(); // 响应式生效
        addGuideInfo({ ...value, type: getGuideType(modifiers), el });
    },

    updated(el, { value, oldValue, modifiers }) {
        const { addGuideInfo, updateGuideInfo } = useGuideState(); // 响应式生效
        // 更新不触发显示
        oldValue?.enabled
            ? updateGuideInfo({ ...value, type: getGuideType(modifiers), el })
            : addGuideInfo({ ...value, type: getGuideType(modifiers), el });
    },

    unmounted(el, { value }) {
        if (value?.id) {
            const { removeGuideInfo } = useGuideState(); // 响应式生效
            removeGuideInfo(value.id);
        }
        if (typeof el?.__destroyGuide !== 'undefined') {
            el.__destroyGuide();
        }
    },
};

export { guideDirective as vGuide };
