<pet-info lang="json">
{ "title": "布局方向", "description": "" }
</pet-info>
<script setup lang="ts">
import Box from '../box.vue';
import type { DirectionType, JustifyType, AlignType } from '../types';
const directionValues: DirectionType[] = ['row', 'row-rev', 'col', 'col-rev'];
</script>

<template>
    <div>
        <span v-for="dir in directionValues" :key="dir" class="demo-wrapper">
            <h4>direction="{{ dir }}"</h4>
            <Box :direction="dir" class="box">
                <img
                    src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                    class="img"
                />
                <img
                    src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                    class="img"
                />
            </Box>
        </span>
    </div>
</template>

<style lang="scss" scoped>
.box {
    width: 200px;
    height: 200px;
    background: #f5f5f5;
    position: relative;
    &::before {
        position: absolute;
        top: 0;
        left: 0;
        content: 'flex-box';
        background: #999;
        color: #fff;
        font-size: 12px;
        padding: 2px 4px;
        border-radius: 0 0 5px 0;
    }
    & > img {
        position: relative;
        width: 60px;
        height: 60px;
    }
}

h4 {
    font-size: 18px;
    line-height: 28px;
    margin: 16px 0;
}
</style>
