import { ref, createApp, h } from 'vue';
import type { App, Component, ComponentPublicInstance } from 'vue';
import Notice, { type NoticeProps } from './index.vue';

export interface NoticeOptions extends NoticeProps {
    onHide?: () => void;
    onAfterLeave?: () => void;
    customComponent?: Component;
}

export function useNotice(currentInstance?: ComponentPublicInstance) {
    let app: App | null;
    let noticeContainer: HTMLElement | null;
    const showNotice = ref(false);

    function createContainer() {
        const noticeContainerEl = document.createElement('div');
        const rootContainer = document.querySelector('body');
        rootContainer?.appendChild(noticeContainerEl);
        return noticeContainerEl;
    }

    function destroyCurrentInstance() {
        if (app) {
            app.unmount();
            app = null;
            noticeContainer?.remove();
            noticeContainer = null;
        }
    }

    function createNotice(options?: NoticeOptions) {
        const propsData = ref(options);
        showNotice.value = true;

        return new Promise<void>((resolve) => {
            function onHide() {
                if (options?.onHide) {
                    options.onHide();
                }
                showNotice.value = false;
            }

            function onAfterLeave() {
                if (options?.onAfterLeave) {
                    options.onAfterLeave();
                }
                destroyCurrentInstance();
                resolve();
            }

            const ProgressToastComponent = {
                render() {
                    const customComponent = options?.customComponent;
                    return h(
                        Notice,
                        {
                            ...propsData.value,
                            show: showNotice.value,
                            onHide,
                            onAfterLeave,
                        },
                        {
                            default: customComponent ? () => h(customComponent) : undefined,
                        },
                    );
                },

                provide() {
                    return currentInstance?.$?.appContext?.provides ?? {};
                },
            };

            if (!app) {
                noticeContainer = createContainer();
                app = createApp(ProgressToastComponent);
                app.mount(noticeContainer);
            }
        });
    }

    return {
        createNotice,
    };
}
