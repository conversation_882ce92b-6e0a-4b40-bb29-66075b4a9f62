<script setup lang="ts">
/**
 * pet组件使用示例。带动效平台导出尺寸，可直接使用
 * 动效预览：https://vision.corp.kuaishou.com/effect/9332
 *
 * 依赖pet组件 effect-item@^0.4.11
 * 更多组件使用方式、功能见：
 * https://pet-web.corp.kuaishou.com/gallery/detail/?name=@pet/vision.effect-item&version=
 */
import EffectItem from '@pet/vision.effect-item/index.vue';
import { preload } from '@vision/runtime';

const emit = defineEmits<{
    (e: 'ended'): void;
}>();

// 如果需要依赖动效播放结束ended 事件触发其他逻辑，强烈建议修改effectDuration和在下面添加 :race-end="effectDuration" 保证ended触发。详细见pet组件描述
// const effectDuration = 2000;

// 默认使用dynamic import传入，或可以改成其他promise作异步加载
const effectData = import('./config').then((mod) => mod.default);

// 使用import同步加载动效数据。⚠️ pet组件<0.4.9 只支持这种方式使用
// import effectData from './config';

// 使用async function控制加载时机
// const effectData = async () => { /* do something */ return import('./config').then((mod) => mod.default); }

// 如果遇到动效资源加载慢的问题，可以使用preload 进行资源预加载。注意预加载逻辑可能会占用网络带宽
// preload(effectData);
</script>

<template>
    <EffectItem class="effect-wrapper" :data="effectData" :loop="false" autoplay @ended="emit('ended')"></EffectItem>
</template>

<style scoped>
.effect-wrapper {
    position: relative;
    width: 414px;
    height: 230px;
    top: 10px;
    overflow: hidden;
}
</style>
