<pet-info lang="json">
{ "title": "基本用法，默认flex布局", "description": "" }
</pet-info>
<script setup lang="ts">
import Box from '../box.vue';
</script>

<template>
    <Box justify="center" align="center" class="box">
        <img
            src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
            class="img"
        />
    </Box>
</template>

<style lang="scss" scoped>
.box {
    width: 200px;
    height: 200px;
    background: #f5f5f5;
    position: relative;
    &::before {
        position: absolute;
        top: 0;
        left: 0;
        content: 'flex-box';
        background: #999;
        color: #fff;
        font-size: 12px;
        padding: 2px 4px;
        border-radius: 0 0 5px 0;
    }
    & > img {
        width: 80px;
        height: 80px;
    }
}
</style>
