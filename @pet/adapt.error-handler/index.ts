import { isNotNil } from '@pet/yau.core/asserts';
import { h, createApp } from 'vue-demi';
import type { Router } from 'vue-router';

import ErrorPage from './error-page.vue';
import LoginMask from './login-mask.vue';
import { ErrorCode, NoLoginCode } from './const';

const ErrorPageName = 'ErrorPage';

export { ErrorPage, ErrorPageName };

type RequestInfo = {
    url: string;
    requestInit?: RequestInit;
};

export interface ThemeInfo {
    customClass?: string;
    bgColor?: string | string[];
    color?: string;
}

/**
 * key: route name 路由名称
 */
export interface ThemeInfos {
    [key: string]: ThemeInfo;
}

export interface CodeInfos {
    [key: number]: string;
}

type UrlOrPredicate =
    | string
    | {
          url: string;
          predicate: (params: RequestInfo, resp: ResponseData) => boolean;
      };

type RouteNameOrConfig =
    | string
    | {
          name: string;
          autoLogin: boolean;
      };

export interface errorOptions {
    /**
     * 需要拦截的接口列表
     */
    blacklist?: UrlOrPredicate[];
    /**
     * 需要拦截登录的接口列表，会创建一个透明遮罩触发后引导用户登录
     */
    needLoginList?: RouteNameOrConfig[];
    /**
     * 主题配置
     */
    themeInfos?: ThemeInfos;
    /**
     * 全局拦截自定义错误码
     */
    errorCodeInfos?: CodeInfos;
}

export interface ResponseData {
    result: number;
    data?: Record<string, any> | Array<any>;
    error_msg?: any;
    message?: any;
}

let hasEffect = false;
let hasLoginMask = false;

function stringifyTheme(info?: ThemeInfo) {
    if (!info) {
        return {};
    }

    let bgColor = '';

    if (Array.isArray(info.bgColor)) {
        bgColor = info.bgColor.join(',');
    } else if (typeof info.bgColor === 'string') {
        bgColor = info.bgColor;
    }

    return {
        ...info,
        bgColor,
    };
}

function createLoginMask(autoLogin: boolean) {
    const app = createApp({
        render() {
            return h(LoginMask, {
                autoLogin,
            });
        },
    });
    const loginContainer = document.createElement('div');
    const rootContainer = document.querySelector('body');
    rootContainer?.appendChild(loginContainer);
    app.mount(loginContainer);
}

function needBlock(params: RequestInfo, resp: ResponseData, options?: errorOptions) {
    if (!options?.blacklist) {
        return false;
    }

    return options.blacklist.some((item) => {
        if (typeof item === 'string') {
            return params.url.includes(item);
        }
        return params.url.includes(item.url) && item.predicate(params, resp);
    });
}

function needMaskLogin(routeName: string, options?: errorOptions) {
    if (!options?.needLoginList) {
        return undefined;
    }

    return options?.needLoginList?.find((item) => {
        if (typeof item === 'string') {
            return item === routeName;
        }
        return item.name === routeName;
    });
}

function needAutoLogin(item: RouteNameOrConfig) {
    if (typeof item === 'string') {
        return false;
    }
    return item.autoLogin;
}

export function errorHandler(
    /**
     * 错误请求信息
     */
    params: RequestInfo,
    /**
     * 错误响应数据
     */
    error: ResponseData,
    router: Router,
    /**
     * 错误路由名称
     */
    errorRouteName = 'error',
    /**
     * 错误配置信息
     */
    options?: errorOptions,
) {
    if (hasEffect) {
        return;
    }

    const isNeedBlock = needBlock(params, error, options);
    const currentRouteName = router.currentRoute.value.name ?? '';
    const isNeedMaskLogin = needMaskLogin(currentRouteName.toString(), options);
    const backUrl = router.currentRoute.value.fullPath;
    const backRouteName = router.currentRoute.value.name ?? '';
    const themesInfo = stringifyTheme(options?.themeInfos?.[backRouteName.toString()]);

    // 创建透明登录层
    if (isNotNil(isNeedMaskLogin) && NoLoginCode.includes(error.result)) {
        if (!hasLoginMask) {
            const autoLogin = needAutoLogin(isNeedMaskLogin);
            createLoginMask(autoLogin);
            hasLoginMask = true;
        }
        return;
    }

    // 只要result不为1，不管什么情况就都进到兜底页
    if (isNeedBlock) {
        hasEffect = true;
        let errMsg = error.message || error.error_msg || '活动太火爆了，请稍后重试';
        let errCode = error.result as unknown as string;
        if (currentRouteName === errorRouteName) {
            return;
        }
        // 网络错误
        if (typeof error.result !== 'number') {
            errMsg = '网络似乎断开了，请再试一试';
            errCode = `${ErrorCode.NetworkError}`;
        }
        router
            .replace({
                name: errorRouteName,
                query: {
                    msg: errMsg,
                    code: errCode,
                    backUrl,
                    ...themesInfo,
                },
            })
            .then(() => {
                hasEffect = false;
            })
            .finally(() => {
                hasEffect = false;
            });
    }
}
