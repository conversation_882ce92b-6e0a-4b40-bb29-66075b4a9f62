<script setup lang="ts">
import Avatar from '@pet/adapt.avatar/index.vue';
import { debounce, sleep } from '@pet/yau.core';
import { ref, computed, onMounted, watch, shallowRef, markRaw } from 'vue';

import { useLogger } from '@/init/logger';
import type { FriendCardView } from '@/services/open-api-docs/home/<USER>/schemas';

const { sendShow, sendClick } = useLogger();
// 定义组件 Props
interface Props {
    friendCards?: FriendCardView[];
}

const props = withDefaults(defineProps<Props>(), {
    friendCards: () => [],
});

// 定义组件 Emits
const emits = defineEmits<{
    currentCardChange: [data: { currentCard: FriendCardView | null; allCardsInOrder: FriendCardView[] }];
}>();

// 扩展的卡片数据类型，添加索引和层级信息
interface ExtendedCardData extends FriendCardView {
    id: string;
    stackLevel?: number;
    index?: number;
    propertyTags?: Array<{
        text: string;
        hasGenderIcon: boolean;
        gender: string | null;
    }>;
    commonFriendsDesc?: string;
    shouldShowDefaultDesc?: boolean;
}

// 响应式数据
const currentIndex = ref(0);
const isDragging = ref(false);
const dragOffset = shallowRef({ x: 0, y: 0 });
const isAnimating = ref(false);
const cardContainer = ref<HTMLElement | null>(null);

// 格式化属性标签
const formatPropertyTags = (properties?: { [key: string]: string } | null) => {
    if (!properties) {
        return [];
    }

    const tags = [];
    // 性别值转换
    const genderMap: Record<string, string> = {
        M: '男',
        F: '女',
    };

    // 获取转换后的性别值
    const genderValue = properties.GENDER ? genderMap[properties.GENDER] : null;

    // 性别和星座组合（只有当性别为'男'或'女'时才显示性别）
    if (genderValue && properties.XZ) {
        tags.push({
            text: `${genderValue}·${properties.XZ}`,
            hasGenderIcon: true,
            gender: genderValue,
        });
    } else if (genderValue) {
        tags.push({
            text: genderValue,
            hasGenderIcon: true,
            gender: genderValue,
        });
    } else if (properties.XZ) {
        tags.push({
            text: properties.XZ,
            hasGenderIcon: false,
            gender: null,
        });
    }

    // IP地址
    if (properties.IP) {
        tags.push({
            text: `IP：${properties.IP}`,
            hasGenderIcon: false,
            gender: null,
        });
    }

    return tags;
};
// 格式化共同好友数量描述
const formatCommonFriendsDesc = (count?: number | null) => {
    if (!count || count <= 0) {
        return '';
    }
    return `你们共有${count}位共同好友`;
};
// 使用传入的好友卡片数据，预计算所有格式化数据
const cardData = computed<ExtendedCardData[]>(() => {
    if (Boolean(props.friendCards) && props.friendCards.length > 0) {
        return props.friendCards.map((card, index) => {
            const propertyTags = formatPropertyTags(card?.properties);
            return markRaw({
                id: `friend-${index}`,
                ...card,
                nickName: card?.nickName || '快手用户' + card?.userId,
                propertyTags,
                commonFriendsDesc: formatCommonFriendsDesc(card?.commonFriendCount),
                shouldShowDefaultDesc: !card?.commonFriendCount && (!card?.properties || propertyTags.length === 0),
            });
        });
    }
    return [];
});
// 获取总卡片数量
const totalCards = computed(() => cardData.value.length);

// 获取当前卡片信息
const currentCard = computed(() => {
    if (cardData.value.length > 0 && currentIndex.value >= 0 && currentIndex.value < cardData.value.length) {
        return cardData.value[currentIndex.value];
    }
    return null;
});

// 计算前一张、当前、下一张的索引（循环）
const getPrevIndex = (index: number) => (index - 1 + totalCards.value) % totalCards.value;
const getNextIndex = (index: number) => (index + 1) % totalCards.value;

// 计算可见的卡片（只显示当前和下一张卡片）
const visibleCards = computed(() => {
    const current = currentIndex.value;
    const cards = cardData.value;

    if (cards.length === 0) {
        return [];
    }

    // 如果只有一个卡片，只显示当前卡片
    if (cards.length === 1) {
        return [
            {
                ...cards[current],
                stackLevel: 0,
                index: current,
            },
        ];
    }

    // 多个卡片时的逻辑
    const next = getNextIndex(current);

    return [
        {
            ...cards[current],
            stackLevel: 0,
            index: current,
        },
        {
            ...cards[next],
            stackLevel: 1,
            index: next,
        },
    ];
});

// 滑动到上一张
const swipeToPrevious = async () => {
    if (isAnimating.value) {
        return;
    }

    isAnimating.value = true;

    try {
        // 使用requestAnimationFrame优化动画
        dragOffset.value = { x: 260, y: 0 };

        // 短暂延迟后更新索引
        await sleep(120);
        currentIndex.value = getNextIndex(currentIndex.value);
        dragOffset.value = { x: 0, y: 0 };

        // 等待整个动画完成
        await sleep(180);
    } finally {
        isAnimating.value = false;
    }
};

// 滑动到下一张
const swipeToNext = async () => {
    if (isAnimating.value) {
        return;
    }

    isAnimating.value = true;

    try {
        dragOffset.value = { x: -250, y: 0 };

        // 短暂延迟后更新索引，让滑出效果可见
        await sleep(120);
        currentIndex.value = getNextIndex(currentIndex.value);
        dragOffset.value = { x: 0, y: 0 };

        // 等待整个动画完成
        await sleep(180);
    } finally {
        isAnimating.value = false;
    }
};

// 动画到指定位置
const animateToPosition = (targetOffset: { x: number; y: number }): Promise<void> => {
    return new Promise((resolve) => {
        // 使用requestAnimationFrame优化动画
        requestAnimationFrame(() => {
            dragOffset.value = targetOffset;
        });

        // 进一步缩短动画时间，让左滑更快
        setTimeout(() => {
            requestAnimationFrame(() => {
                dragOffset.value = { x: 0, y: 0 };
            });
            resolve();
        }, 150);
    });
};

const checkSwipe = () => {
    const { x, y } = dragOffset.value;
    const horizontalThreshold = 80;

    return Math.abs(x) > Math.abs(y) && Math.abs(x) > horizontalThreshold;
};

// 触摸/鼠标事件处理
// eslint-disable-next-line sonarjs/cognitive-complexity
const startDrag = (event: MouseEvent | TouchEvent) => {
    if (isAnimating.value) {
        return;
    }
    event.preventDefault();
    isDragging.value = true;

    // 重置 dragOffset，防止上次动画的残留值影响拖拽
    dragOffset.value = { x: 0, y: 0 };

    const clientX = event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
    const clientY = event instanceof MouseEvent ? event.clientY : event.touches[0].clientY;

    const startX = clientX;
    const startY = clientY;

    const onMove = (moveEvent: MouseEvent | TouchEvent) => {
        if (!isDragging.value) {
            return;
        }

        const currentX = moveEvent instanceof MouseEvent ? moveEvent.clientX : moveEvent.touches[0].clientX;
        const currentY = moveEvent instanceof MouseEvent ? moveEvent.clientY : moveEvent.touches[0].clientY;

        const deltaX = currentX - startX;
        const deltaY = currentY - startY;

        // 直接更新 dragOffset，不使用节流
        dragOffset.value = { x: deltaX, y: deltaY };
    };

    const onEnd = () => {
        if (!isDragging.value) {
            return;
        }

        isDragging.value = false;

        const { x } = dragOffset.value;

        // 只处理水平滑动（水平位移大于垂直位移）
        const isHorizontalSwipe = checkSwipe();

        if (isHorizontalSwipe) {
            if (x > 0) {
                // 右滑 - 切换到上一张
                swipeToPrevious();
            } else {
                // 左滑 - 切换到下一张
                swipeToNext();
            }
        } else {
            // 回弹到原位
            animateToPosition({ x: 0, y: 0 });
        }

        // 清理事件监听器
        document.removeEventListener('mousemove', onMove);
        document.removeEventListener('mouseup', onEnd);
        document.removeEventListener('touchmove', onMove);
        document.removeEventListener('touchend', onEnd);
    };

    // 添加事件监听器
    document.addEventListener('mousemove', onMove);
    document.addEventListener('mouseup', onEnd);
    document.addEventListener('touchmove', onMove);
    document.addEventListener('touchend', onEnd);
};

// 直接跳转到指定卡片
const goToCard = (targetIndex: number) => {
    if (isAnimating.value || targetIndex === currentIndex.value) {
        return;
    }

    currentIndex.value = targetIndex;
};

// 计算卡片样式，减少模板中的复杂计算
const cardStyles = computed(() => {
    return (card: any) => {
        const { stackLevel } = card;
        const { x, y } = dragOffset.value;

        // 计算transform
        let transform = '';
        if (stackLevel === 0) {
            // 当前卡片：响应拖拽
            transform = `translate(${x}px, ${y}px) rotate(${x * 0.1}deg)`;
        } else {
            // 下一张卡片：在拖拽时保持静止，只有基础旋转
            const rotation = stackLevel * 3;
            const offsetX = 0; // 固定偏移为0，避免受拖拽影响
            transform = `rotateZ(${rotation}deg) translateX(${offsetX}px)`;
        }

        // 计算opacity
        let opacity = 1;
        if (stackLevel === 1) {
            // 下一张卡片的透明度处理
            if (Math.abs(x) > 100) {
                // 只在实际拖拽且达到阈值时才改变透明度
                opacity = Math.min(1, 0.56 + (Math.abs(x) - 100) / 150);
            } else {
                // 其他情况保持固定透明度，避免系统差异导致的闪烁
                opacity = 0.56;
            }
        } else if (stackLevel !== 0) {
            opacity = 0.56;
        }

        return {
            transform,
            zIndex: 10 - stackLevel,
            pointerEvents: 'auto' as const,
            opacity,
        };
    };
});

// 获取按当前显示顺序排列的所有卡片
const getAllCardsInOrder = () => {
    const cards = cardData.value;
    if (cards.length === 0) {
        return [];
    }

    const current = currentIndex.value;
    const orderedCards = [];

    // 从当前卡片开始，按顺序排列所有卡片
    for (let i = 0; i < cards.length; i++) {
        const index = (current + i) % cards.length;
        orderedCards.push(cards[index]);
    }

    return orderedCards;
};
const handleNextCardBtnClick = debounce(() => {
    // 防止拖拽交互和点击交互冲突
    if (isDragging.value && checkSwipe()) {
        return;
    }

    // 埋点上报：换一个按钮点击
    const currentCardData = currentCard.value;
    sendClick('OP_ACTIVITY_MAKE_TEAM_CARD', {
        friend_id: currentCardData?.userId || '',
        click_area: 'change',
    });
    swipeToNext();
}, 200);

// 监听当前卡片变化并发送事件
watch(
    [currentCard, currentIndex],
    ([newCard, newIndex]) => {
        const allCardsInOrder = getAllCardsInOrder();
        emits('currentCardChange', {
            currentCard: newCard,
            allCardsInOrder,
        });

        // 埋点上报：卡片展示
        if (newCard) {
            sendShow('OP_ACTIVITY_MAKE_TEAM_CARD', {
                friend_id: newCard?.userId || '',
            });
        }
    },
    { immediate: true },
);

// 组件挂载时初始化
onMounted(() => {
    console.log('卡片滑动组件初始化完成，总卡片数:', totalCards.value);

    // 预加载所有图片以防止白闪
    cardData.value.forEach((card) => {
        if (card.avatar) {
            const img = new Image();
            img.src = card.avatar;
            img.onload = () => {
                console.log(`图片预加载完成: ${card?.nickName ?? ''}`);
            };
        }
    });
});
</script>

<template>
    <div class="circular-swiper">
        <div ref="cardContainer" class="card-container">
            <!-- 卡片堆叠 -->
            <div
                v-for="card in visibleCards"
                :key="card.id"
                class="card"
                :class="{
                    'card--dragging': isDragging && card.stackLevel === 0,
                }"
                :style="cardStyles(card)"
                @mousedown="card.stackLevel === 0 && totalCards > 1 ? startDrag($event) : null"
                @touchstart="card.stackLevel === 0 && totalCards > 1 ? startDrag($event) : null"
            >
                <div class="card-content">
                    <div v-if="card?.title" class="card-title">{{ card.title }}</div>
                    <div v-if="card?.desc" class="card-sub-title">{{ card.desc }}</div>
                    <div class="card-avatar">
                        <Avatar :src="card.avatar || ''" :width="84" default-type="light" />
                    </div>
                    <div class="card-name">{{ card.nickName }}</div>
                    <div v-if="card.propertyTags && card.propertyTags.length > 0" class="card-tag">
                        <span v-for="tag in card.propertyTags" :key="tag.text" class="tag-item">
                            <!-- 性别图标 -->
                            <span
                                v-if="tag.hasGenderIcon"
                                class="gender-icon"
                                :class="{
                                    'icon-male': tag.gender === '男',
                                    'icon-female': tag.gender === '女',
                                }"
                            ></span>
                            {{ tag.text }}
                        </span>
                    </div>
                    <div v-if="card.commonFriendsDesc" class="card-friends-desc">
                        {{ card.commonFriendsDesc }}
                    </div>
                    <div v-if="card.shouldShowDefaultDesc" class="card-friends-desc">这位朋友什么也没留下～</div>

                    <!-- 换一个按钮 -->
                    <!-- click被上层preventDefault ban了 -->
                    <!-- touchend.stop会导致注册在document上的事件无法正常销毁 -->
                    <div
                        v-if="card.stackLevel === 0 && totalCards > 1"
                        class="card-change-btn"
                        @touchend="handleNextCardBtnClick"
                    >
                        <span class="card-change-icon"></span>
                        <span class="card-change-text">换一个</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <!-- <div class="controls">
            <button class="control-btn control-btn--prev" :disabled="isAnimating" @click="swipeToPrevious">
                ← 上一张
            </button>
            <button class="control-btn control-btn--next" :disabled="isAnimating" @click="swipeToNext">下一张 →</button>
        </div> -->
    </div>
</template>

<style lang="scss" scoped>
.circular-swiper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.card-container {
    position: relative;
    width: 290px;
    height: 357px;
}

.card {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 32px;
    overflow: hidden;
    background: #fff; /* 改为灰色背景，减少白闪 */
    cursor: grab;
    transition:
        transform 0.28s cubic-bezier(0.25, 0.8, 0.25, 1),
        opacity 0.28s ease;
    user-select: none;
    transform-style: preserve-3d;
    transform-origin: right bottom; /* 设置旋转原点为右下角 */
    backface-visibility: hidden;
}

.card:active {
    cursor: grabbing;
}

.card--dragging {
    transition:
        transform 0.12s ease-out,
        opacity 0.12s ease-out !important;
    cursor: grabbing;
}

.card--current {
    z-index: 10;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: transform, opacity;
}

.card--next {
    z-index: 9;
}

.card--next-next {
    z-index: 8;
}

.card--hidden {
    opacity: 0 !important;
    pointer-events: none !important;
    z-index: -1;
}

/* 渐显动画效果 */
.card--fade-in {
    animation: fadeInFromBottom 0.8s ease-out forwards;
}

@keyframes fadeInFromBottom {
    0% {
        opacity: 0;
        transform: translateY(20px) rotateZ(8deg) translateX(10px) scale(0.95);
    }
    50% {
        opacity: 0.5;
        transform: translateY(10px) rotateZ(6deg) translateX(8px) scale(0.97);
    }
    100% {
        opacity: 1;
        transform: rotateZ(8deg) translateX(10px);
    }
}

.card-content {
    position: relative;
    width: 100%;
    height: 326px;
    padding-top: 31px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: url('./assets/card-bg.png') no-repeat top center;
}

.card-title {
    font-family: KuaiYuanHuiTi;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px /* 116.667% */;
    background: linear-gradient(271deg, #000 0.92%, #4e010a 76.82%, #ab2132 100.26%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    max-width: 260px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-sub-title {
    margin-top: 6px;
    color: #c98888;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px /* 138.462% */;
    max-width: 260px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-avatar {
    margin-top: 26px;
}

.card-name {
    margin-top: 12px;
    height: 22px;
    color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    max-width: 208px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-tag {
    height: 19px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    .tag-item {
        display: inline-flex;
        align-items: center;
        margin: 0 4px;
        padding: 0 6px;
        border-radius: 7px;
        background-color: #f6f6f6;
        color: #222;
        text-align: center;
        font-size: 11px;
        font-style: normal;
        font-weight: 400;
        line-height: 19px;

        .gender-icon {
            display: inline-block;
            margin-right: 2px;
            font-size: 10px;
            width: 12px;
            height: 12px;

            &.icon-male {
                background: url('./assets/icon-man.png') no-repeat center;
                background-size: 12px 12px;
            }

            &.icon-female {
                background: url('./assets/icon-female.png') no-repeat center;
                background-size: 12px 12px;
            }
        }
    }
}
.card-friends-desc {
    margin-top: 8px;
    color: #9c9c9c;
    font-weight: 400;
    font-size: 12px;
    max-width: 260px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-change-btn {
    position: absolute;
    bottom: 26px;
    left: 50%;
    transform: translateX(-50%);
    height: 25px;
    padding: 0 8px; /* 增加内边距提高点击区域 */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 999; /* 提高层级确保在最上层 */
    pointer-events: auto;
    user-select: none; /* 防止文本选择 */
    border-radius: 200px;
    background: rgba(255, 230, 234, 0.6);
    .card-change-icon {
        width: 14px;
        height: 14px;
        margin-right: 2px;
        background: url('./assets/icon-change.png') no-repeat center;
        background-size: 100% 100%;
        font-size: 10px;
        line-height: 1;
    }

    .card-change-text {
        font-size: 11px;
        color: #f95a7f;
        font-weight: 500;
        line-height: 1;
    }
}

.swipe-hint--visible {
    opacity: 1;
}

.controls {
    display: flex;
    margin: 20px;
}

.control-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    margin: 0 10px;
    cursor: pointer;
}

.control-btn--prev {
    background: #28a745;
    color: white;
}

.control-btn--next {
    background: #007bff;
    color: white;
}
</style>
