<script setup lang="ts">
import type { RewardDoubledDetailView } from '@/services/open-api-docs/home/<USER>/schemas';
import { computedBeishu } from '@/utils/popupTransform/util';

const { data } = defineProps<{
    data: RewardDoubledDetailView;
    isLoin: boolean;
}>();

const beishu = computed(() => computedBeishu(data));
</script>
<template>
    <div :class="['double-llwd', isLoin ? 'double-llwd-loin' : 'double-llwd-packet']">
        <div class="llwd llwd-left">
            <div class="img"></div>
            <div class="amount">
                <span class="num">{{ data.displayOriginalLlrewdAmount ?? '' }}</span>
                <span v-if="data.displayOriginalLlrewdUnit && !isLoin" class="unit">{{
                    data.displayOriginalLlrewdUnit
                }}</span>
            </div>
            <span class="desc">{{ data.originalLlrewdDesc ?? '' }}</span>
        </div>
        <div class="middle"></div>
        <div class="llwd llwd-right">
            <div class="img">
                <div v-if="beishu" class="bubble-tag">
                    <div class="bubble-tag-txt">
                        <div class="bubble-tag-txt-1">x</div>
                        <div class="bubble-tag-txt-2">{{ beishu }}倍</div>
                    </div>
                </div>
            </div>
            <div class="amount">
                <span class="num">{{ data.displayDoubleLlrewdAmount ?? '' }}</span>
                <span v-if="data.displayDoubleLlrewdUnit && !isLoin" class="unit">{{
                    data.displayDoubleLlrewdUnit
                }}</span>
            </div>
            <span class="desc">{{ data.doubleLlrewdDesc ?? '' }}</span>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.double-llwd {
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    span {
        display: inline-block;
    }
    .llwd {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
    }
}

.middle {
    width: 36px;
    height: 32px;
    @include bg('@/assets/first-day-0714-exp/coin-arrow.png');
    background-size: 100% 100%;
}

.amount {
    display: flex;
    flex-direction: row;
    justify-content: center;
    .num {
        font-family: MiSans-Heavy;
        font-weight: 900;
        letter-spacing: 0px;
        color: #fe3666;
    }
}

.desc {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 10px;
    line-height: 14px;
    letter-spacing: 0px;
    color: #8a8a8a;
    white-space: nowrap;
}

.double-llwd-loin {
    height: 89px;
    padding-left: 1px;
    margin-top: 6px;
    .desc {
        margin-top: -1px;
    }
    .llwd-left {
        width: 52px;
        .img {
            width: 52px;
            height: 46px;
            @include bg('@/assets/first-day-0714-exp/bubble-left-coin.png');
            background-size: 100% 100%;
        }
    }
    .middle {
        margin: 11px 8px 0;
    }
    .llwd-right {
        width: 78px;
        .img {
            width: 78px;
            height: 46px;
            @include bg('@/assets/first-day-0714-exp/bubble-right-coin.png');
            background-size: 100% 100%;
            position: relative;
            .bubble-tag {
                position: absolute;
                width: 59px;
                height: 33px;
                @include bg('@/assets/first-day-0714-exp/bubble-tag.png');
                background-size: 100% 100%;
                top: -11.7px;
                right: -22.5px;
                transform: rotate(-1.89deg);
                .bubble-tag-txt {
                    width: 100%;
                    height: 100%;
                    box-sizing: border-box;
                    padding-bottom: 4px;
                    color: #210808;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    transform: rotate(-1.12deg);
                    .bubble-tag-txt-1 {
                        margin-top: 2px;
                        font-family: KuaiYuanHuiTi;
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 17px;
                        letter-spacing: -1px;
                    }
                    .bubble-tag-txt-2 {
                        font-family: KuaiYuanHuiTi;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 17px;
                        letter-spacing: -1px;
                    }
                }
            }
        }
    }
    .amount {
        margin-top: -1px;
        height: 27px;
        .num {
            font-size: 20px;
            font-family: 'MiSans';
            line-height: 27px;
        }
    }
}

.double-llwd-packet {
    height: 102px;
    padding-left: 0.5px;
    margin-top: 7px;
    .llwd-left {
        width: 40px;
        .img {
            width: 40px;
            height: 40px;
            @include bg('@/assets/first-day-0714-exp/packet.png');
            background-size: 100% 100%;
        }
    }
    .middle {
        margin: 24px 17px 0 16px;
    }
    .llwd-right {
        width: 60px;
        .img {
            width: 60px;
            height: 60px;
            @include bg('@/assets/first-day-0714-exp/packet.png');
            background-size: 100% 100%;
            position: relative;
            .bubble-tag {
                position: absolute;
                width: 59px;
                height: 33px;
                @include bg('@/assets/first-day-0714-exp/bubble-tag.png');
                background-size: 100% 100%;
                top: -13.7px;
                right: -34.5px;
                transform: rotate(-1.89deg);
                .bubble-tag-txt {
                    width: 100%;
                    height: 100%;
                    box-sizing: border-box;
                    padding-bottom: 4px;
                    color: #210808;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    transform: rotate(1.12deg);
                    .bubble-tag-txt-1 {
                        margin-top: 3px;
                        font-family: KuaiYuanHuiTi;
                        font-weight: 400;
                        font-size: 9px;
                        line-height: 100%;
                        letter-spacing: -1px;
                    }
                    .bubble-tag-txt-2 {
                        font-family: KuaiYuanHuiTi;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 100%;
                        letter-spacing: -1px;
                    }
                }
            }
        }
    }
    .amount {
        margin-top: 2px;
        height: 25px;
        .num {
            font-size: 20px;
            font-family: AlibabaPuHuiTi-2-105-Heavy;
            line-height: 25px;
        }
        .unit {
            margin-top: 2px;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 15px;
            line-height: 23px;
            letter-spacing: 0px;
            color: #fe3666;
            white-space: nowrap;
        }
    }
    .desc {
        margin-top: 1px;
    }
}
</style>
