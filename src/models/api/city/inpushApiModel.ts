import { createModel, useModel } from '@gundam/model';
import { useRestQueryWithType } from '@gundam/model/utils/apiConfig';
import { injectVisibility, useDynamicInterval } from '@pet/yau.yoda';
import { until, whenever } from '@vueuse/core';

import { useCityHomeApiModel } from '@/models/api/city/homeApiModel';
import { useConfigModel } from '@/models/city/configModel';
import { secondaryPageControllerInpush } from '@/services/open-api-docs/home/<USER>/vue-apollo-model-client';
import { InpushType, PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

// import { TaskType, usePopupModel } from '../popup.model';

// 不需要展示 inpush 的类型
// const excludeInpushTypes = [InpushType.GRID_TASK_UPDATE];

/**
 * cityInpushApiModel 轮询
 * 除夕方案：在 refreshPush 中加判断，轮询不会暂停，但是条件不足会不发请求，业务不用管
 * 预热方案：导出暂停方法，业务来控制轮询（暂定看看需不需要）
 */
export const cityInpushApiModel = createModel(({ getModelInstance }) => {
    const pageVisible = injectVisibility()?.visible ?? ref(true);
    // const { openInpush, currentTasks } = usePopupModel();
    const { data: homeData, refreshHome, forceRefreshHome } = useCityHomeApiModel(); // inpushConfig
    const { inpushConfig } = useConfigModel();

    const { refetch, info } = useRestQueryWithType(secondaryPageControllerInpush)({ skip: () => !homeData.value });

    const nextTimeMills = computed(() => info.data?.nextTimeMills ?? inpushConfig.value?.interval ?? 60 * 1000);

    const {
        pause: rawPause,
        resume: rawResume,
        trigger: triggerInpush,
    } = useDynamicInterval(
        async () => {
            if (!pageVisible.value || info.loading || !homeData.value) {
                return;
            }
            await refetch();
        },
        {
            interval: nextTimeMills,
            // 切前台重新请求， 后台状态不会进行轮询
            refreshAfterVisible: false,
        },
    );

    let pauseStack = 0;
    const pausePolling = () => {
        if (pauseStack === 0) {
            rawPause();
        }
        pauseStack++;
    };
    const resumePolling = () => {
        pauseStack--;
        if (pauseStack === 0) {
            rawResume();
        }
    };

    const refresh = async () => {
        await until(() => info.loading).toBe(false);
        pausePolling();
        await refetch();
        resumePolling();
    };

    // 轮询数据变化
    watch(
        () => info.data,
        (data) => {
            // 刷新主接口
            data?.needRefreshPopup && refreshHome();
            // 弹inpush
            data?.inpushList.forEach((inpush) => {
                // if (!excludeInpushTypes.includes(inpush.inpushType)) {
                //     openInpush(inpush);
                // }

                if (inpush.inpushType === InpushType.GRID_TASK_UPDATE) {
                    console.log('[GridTask] inpush refreshHome');
                    // 格子任务更新（看广告、双端互拉），刷新主接口（更新格子任务进度，拉 popList）
                    refreshHome().then((data) => {
                        console.log('[GridTask] inpush refreshHome then');
                        const hasGridTaskPopup = data.value?.popList?.some(
                            (popupData) => popupData.popupType === PopupType.CITY_LLCH_LLAWD_GRID_TASK,
                        );
                        // 如果本次请求主接口 由于任务状态更新延迟
                        if (!hasGridTaskPopup) {
                            console.log('[GridTask] inpush refreshHome again');
                            refreshHome();
                        }
                    });
                }
            });
        },
    );

    // 页面可见时，立即触发一次轮询
    whenever(pageVisible, () => {
        triggerInpush();
    });

    return {
        data: info.data,
        refreshInpush: refresh,
        pausePolling,
        resumePolling,
    };
});

export const useCityInpushApiModel = () => useModel(cityInpushApiModel);
