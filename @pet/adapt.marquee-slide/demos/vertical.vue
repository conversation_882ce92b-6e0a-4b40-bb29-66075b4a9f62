<pet-info lang="json">
{
    "title": "纵向滚动",
    "description": "纵向滚动跑马灯",
    "priority": 100
}
</pet-info>
<script setup lang="ts">
import { MarqueeSlide } from '../index';

const messageList = [
    {
        avatar: 'https://p2-pro.a.yximgs.com/uhead/AB/2023/04/28/10/BMjAyMzA0MjgxMDI2MTRfMTM0NzQzNjY4M18yX2hkNzgyXzg5Ng==_s.jpg',
        text: '获得1枚金币',
    },
    {
        avatar: 'https://p2-pro.a.yximgs.com/uhead/AB/2024/09/08/11/BMjAyNDA5MDgxMTUxMjlfNDM2MDEzMTIwOV8yX2hkMTQwXzI4MQ==_s.jpg',
        text: '获得2枚金币',
    },
    {
        avatar: 'https://p2-pro.a.yximgs.com/uhead/AB/2024/09/04/10/BMjAyNDA5MDQxMDA3NDVfMTQwODA0MjQ5MV8yX2hkMzIxXzc2Ng==_s.jpg',
        text: '获得3枚金币',
    },
];
</script>

<template>
    <div class="container">
        <MarqueeSlide :list="messageList" class="marquee-box">
            <template #item="{ data, index }">
                <div class="row">
                    <div v-if="data" class="line">
                        <img class="avatar" :src="data.avatar" :alt="data.text" /><b>index: {{ index }}</b> data:
                        {{ data.text }}
                    </div>
                </div>
            </template>
        </MarqueeSlide>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.marquee-box {
    font-size: 14px;
    width: 300px;
    height: 20px;
    margin-bottom: 10px;
    .line {
        padding: 10px 0;
        display: flex;
        align-items: center;
    }
    .avatar {
        width: 24px;
        height: 24px;
        margin-right: 6px;
    }
}
</style>
