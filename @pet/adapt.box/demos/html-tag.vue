<pet-info lang="json">
{ "title": "HTML标签", "description": "" }
</pet-info>
<script setup lang="ts">
import Box from '../box.vue';
import BoxItem from '../box-item.vue';
</script>

<template>
    <div class="wrapper">
        <Box :items="3" tag="ul">
            <BoxItem v-for="n in 3" :key="n" tag="li"> {{ n }} li </BoxItem>
        </Box>
    </div>
</template>

<style lang="scss" scoped>
.wrapper {
    width: 360px;
    background: #f5f5f5;
    position: relative;
}

li {
    font-size: 16px;
}
</style>
