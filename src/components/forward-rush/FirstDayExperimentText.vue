<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        mainText?: string;
        subText?: string;
    }>(),
    {
        mainText: '',
        subText: '',
    },
);

const splitMainText = computed(() => {
    if (!props.subText && props.mainText.includes('金币')) {
        const splitted = props.mainText.split('金币');
        return [splitted[0], '金币'];
    }
    return [props.mainText];
});
</script>
<template>
    <div class="experiment">
        <div class="main-text">
            <span class="main-text-1">{{ splitMainText[0] }}</span>
            <span class="main-text-2">{{ splitMainText[1] }}</span>
        </div>
        <span v-if="subText" class="sub-text">{{ subText }}</span>
    </div>
</template>
<style lang="scss" scoped>
.experiment {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .main-text {
        display: flex;
        flex-direction: row;
        height: 33px;
        line-height: 33px;
        transform: skew(-5deg);
        span {
            display: inline-block;
            font-family: KuaiYuanHuiTi;
            font-weight: 400;
            letter-spacing: 0px;
            background: linear-gradient(346deg, #fff2c6 6.59%, #fff 67.44%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .main-text-1 {
            font-size: 30px;
            line-height: 33px;
        }
        .main-text-2 {
            margin-top: 11px;
            font-size: 20px;
            line-height: 22px;
        }
    }
    .sub-text {
        margin-top: 2px;
        display: inline-block;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0px;
        color: #fff;
    }
}
</style>
