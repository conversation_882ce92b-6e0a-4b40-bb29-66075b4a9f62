<pet-info lang="json">
{ "title": "动效按钮", "description": "支持 sharp breath 类型", "priority": 2 }
</pet-info>
<script lang="ts" setup>
import Button from '../index.vue';
</script>

<template>
    <div>
        <div class="title">扫光效果</div>
        <Button class="item-btn" :type="'primary'" :ani-type="'shine'" :height="72">立即使用</Button>
        <div class="title">呼吸效果</div>
        <Button class="item-btn" :height="72" :ani-type="'breath'">立即使用</Button>
        <div class="title">扫光效果 + 呼吸效果</div>
        <Button class="item-btn" :height="72" :ani-type="['shine', 'breath']">立即使用</Button>
        <div class="title">禁用效果</div>
        <Button class="item-btn" :height="72" :ani-type="'breath'" disabled>立即使用</Button>
    </div>
</template>

<style lang="scss" scoped>
div {
    font-size: 14px;
}
.title {
    margin: 16px 0;
}
.item-btn {
    margin: 0 4px;
}
</style>
