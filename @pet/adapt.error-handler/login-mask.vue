<script setup lang="ts">
import Back from '@pet/adapt.back/index.vue';
import { useBarInfo, isAtFourTab } from '@pet/yau.core/device';
import { invoke } from '@yoda/bridge';
import { onBeforeMount, onBeforeUnmount, computed } from 'vue-demi';
import { useHomeModel } from '@/models/homeModel';

const props = defineProps<{
    autoLogin?: boolean;
}>();

const { statusBarHeight } = useBarInfo();
const { isLogin } = useHomeModel(); // 主接口

const topGap = computed(() => {
    return {
        marginTop: `${statusBarHeight}px`,
    };
});

// 如下不需要返回按钮遮罩的场景
const searchParams = new URLSearchParams(window.location.search);
const atHalfWebView = searchParams.get('layoutType') === '3';
const isNeedBack = computed(() => !(isAtFourTab() || atHalfWebView || !isLogin.value));

let logging = false;
async function doLogin() {
    if (logging) {
        return;
    }
    logging = true;

    try {
        await invoke('social.login', {
            checkFromServer: true,
        });
        window.location.reload();
    } catch (e) {
        await invoke('ui.showToast', {
            type: 'normal',
            text: '登录失败',
        });
    } finally {
        logging = false;
    }
}

const NO_LOGIN_ClASS = 'g-no-login';

function addRootClass() {
    document.documentElement.classList.add(NO_LOGIN_ClASS);
}

function removeRootClass() {
    document.documentElement.classList.remove(NO_LOGIN_ClASS);
}

addRootClass();

onBeforeMount(() => {
    if (props.autoLogin) {
        doLogin();
    }
});

onBeforeUnmount(() => {
    removeRootClass();
});
</script>

<template>
    <div class="login-mask" @click="doLogin">
        <Back v-if="isNeedBack" class="login-back-btn" :style="topGap" @click.stop />
    </div>
</template>
<style>
:root {
    --adapt-error-handler-login-mask-z-index: 9999;
    --adapt-error-handler-login-mask-back-btn-size: 40px;
    --adapt-error-handler-login-mask-back-btn-x-gap: 12px;
}
</style>
<style lang="scss" scoped>
.login-mask {
    position: fixed;
    z-index: var(--adapt-error-handler-login-mask-z-index);
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}
.login-back-btn {
    width: var(--adapt-error-handler-login-mask-back-btn-size);
    height: var(--adapt-error-handler-login-mask-back-btn-size);
    margin-left: var(--adapt-error-handler-login-mask-back-btn-x-gap);
}
</style>
