<pet-info lang="json">
{ "title": "自定义", "description": "" }
</pet-info>
<script setup lang="ts">
import IconWidget from '../index.vue';

function handleClick() {
    console.log('click');
}
</script>

<template>
    <div>
        <h4>自定义</h4>
        <IconWidget type="text" text="攻略" @click="handleClick" />
    </div>
</template>
<style lang="scss" scoped>
h4 {
    font-size: 18px;
    line-height: 28px;
    margin: 16px 0;
}
</style>
