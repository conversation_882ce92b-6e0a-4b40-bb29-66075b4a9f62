<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Picture from "@pet/quantum.PictureImg";
import TVPlayer from '@pet/summer.tvplayer/index.vue';

import { useAnimationPause } from '@/models/animationVisibility';

import boatMov from './assets/boat.mov?url';
import boatMp4 from './assets/boat.mp4?url';
import boatWebm from './assets/boat.webm?url';
import boatPng from './assets/fallback.png';
import boatDown from './assets/fallback.png?preset=modern';
const { effectShowStatus } = useDowngradeLevel();
const { isAnimationPause } = useAnimationPause()
const emit = defineEmits<{
    (e: 'ended'): void;
}>();

const urls = {
    mp4: boatMp4,
    mov: boatMov,
    webm: boatWebm,
}

</script>
<template>
    <TVPlayer v-if="effectShowStatus.L1" id="9477" class="tv-wrapper" :urls="urls" :autoplay="true" :loop="true" :placeholder="boatPng" :play="!isAnimationPause" @ended="emit('ended')"/>
    <Picture v-else class="tv-wrapper" :src="boatDown" />
</template>
<style lang="scss" scoped>
.tv-wrapper {
    position: absolute;
    width: 75px;
    height: 80px;
    overflow: hidden;
    left: -5px;
    top: 30px
}
</style>
