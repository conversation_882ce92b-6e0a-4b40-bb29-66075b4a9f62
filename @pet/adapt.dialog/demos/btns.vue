<pet-info lang="json">
{ "title": "按钮组", "description": "", "priority": 5 }
</pet-info>
<script setup lang="ts">
import { toast } from '@pet/adapt.toast';
import AdaptDialog from '../index.vue';

function handleConfirm() {
    toast('触发 confirm 事件');
}

function handleBottomClick() {
    toast('触发 bottom-text-btn-click 事件');
}
function handleCencel() {
    toast('触发 cancel 事件');
}
function handleClose() {
    toast('触发 close 事件');
}
</script>

<template>
    <div class="demo-wrapper">
        <AdaptDialog
            class="static dialog-all demo-dialog"
            show
            title="此处为主标题"
            sub-title="此处为副标题，可用于辅助信息"
            confirm-text="确认"
            cancel-text="再想想"
            bottom-message="此处为辅助信息或"
            bottom-btn-text="次级按钮"
            @confirm="handleConfirm"
            @cancel="handleCencel"
            @close="handleClose"
            @bottom-text-btn-click="handleBottomClick"
        >
            <template #icon><div class="demo-content"></div></template>
        </AdaptDialog>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
@import '@pet/adapt.fonts/style/kuaiyuanhui.css';
.dialog-all {
    --adapt-dialog-title-font-color: linear-gradient(273deg, #100110 17.93%, #760776 84.26%);
    --adapt-dialog-title-font-family: MFYuanHei;
    --adapt-button-font-weight: 600;
}
.demo-content {
    width: 240px;
    height: 130px;
    background: url('../assets/imgs/default-content.png') 100% 100% no-repeat;
    background-size: 100% 100%;
}
</style>
