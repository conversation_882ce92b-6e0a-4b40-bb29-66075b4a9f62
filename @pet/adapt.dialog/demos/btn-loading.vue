<pet-info lang="json">
{ "title": "按钮loading", "description": "主按钮不可点态，会出发click事件", "priority": 3 }
</pet-info>
<script setup lang="ts">
import { toast } from '@pet/adapt.toast';
import AdaptDialog from '../index.vue';
import Logo from '../assets/imgs/default-logo.png';

function handleConfirm() {
    toast('触发 confirm 事件');
}

function handleBottomClick() {
    toast('触发 bottom-text-btn-click 事件');
}
function handleClose() {
    toast('触发 close 事件');
}
</script>

<template>
    <div class="demo-wrapper">
        <AdaptDialog
            class="static dialog-all demo-dialog"
            show
            :custom-logo="Logo"
            title="此处为主标题"
            sub-title="此处为副标题，可用于辅助信息"
            confirm-text="按钮文案"
            btn-loading
            bottom-message="此处为辅助信息或"
            bottom-btn-text="次级按钮"
            cartoon
            @confirm="handleConfirm"
            @close="handleClose"
            @bottom-text-btn-click="handleBottomClick"
        >
            <div class="demo-content"></div>
        </AdaptDialog>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
@import '@pet/adapt.fonts/style/kuaiyuanhui.css';
.dialog-all {
    --adapt-lay-cartoon: url('../assets/imgs/default-cartoon.png');
    --adapt-lay-cartoon-margin-bottom: 54px;
    --adapt-dialog-title-font-color: linear-gradient(273deg, #100110 17.93%, #760776 84.26%);
    --adapt-dialog-title-font-family: MFYuanHei;
    --adapt-button-font-weight: 600;
}
.demo-content {
    width: 240px;
    height: 130px;
    background: url('../assets/imgs/default-content.png');
}
</style>
