# 特殊字体

设计提供的规范特殊字体全量字体文件示例。在项目使用中**按需剪裁**。

[设计字体规范文档](https://docs.corp.kuaishou.com/k/home/<USER>/fcABvhskVzn-tAKVfikSG6a3r)

字体剪裁请使用 [c.font-compress](https://pet-web.corp.kuaishou.com/gallery/detail/?name=@pet/c.font-compress)

## 使用方式

对应figma复制出来的code,对应字重就直接使用figma的font-weight值即可。

如：

```css
.custom-font {
    /* 阿里巴巴 */
    font-family: "Alibaba PuHuiTi 3.0";
    font-style: normal;
    font-weight: 1000;
    font-size: 12px;
    line-height: 17px;
    /* identical to box height */

    color: #000000;
}
```
