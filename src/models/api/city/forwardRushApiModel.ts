import { createModel } from '@gundam/model';
import { useRestMutationWithType } from '@gundam/model/utils/apiConfig';
import { toast } from '@pet/adapt.toast';
import { getErrorMessage } from '@pet/base.model-utils-helper';
import { isNotNil } from '@pet/yau.core';

import { secondaryPageControllerChessMove } from '@/services/open-api-docs/home/<USER>/vue-apollo-model-client';
import type {
    CityChessMoveResultView,
    ChessProgressView,
    HomeAccountModel,
} from '@/services/open-api-docs/home/<USER>/schemas';

const DEFAULT_ERROR_MESSAGE = '活动太火爆了，刷新一下试试';

export const forwardRushApiModel = createModel(() => {
    const forwardRush = useRestMutationWithType(secondaryPageControllerChessMove)();
    // 挑战格子任务详情
    const moveProgress = ref<ChessProgressView>();

    /**
     * 向前冲
     * @returns
     */
    async function goForward(): Promise<{ success: boolean; needRefresh: boolean; data?: CityChessMoveResultView }> {
        console.log('goForward');
        await forwardRush.mutate({});
        const error = forwardRush.info.error;
        if (error) {
            const message = getErrorMessage(error);
            toast(message ?? DEFAULT_ERROR_MESSAGE);
            return { success: false, needRefresh: false };
        }
        const data = forwardRush.info.data;
        if (!isNotNil(data?.progress)) {
            toast(DEFAULT_ERROR_MESSAGE);
            return { success: false, needRefresh: isNotNil(data) };
        }
        moveProgress.value = data?.progress;

        return { success: true, data, needRefresh: false };
    }

    return {
        goForward,
        moveProgress,
    };
});
