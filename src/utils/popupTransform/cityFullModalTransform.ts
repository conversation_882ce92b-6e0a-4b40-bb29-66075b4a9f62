import type { CreatePopupTaskType } from '@pet/adapt.task-queue/task-modules/popupTask';

// import CommonModal from '@/components/common-modals/CommonModal.vue';
import CityFullModal from '@/components/city/full-modal/CityFullModal.vue';
import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

import { type PopupBtnClick, type PopupDataType, type PopupTaskConfig } from './types';
import { filterNullValues } from '../common';

export const CityModalTypes = [
    PopupType.LS_ATTRACTION_TICKETS, // 景点门票
    // PopupType.LS_TRAVEL_QUALIFICATIONS, // 旅游资格
    PopupType.LS_CITY_PHYSICAL_LLREWD, // 旅游资格、卫衣和贴纸
    PopupType.LS_LOCAL_LIFE_COUPON, // 本地生活优惠券
    // PopupType.LS_CITY_PAGE_BLESS, // 祝福语
    PopupType.LS_CITY_PROFILE_PENDANT, // 头像挂件
] satisfies PopupType[];

const getTitle = (title: string | string[]) => {
    if (Array.isArray(title)) {
        return title;
    }
    if (title?.includes('\n')) {
        return title.split('\n');
    }
    return title;
};

export function cityModalTransform(
    popupData: PopupDataType<typeof CityModalTypes>,
    createPopupTask: CreatePopupTaskType,
    config?: PopupTaskConfig,
    onBtnClick?: PopupBtnClick,
) {
    const type: 'dialog' | 'layer' = 'layer';
    const data: any = filterNullValues({
        popupType: popupData?.popupType ?? '',
        type,
        sponsorLogo: popupData?.sponsorLogo,
        sponsorText: popupData?.sponsorText,
        title: getTitle(popupData?.title ?? ''),
        subTitle: popupData?.subTitle ?? '',
        desc: '',
        message: '',
        bottomDesc: (popupData as any).llpeDetail?.[0]?.bottomDesc || '',
        icon: popupData?.icon,
        mainButton: popupData?.mainButton?.linkText,
        btnClick: onBtnClick,
        // flyToTarget: 'wallet',
        cloneFlyToTarget: 'wallet',
        clonePilotProps: {
            duration: 667,
            bezier: [0, -147, -98.25, 4.365],
            timeBezier: [0.33, 0.0, 0.67, 1.0],
            style: {
                '--adapt-bezier-fly-fade-start': '1',
                '--adapt-bezier-fly-fade-end': '1',
                '--adapt-bezier-fly-scale-end': '0.1',
            },
        },
    });

    // 创建弹窗任务
    const task = createPopupTask({
        component: CityFullModal,
        data,
        options: config,
    });

    return task;
}
