<template>
    <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class="icon-close-small-simple"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M10.7748 4.92515C10.5055 4.65591 10.069 4.65591 9.79976 4.92515L7.84951 6.87539L5.89937 4.92524C5.63013 4.65601 5.1936 4.65601 4.92437 4.92524C4.65513 5.19448 4.65513 5.63101 4.92436 5.90024L6.87451 7.85039L4.92476 9.80015C4.65552 10.0694 4.65552 10.5059 4.92476 10.7751C5.19399 11.0444 5.63052 11.0444 5.89976 10.7751L7.84951 8.82539L9.79937 10.7752C10.0686 11.0445 10.5051 11.0445 10.7744 10.7752C11.0436 10.506 11.0436 10.0695 10.7744 9.80024L8.82451 7.85039L10.7748 5.90015C11.044 5.63091 11.044 5.19439 10.7748 4.92515Z"
            fill="currentColor"
        />
    </svg>
</template>

<style>
:root {
    /* 箭头默认颜色 */
    --adapt-icon-close-small-simple-color: #ff4185;
}
</style>

<style lang="scss" scoped>
.icon-close-small-simple {
    color: var(--adapt-icon-close-small-simple-color);
}
</style>
