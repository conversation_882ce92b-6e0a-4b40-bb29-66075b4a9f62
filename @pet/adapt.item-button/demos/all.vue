<pet-info lang="json">
{ "title": "所有图标", "description": "目前使用出现的图标按钮形式" }
</pet-info>
<script setup lang="ts">
import IconMusic from '@pet/adapt.icons/music-svg.vue';
import IconRecord from '@pet/adapt.icons/record-svg.vue';
import IconRule from '@pet/adapt.icons/guide-svg.vue';
import IconScan from '@pet/adapt.icons/scan-svg.vue';
import IconShare from '@pet/adapt.icons/share-svg.vue';
import IconTrends from '@pet/adapt.icons/trends-svg.vue';
import IconWallet from '@pet/adapt.icons/wallet-svg.vue';

import ItemButton from '../index.vue';
</script>

<template>
    <div class="demo">
        <ItemButton>
            <template #icon><IconRecord /></template>记录
        </ItemButton>
        <ItemButton>
            <template #icon><IconWallet /></template>钱包
        </ItemButton>
        <ItemButton>
            <template #icon><IconShare /></template>分享
        </ItemButton>
        <ItemButton>
            <template #icon><IconMusic /></template>音效设置
        </ItemButton>
        <ItemButton>
            <template #icon><IconRule /></template>攻略
        </ItemButton>
        <ItemButton>
            <template #icon><IconScan /></template>扫一扫
        </ItemButton>
        <ItemButton>
            <template #icon><IconTrends /></template>动态
        </ItemButton>
        <!-- <hr />
        <h4>自定义图标</h4>
        <ItemButton>
            <template #icon>
                <img
                    src="https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2022-03-18/1647596830941.e8bd99c546809e21.png"
                />
            </template>
            Likes
        </ItemButton>
        <hr />
        <h4>辅助操作区</h4>
        <ItemButton>
            <template #icon>
                <IconMusic />
            </template>
            音效设置
            <template #addon>
                <input type="checkbox" />
            </template>
        </ItemButton>
        <hr />
        <h4>不可用</h4>
        <ItemButton disabled>
            <template #icon>
                <IconScan />
            </template>
            扫一扫
        </ItemButton> -->
    </div>
</template>

<style lang="scss" scoped>
.demo {
    width: 100%;
}
</style>
