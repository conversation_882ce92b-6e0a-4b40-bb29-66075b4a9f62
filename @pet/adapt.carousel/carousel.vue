<script lang="ts">
export default {
    name: 'AdaptCarousel',
};
</script>

<script setup lang="ts" generic="T extends any">
import { px2rem } from '@pet/core.mobile';
import { useRafFn } from '@vueuse/core';
import { watch, ref, computed, type Ref, nextTick } from 'vue-demi';

interface CarouselItemWithUid {
    item: any;
    uid: number;
}

const defaultConfig: CarouselItemWithUid[] = [];

const props = withDefaults(
    defineProps<{
        /**
         * 每个item的高度，414下的px值
         */
        itemHeight?: number;
        /**
         * 元素高度运动时间，单位 s
         */
        duration?: number;
        /**
         * array 传入数据
         */
        list?: T[];
        /**
         * 展示的弹幕数量
         */
        showNum?: number;
        /**
         * 播放
         */
        play?: boolean;
        /**
         * fps 帧率主要为了动效降级
         */
        fps?: number;
    }>(),
    {
        itemHeight: 60,
        duration: 2,
        list: () => [],
        showNum: 2,
        play: true,
        fps: 60,
    },
);

const slots = defineSlots<{
    default(props: { item: T; index: number }): any;
}>();

let uid = 1;
const listWithUid: Ref<CarouselItemWithUid[]> = computed(() => {
    return props.list.map((item) => ({
        item,
        uid: uid++,
    }));
});

const showData = ref<CarouselItemWithUid[]>(defaultConfig);
const beginData = ref<boolean[]>([]);
const timerStatus = ref(true);
// 即将展示的下标，0-5
const nextShowIndex = ref<number>(0);
// 当前消费的下标
const feedIndex = ref<number>(0);
const showTimer = ref(false);

const init = (play: boolean) => {
    timerStatus.value = play;
    if (!play) {
        beginData.value = [];
        nextShowIndex.value = 0;
        feedIndex.value = feedIndex.value < 2 ? 0 : feedIndex.value - 2;
    }
    showTimer.value = play;
};

const nextRound = () => {
    if (listWithUid.value.length === 0) {
        return;
    }
    if (nextShowIndex.value >= listWithUid.value.length) {
        nextShowIndex.value = nextShowIndex.value === props.showNum - 1 ? 0 : nextShowIndex.value + 1;
        return;
    }
    beginData.value.splice(nextShowIndex.value, 1, false);
    nextTick(() => {
        showData.value.splice(nextShowIndex.value, 1, listWithUid.value[feedIndex.value]!);
        beginData.value.splice(nextShowIndex.value, 1, true);
        nextShowIndex.value = nextShowIndex.value === props.showNum - 1 ? 0 : nextShowIndex.value + 1;
        feedIndex.value = feedIndex.value === listWithUid.value.length - 1 ? 0 : feedIndex.value + 1;
    });
};

const timelineEnd = () => {
    if (!timerStatus.value) {
        return;
    }
    nextRound();
};

const itemStyle = computed(() => {
    return {
        height: px2rem(props.itemHeight),
        animationDuration: `${props.showNum * props.duration}s`,
        animationTimingFunction: `steps(${props.showNum * props.duration * props.fps})`,
        '--adapt-carousel-move-step-1': `${0 - props.showNum * 80}%`,
        '--adapt-carousel-move-step-2': `${0 - props.showNum * 100}%`,
    };
});

const carouselHeight = computed(() => {
    return {
        height: px2rem(props.itemHeight * props.showNum),
    };
});

let lastTime = -Infinity;
useRafFn(() => {
    if (!showTimer.value) {
        return;
    }

    const now = Date.now();
    if (now - lastTime > props.duration * 1000) {
        lastTime = now;
        timelineEnd();
    }
});

watch(
    () => props.list,
    () => {
        showTimer.value = true;
        feedIndex.value = 0;
    },
    {
        immediate: true,
    },
);

watch(
    () => props.play,
    (val) => {
        init(val);
    },
);
</script>
<template>
    <div class="carousel-box" :style="carouselHeight">
        <template v-for="(item, index) of showData">
            <div v-if="beginData[index]" :key="item.uid" class="carousel-child" :style="itemStyle">
                <slot :item="item.item" :index="index"></slot>
            </div>
        </template>
    </div>
</template>
<style lang="scss" scoped>
@keyframes move {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    80% {
        opacity: 1;
        transform: translateY(var(--adapt-carousel-move-step-1));
    }
    100% {
        opacity: 0;
        transform: translateY(var(--adapt-carousel-move-step-2));
    }
}

.carousel-box {
    /* 默认高度，实际高度动态计算行内样式 */
    height: 120px;
    position: relative;
    .carousel-child {
        /* 默认高度，实际高度动态计算行内样式 */
        height: 60px;
        position: absolute;
        bottom: 0;
        opacity: 0;
        animation: move linear;
        display: flex;
        align-items: flex-end;
    }
}
</style>
