<script setup lang="ts">
import type { Button, BottomDesc } from '../types';
import ArrRight from '../assets/arr-right.vue';

defineProps<{
    mainBtn: Button;
    subBtn?: Button;
    bottomDesc?: BottomDesc;
}>();

const emits = defineEmits<{
    (event: 'mainBtnClick'): void;
    (event: 'subBtnClick'): void;
    (event: 'btmDescClick'): void;
}>();
</script>

<template>
    <div class="button-wrapper">
        <Transition v-if="!subBtn" name="button" appear>
            <div class="big-btn" @click="emits('mainBtnClick')">
                <span class="text">{{ mainBtn.linkText }}</span>
            </div>
        </Transition>
        <div v-else class="btn-group">
            <div class="btn left" @click="emits('subBtnClick')">
                <span class="text">{{ subBtn.linkText }}</span>
            </div>
            <div class="btn right" @click="emits('mainBtnClick')">
                <span class="text">{{ mainBtn.linkText }}</span>
            </div>
        </div>
        <div v-if="bottomDesc" :class="['bottom-desc', 'small-top']">
            {{ bottomDesc.desc
            }}<span class="btn" @click="emits('btmDescClick')">{{ bottomDesc.linkText }}<ArrRight /></span>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@import '../common.scss';
.button-wrapper {
    @include flex-center;
    flex-direction: column;
    width: 100%;
    min-height: 94px;
    justify-content: center;

    .big-btn {
        @include flex-center;
        width: var(--adapt-packet-inner-btn-width);
        height: var(--adapt-packet-inner-btn-height);
        margin-bottom: 6px;
        font-size: var(--adapt-packet-inner-btn-font-size);
        line-height: 28px;
        color: var(--adapt-packet-inner-btn-font-color);
        background: no-repeat center/100%;
        background-image: var(--adapt-packet-inner-btn-bg-image);

        & .text {
            font-weight: bold;
            font-family: var(--adapt-packet-inner-btn-font-family);
            @include ellipse;
        }
    }

    .btn-group {
        @include flex-center;
        width: 100%;
        height: 74px;
        margin: 6px 0 12px;

        .btn {
            @include flex-center;
            height: var(--adapt-packet-inner-group-btn-height);
            width: var(--adapt-packet-inner-group-btn-width);

            & .text {
                font-size: var(--adapt-packet-inner-group-btn-font-size);
                font-family: var(--adapt-packet-inner-btn-font-family);
                font-weight: bold;
                @include ellipse;
            }

            &.left {
                margin-right: 12px;
                color: var(--adapt-packet-inner-left-btn-font-color);
                background: no-repeat center/100%;
                background-image: var(--adapt-packet-inner-left-btn-bg-image);
            }

            &.right {
                background: no-repeat center/100%;
                color: var(--adapt-packet-inner-right-btn-font-color);
                background-image: var(--adapt-packet-inner-right-btn-bg-image);
            }
        }
    }

    .bottom-desc {
        margin-top: var(--adapt-packet-inner-btm-desc-margin-top);
        color: var(--adapt-packet-inner-btm-desc-font-color);
        --adapt-packet-arr-right-color: var(--adapt-packet-inner-btm-desc-font-color);
        font-size: 12px;
        line-height: 18px;
        opacity: 0.8;

        .btn {
            font-weight: bold;
        }
    }

    .small-top {
        margin-top: 2px;
    }
}

// 主按钮动效
.button-enter-active {
    animation: btn-shake 0.7666666666666667s 1.3333333333333333s linear;
}
</style>
