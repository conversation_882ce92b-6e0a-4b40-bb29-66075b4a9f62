<script setup lang="ts">
import { ref, computed } from 'vue-demi';

import XImage from '../index.vue';

const imgSrc = ref(
    'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-07-21/1689937599115.58198604ad36d4a3.png',
);
</script>

<template>
    <div class="demo">
        <h5>:spinner-wait-time="100"</h5>
        <div class="container">
            <XImage :src="imgSrc" class="image-item" :spinner-wait-time="100" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
}
.image-item {
    width: 100px;
    height: 100px;
}
div {
    font-size: 16px;
}
</style>
