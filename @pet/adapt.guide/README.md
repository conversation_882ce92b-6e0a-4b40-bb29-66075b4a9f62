# @pet/adapt.guide

新手引导手势动效，提供了正向与反向，也可以定义手势动效展示的次数

## 属性

| 属性名     | 类型    | 默认值 | 可选值             | 说明                   |
| ---------- | ------- | ------ | ------------------ | ---------------------- |
| reverse    | boolean | false  | -                  | 手势方向               |
| repeat     | number  | -      | -                  | 重复次数，不传无限循环 |
| play       | boolean | true   | -                  | 播放动效               |
| alignPoint | union   | 'edge' | 'edge' \| 'center' | 对齐标准点             |
| src        | string  | Hand   | -                  | 手势图片               |

## 事件

| 事件名 | 载荷 | 说明       |
| ------ | ---- | ---------- |
| end    |      | 手动画结束 |
