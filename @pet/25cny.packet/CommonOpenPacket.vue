<!-- 开红包组件封装示例 -->
<script setup lang="ts">
import OpenPacket from './components/OpenPacket.vue';
import type { OpenPacketProps } from './components/OpenPacket.vue';
import { inject, onMounted, ref } from 'vue-demi';
import type { ClickPosition, PopupBtnClick } from './type';
import { isNotNil } from '@pet/yau.core';

const props = defineProps<
    OpenPacketProps & {
        btnClick?: PopupBtnClick;
        openType?: 'none' | 'click' | 'count';
        /** 开红包皮埋点 */
        onOpenPacket?: () => void;
    }
>();

const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'end', val: { event: string; data: OpenPacketProps }): void;
}>();

const position = ref<ClickPosition>('close');
const coverStatus = ref(false);
const showProgress = ref(false);

const showButtonGuide = ref(false);

const tryToShowPopupButtonGuide = inject<() => Promise<boolean>>('popupButtonGuide');

const triggerBtnClick = () => {
    if (props.btnClick) {
        props.btnClick({
            position: position.value,
            coverStatus: coverStatus.value,
            destroy: () => {
                emits('close');
            },
        });
    } else {
        emits('close');
    }
};

const onCloseClick = (isCover?: boolean) => {
    position.value = 'close';
    coverStatus.value = isCover ?? false;
    triggerBtnClick();
};

const onMainClick = () => {
    position.value = 'mainClick';
    triggerBtnClick();
};

const onSubClick = () => {
    position.value = 'subClick';
    triggerBtnClick();
};

const onBottomClick = () => {
    position.value = 'bottomClick';
    triggerBtnClick();
};

const onAfterLeave = () => {
    emits('end', {
        event: position.value,
        data: props,
    });
};

const onCouponClick = () => {
    position.value = 'couponClick';
    triggerBtnClick();
};

const handleOpenPacket = () => {
    showProgress.value = true;
    props.onOpenPacket?.();
};

const handleCoverClick = () => {
    position.value = 'coverClick';
    triggerBtnClick();
};

onMounted(async () => {
    const result = await tryToShowPopupButtonGuide?.();
    if (isNotNil(result)) {
        showButtonGuide.value = result;
    }
});
</script>

<template>
    <OpenPacket
        v-bind="props"
        :open-type="openType"
        :main-button="props.mainButton ? { ...props.mainButton, guide: showButtonGuide } : undefined"
        @after-leave="onAfterLeave"
        @close="onCloseClick"
        @main-click="onMainClick"
        @sub-click="onSubClick"
        @bottom-click="onBottomClick"
        @coupon-click="onCouponClick"
        @packet-open="handleOpenPacket"
        @open-click="handleCoverClick"
    >
    </OpenPacket>
</template>

<style lang="scss" scoped>
:deep .popup-footer {
    z-index: -1;
}
.city_ls_llch_llrewd_popup :deep .blessing_icon {
    width: 16px;
    height: 16px;
    --adapt-dialog-blessing-icon: url('./assets/dialog-blessing-icon-wechat.png');
}
</style>
