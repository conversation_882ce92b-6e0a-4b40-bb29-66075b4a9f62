<pet-info lang="json">
{ "title": "所有状态", "description": "缺省图标，所有对应关系" }
</pet-info>
<script lang="ts" setup>
import EmptyStatus from '../index.vue';

const iconsMap = [
    { network: '网络异常' },
    { protect: '青少年模式' },
    { anomaly: '账号存在风险' },
    { band: '活动未对设备开放' },
    { empty: '无相关内容' },
    { notStart: '活动暂未开始，敬请期待' },
    { end: '活动已下线' },
    { noGift: '无礼物' },
    { noMessage: '无会话' },
    { noBody: '无好友' },
    { user: '无账号' },
    { activityHot: '活动火爆，请稍后重试' },
    { download: '下载最新版本App' },
];
</script>

<template>
    <div>
        <div v-for="(type, index) in iconsMap" :key="index" class="container">
            <EmptyStatus v-for="(info, icon) in type" :key="icon" :icon="icon"> {{ icon }}: {{ info }} </EmptyStatus>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.container {
    margin: 10px;
}
</style>
