<!-- 页面底部组件，包含向前冲按钮-->
<script setup lang="ts">
import MainButtons from '@/components/city/forward-rush/ForwardRushButton.vue';
import FinalBubble from '@/components/city/progress-bubble/FinalBubble.vue';
import ProgressBubble from '@/components/city/progress-bubble/ProgressBubble.vue';
import { useBubbleModel } from '@/models/city/bubbleModel';
import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';
const { showBubbleType } = useBubbleModel();

const switchShowBubbleType = () => {
    showBubbleType.value = showBubbleType.value === 'progress' ? 'final' : 'progress';
};
const { autoScroll, isFourTabMode, isSearchTabMode } = useForwardRushGridModel();
</script>

<template>
    <div
        class="footer-view"
        :class="{ 'four-tab-mode': isFourTabMode, 'search-tab-mode': isSearchTabMode, scroll: autoScroll }"
    >
        <!-- debug -->
        <div
            :style="{
                margin: '5px',
                backgroundColor: 'white',
                cursor: 'pointer',
                position: 'absolute',
                left: 0,
                display: 'none',
            }"
        >
            <div :style="{ padding: '5px' }" @click="switchShowBubbleType">切换气泡: {{ showBubbleType }}</div>
        </div>
        <!-- 进度红包气泡 -->
        <ProgressBubble></ProgressBubble>
        <!-- 已完成气泡 -->
        <FinalBubble></FinalBubble>
        <!-- 底部按钮 -->
        <MainButtons class="main-button" />
    </div>
</template>

<style lang="scss" scoped>
.footer-view {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 261px;
    z-index: 100;
    &.scroll {
        height: 230px;
        :deep(.forward-btn-container) {
            margin-bottom: 0;
        }
    }
    &.four-tab-mode {
        height: 230px;
        :deep(.forward-btn-container) {
            margin-bottom: 8px;
        }
    }
    &.search-tab-mode {
        height: 245px;
        :deep(.forward-btn-container) {
            margin-bottom: 19px;
        }
    }

    .main-buttons {
        display: flex;
        flex-direction: row;
        justify-content: center;
        width: 100%;
    }
}
</style>
