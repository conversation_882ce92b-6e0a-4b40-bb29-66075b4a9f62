<script lang="ts" setup></script>
<template>
    <svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask
            id="mask0_2422_50258"
            style="mask-type: alpha"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="11"
            height="11"
        >
            <path d="M0.5 0.962891H10.5V10.9629H0.5V0.962891Z" fill="white" />
        </mask>
        <g mask="url(#mask0_2422_50258)">
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M4.20852 3.03761C4.44339 2.78466 4.83885 2.77002 5.09179 3.0049L8.00846 5.71329C8.13581 5.83155 8.20817 5.99749 8.20817 6.17128C8.20817 6.34507 8.13581 6.51102 8.00846 6.62927L5.09191 9.33755C4.83897 9.57243 4.44352 9.55779 4.20864 9.30485C3.97376 9.05191 3.9884 8.65645 4.24134 8.42157L6.66468 6.17128L4.24122 3.92088C3.98828 3.68601 3.97364 3.29055 4.20852 3.03761Z"
                fill="currentColor"
            />
        </g>
    </svg>
</template>
<style lang="scss" scoped>
.invite-svg {
    color: var(--adapt-packet-arr-right-color);
    vertical-align: top;
}
</style>
