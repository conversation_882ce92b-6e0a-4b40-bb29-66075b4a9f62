<script lang="ts">
export default {
    name: 'AdaptCouponCube',
};
</script>
<script setup lang="ts">
import { computed } from 'vue-demi';

import CouponCube from './coupon-cube.vue';
import { transformCoupon } from './transform';
import type { PlatformCouponData } from './types';

interface Props {
    /**
     * 服务端卡券数据
     */
    info: PlatformCouponData;
    /**
     * 标签
     */
    tag?: string;
    /**
     * 是否可用
     */
    btnDisabled?: boolean;
}
const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
    (event: 'coupon-click', info: PlatformCouponData): void;
}>();

const couponInfo = computed(() => transformCoupon(props.info));
function handleClick() {
    emit('coupon-click', props.info);
}
</script>

<template>
    <CouponCube
        :type="couponInfo.type"
        :value-main="couponInfo.mainDesc"
        :value-desc="couponInfo.subDesc"
        :value-image="couponInfo.img"
        :status="couponInfo.status"
        :tag="tag"
        :btn-disabled="btnDisabled"
        @coupon-click="handleClick"
    >
    </CouponCube>
</template>

<style lang="scss" scoped></style>
