<script lang="ts">
export default {
    name: 'AdaptLogo',
};
</script>

<script setup lang="ts">
import { useCdnLevel } from '@pet/25cny.cdn-image/useCdnLevel';
import XImage from '@pet/adapt.image/index.vue';
import { computed, defineAsyncComponent } from 'vue-demi';

interface Props {
    /**
     * Logo src
     */
    src?: string;
    /**
     * Logo 降级文案
     */
    brandLogoDegradeText?: string;
    /**
     * 加载重试次数
     */
    retryTimes?: number;
    /**
     * Logo color 兜底Logo为SVG格式，可以修改颜色
     */
    color?: string;
}
const props = withDefaults(defineProps<Props>(), {
    retryTimes: 3,
});

const KwaiLogoSvg = defineAsyncComponent(
    () => import('./assets/kwai-logo-svg.vue'),
) as unknown as typeof import('./assets/kwai-logo-svg.vue').default;

const kwaiLogoColor = computed(() => {
    return Boolean(props.color) ? { color: props.color } : {};
});
const { downgradeState } = useCdnLevel();
const isDowngrade = computed(() => downgradeState.value['P0']);

const emptyImg = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
</script>

<template>
    <div class="logo-wrapper">
        <div v-if="isDowngrade" class="degrade-text">{{ brandLogoDegradeText }}</div>
        <XImage v-else-if="src" :src="src" class="logo-image" :retry-times="retryTimes" :fallback-src="emptyImg" />
        <KwaiLogoSvg v-else class="logo-svg" :style="kwaiLogoColor" />
    </div>
</template>

<style>
:root {
    /* 默认logo宽度 */
    --adapt-logo-default-width: 60px;
    /* 默认logo高度 */
    --adapt-logo-default-height: 22px;
    /* logo容器高度 */
    --adapt-logo-wrapper-height: 40px;
    /* logo容器颜色 */
    --adapt-logo-wrapper-color: #fef6ca;
}
</style>

<style lang="scss" scoped>
.degrade-text {
    font-family: 'PingFang SC';
    font-size: 18px;
    font-weight: 600;
    line-height: 32px;
    text-align: center;
}
.logo-wrapper {
    height: var(--adapt-logo-wrapper-height);
    color: var(--adapt-logo-wrapper-color);
    display: flex;
    justify-content: center;
    align-items: center;
}
.logo-image {
    display: block;
    margin: auto;
    height: 100%;
    max-width: 100%;
    :deep(img) {
        object-fit: contain;
    }
}
.logo-svg {
    width: var(--adapt-logo-default-width);
    height: var(--adapt-logo-default-height);
}
</style>
