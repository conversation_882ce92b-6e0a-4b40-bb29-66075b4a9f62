import { useModel, createModel } from '@gundam/model';
import { NoLoginCode, BindErrorCode } from '@pet/adapt.error-handler/const';

import { useCityHomeApiModel } from '../api/city/homeApiModel';

/**
 * homeModel 数据都从这里拿，别直接从 apiModel 拿
 * 只对作用范围大的重要的数据进行 computed 导出
 * 业务数据建议自己单开 model
 */
export const cityHomeModel = createModel(() => {
    const {
        data: homeData,
        refreshHome,
        result,
        errorMsg,
        loading: homeLoading,
        doneStatus: homeDoneStatus,
        forceRefreshHome,
        blockingRefreshHandler,
        localPrefix,
    } = useCityHomeApiModel();

    return {
        homeData,
        refreshHome,
        forceRefreshHome,
        blockingRefreshHandler,
        homeLoading,
        homeDoneStatus,
        localPrefix,
        chessboard: computed(() => homeData.value?.chessboard ?? undefined),
        /** 是否登录 */
        isLogin: computed(() => !NoLoginCode.includes(result.value)),
        /** 首页-账户信息 */
        account: computed(() => homeData.value?.accountModel ?? {}),
        /** 倒计时 */
        countDown: computed(() => homeData.value?.countDown ?? undefined),
        // 主接口进度数据
        homeProgress: computed(() => homeData?.value?.chessboard?.progress),
        // 主接口阻塞格子任务弹窗数据
        homeGridTaskPopUp: computed(() => homeData?.value?.chessboard?.gridTaskPopUp),
        // 进度红包
        processingListView: computed(() => homeData?.value?.processingListView ?? undefined),
        /** 标题信息 */
        titleInfo: computed(() => homeData.value?.titleInfo ?? undefined),
        /** 中央建筑icon */
        cityIcon: computed(() => homeData.value?.cityIcon ?? undefined),
        /** 双绑异常 */
        isBindError: computed(() => BindErrorCode.includes(result.value)),
        /** 主接口异常信息 */
        errorMsg,
        /** 弹框数据 */
        popList: computed(() => homeData.value?.popList ?? []),
        /** 文旅账号 */
        cityTourismAccount: computed(() => homeData.value?.cityTourismAccount ?? undefined),
        /** 城市新鲜事 */
        cityNewsUrl: computed(() => homeData.value?.cityNewsUrl ?? undefined),
        /** 当前城市名称 */
        cityName: computed(() => homeData.value?.cityName ?? ''),
    };
});

export const useCityHomeModel = () => useModel(cityHomeModel);
