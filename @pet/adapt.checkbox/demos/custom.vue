<pet-info lang="json">
{ "title": "自定义", "description": "自定义样式", "priority": 66 }
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import CheckBox from '../index.vue';
const input1 = ref(true);
</script>

<template>
    <div class="demo">
        <h5>12px</h5>
        <CheckBox v-model="input1" class="item checkSize12px" />
        <CheckBox v-model="input1" class="item checkSize12px" type="flat" />
        <h5>14px 默认尺寸</h5>
        <CheckBox v-model="input1" class="item" />
        <CheckBox v-model="input1" class="item" type="flat" />
        <h5>16px</h5>
        <CheckBox v-model="input1" class="item checkSize16px" />
        <CheckBox v-model="input1" class="item checkSize16px" type="flat" />
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.demo {
    padding: 10px;
}

.item {
    + .item {
        margin-left: 10px;
    }
}

.checkSize12px {
    --adapt-checkbox-height: 12px;
}

.checkSize16px {
    --adapt-checkbox-height: 16px;
}
</style>
