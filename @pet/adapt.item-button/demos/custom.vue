<pet-info lang="json">
{ "title": "图标自定义", "description": "图标位置插入图片素材" }
</pet-info>
<script setup lang="ts">
import ItemButton from '../index.vue';
</script>

<template>
    <div class="demo">
        <ItemButton>
            <template #icon>
                <img
                    src="https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2022-03-18/1647596830941.e8bd99c546809e21.png"
                />
            </template>
            Likes
        </ItemButton>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    width: 100%;
}
</style>
