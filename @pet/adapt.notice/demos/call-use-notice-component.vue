<pet-info lang="json">
{ "title": "命令调用自定义组件", "description": "扩展形式" }
</pet-info>
<script setup lang="ts">
import { getCurrentInstance } from 'vue';
import Button from '@pet/adapt.button/index.vue';
import CustomComponent from './components/custom-component.vue';
import { useNotice } from '../useNotice';
const currentInstance = getCurrentInstance()?.proxy!;
const { createNotice } = useNotice(currentInstance);

function handleClick() {
    createNotice({
        customComponent: CustomComponent,
    });
}
</script>

<template>
    <div class="demo-box">
        <Button class="button" @click="handleClick">发送一个通知</Button>
    </div>
</template>

<style scoped>
@import '@pet/adapt.reset/reset.css';
.demo-box {
    position: fixed;
    inset: 100px 0 0 0;
    margin: auto;
    width: 200px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
}
</style>
