{"$schema": "https://static.yximgs.com/udata/pkg/WEB-LIVE/pet/pet-config.schema-v4.json", "name": "@pet/adapt.notice", "description": "inpush通知", "version": "0.0.0", "type": "mobile", "viewport": 414, "targets": ["vue3"], "documentFor": ["index.vue"], "dependencies": {"@pet/core.mobile": "0.0.0", "@pet/adapt.button": "0.0.0", "@pet/adapt.popup": "0.0.0", "@pet/adapt.transition": "0.0.0", "@pet/yau.core": "0.0.0", "@pet/adapt.card": "0.0.0", "@pet/adapt.reset": "0.0.0", "@pet/adapt.avatar": "0.0.0", "@vueuse/core": "^10.9.0"}}