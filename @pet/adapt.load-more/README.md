# @pet/adapt.load-more

## Props

| Prop name  | Description                            | Type    | Values | Default                     |
| ---------- | -------------------------------------- | ------- | ------ | --------------------------- |
| rootMargin | intersectionObservable rootMargin 参数 | string  | -      | '0px 0px 100px 0px'         |
| noMore     | 内容加载完毕的标志                     | boolean | -      | false                       |
| message    | 内容加载完毕提示语                     | string  | -      | 已经到底了，没有更多内容了~ |

## Events

| Event name | Properties                    | Description |
| ---------- | ----------------------------- | ----------- |
| visible    | **val** `boolean` - undefined | 可见状态    |
| to-bottom  |                               | 是否到底部  |

## Slots

| Name    | Description | Bindings |
| ------- | ----------- | -------- |
| default |             |
