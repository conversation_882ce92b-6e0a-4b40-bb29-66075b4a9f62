import { createModel, useModel } from '@gundam/model';
import { sleep } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';

import { useForwardRushBtnModel } from '@/models/city/forwardRushBtnModel';
import { TaskType, usePopupModel } from '@/models/popup.model';
import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

import { useBeginnerGuideModel } from './beginnerGuideModel';
import { forwardRushBtnModel } from './forwardRushBtnModel';
import { useCityHomeApiModel } from '../api/city/homeApiModel';

/**
 * ProcessModel
 * 流程化触发剧本
 */
export const processModel = createModel(({ getModelInstance }) => {
    const { data: homeData } = useCityHomeApiModel();
    const { openSummerPopup } = usePopupModel();
    const { firstTimeAccessGuide, notFirstTimeAccessGuide } = useBeginnerGuideModel();
    const { enableForwardRush } = useForwardRushBtnModel();
    const { log } = useCaptureDebugLog('processModel');

    // 每次home数据变化后，触发流程
    watch(
        homeData,

        async () => {
            // 存在 model 执行但 home 接口没有返回的情况
            if (!homeData.value) {
                return;
            }
            log('homeData watch');
            if (homeData.value?.popList?.length) {
                const popupData = homeData.value.popList[0];
                log('popList 有值');
                if (popupData.popupType === PopupType.CITY_PAGE_GUIDE_POPUP) {
                    enableForwardRush.value = false;
                    await firstTimeAccessGuide(popupData);
                    // 首次进入完整引导需要 7000ms 才能播放完
                    await sleep(2000);
                    enableForwardRush.value = true;
                    getModelInstance(forwardRushBtnModel)?.autoForwardRush();
                } else if (popupData.popupType === PopupType.CITY_PAGE_DAILY_GUIDE_POPUP) {
                    enableForwardRush.value = false;
                    notFirstTimeAccessGuide(popupData);
                    // 进入完整引导需要 2500ms 才能播放完
                    await sleep(2000);
                    enableForwardRush.value = true;
                    getModelInstance(forwardRushBtnModel)?.autoForwardRush();
                } else if (popupData.popupType === PopupType.CITY_LLCH_LLAWD_GRID_TASK) {
                    // 挑战格子任务奖励弹窗
                    // 关闭格子任务弹窗
                    log('[GridTask] 关闭格子任务弹窗', popupData.popupType);
                    await getModelInstance(forwardRushBtnModel)?.closeGridTask();
                    // 打开奖励弹窗
                    log('[GridTask] 打开奖励弹窗');
                    const LLCHTask = openSummerPopup(popupData, {
                        taskType: TaskType.PASSIVE_POPUP,
                    });
                    LLCHTask?.end.finally(() => {
                        // 格子任务完成，关闭奖励弹窗后，自动向前冲一步
                        log('[GridTask] 自动向前冲一步');
                        getModelInstance(forwardRushBtnModel)?.handleForwardRush('gridTask');
                    });
                } else {
                    // 其他派发弹窗（包含普通任务奖励弹窗）
                    openSummerPopup(popupData, {
                        taskType: TaskType.PASSIVE_POPUP,
                    });
                }
            }
        },
        {
            immediate: true,
            deep: true,
        },
    );

    return {};
});

export const useProcessModel = () => useModel(processModel);
