<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import { watchImmediate } from '@vueuse/core';

import type { PilotType } from '@/components/charge-forward/types';
import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';

const { ipStyle } = useForwardRushGridModel();

const Pilot = defineAsyncComponent(() => import('@pet/adapt.bezier-path-fly/index.vue'));

const props = defineProps<{
    delay: number;
}>();
const emits = defineEmits<{
    (e: 'ended'): void;
}>();
const pilotRef = ref<PilotType | null>(null);
const show = ref(false);

const flyEnd = () => {
    emits('ended');
    show.value = false;
};

watchImmediate(pilotRef, (val) => {
    if (val) {
        setTimeout(() => {
            show.value = true;
            pilotRef.value?.boot();
        }, props.delay ?? 0);
    }
});
function toPropsValue(props: string) {
    return props.split(',').map((n) => +n);
}
const pathBezierRef = ref('0.0, 0.0, 1.0, 1.0');
const pathBezier = computed(() => toPropsValue(pathBezierRef.value));
</script>
<template>
    <Pilot
        v-show="show"
        ref="pilotRef"
        class="llward-pilot"
        fly-to-target="earn-card-amount"
        :duration="700"
        :fly-to-target-offset="{ x: -20, y: -34 }"
        :bezier="pathBezier"
        @end="flyEnd"
    >
        <div :style="ipStyle">
            <div class="llward-icon-static llward-icon-scale"></div>
        </div>
    </Pilot>
</template>
<style lang="scss" scoped>
.llward-pilot {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 101;
    .llward-icon {
        position: absolute;
        width: 76px;
        height: 76px;
    }
    .llward-icon-static {
        position: absolute;
        width: 76px;
        height: 76px;
        left: -20px;
        top: -30px;
        @include bg('../../../assets/bg/cash.png');
        background-size: 100% 100%;
    }
}
.llward-icon-scale {
    animation: llward-icon-ani-scale 0.7s 0s cubic-bezier(0.167, 0, 0.833, 1.2) forwards;
}

@keyframes llward-icon-ani-scale {
    0% {
        transform: scale(1, 1);
    }
    100% {
        transform: scale(2, 2);
    }
}

@keyframes llward-icon-ani-rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(90deg);
    }
}
</style>
