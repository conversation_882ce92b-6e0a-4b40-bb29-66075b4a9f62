<script lang="ts" setup>
import { useIntersectionObserver } from '@vueuse/core';
import { computed, ref, watch } from 'vue-demi';

import Avatar from '@pet/adapt.avatar/index.vue';
import type { AvatarProps, TaskListOptions } from '@pet/25cny.task-list/components/types';
import type { TaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import { useLoggerEmits } from '@pet/work.task-list-core/utils/useLoggerEmits';
const emits = defineEmits<{
    (e: 'clickAvatar', uid: number): void;
}>();

const props = defineProps<{
    taskInfo: TaskInfo;
    userInfo: {
        avatar: string;
        username: string;
        userId: number;
    };
    uiPassThroughConfig?: Exclude<
        Exclude<TaskListOptions['uiPassThroughConfig'], undefined>['relationChainTask'],
        undefined
    >['avatar'];
}>();

const connectionAvatarRef = ref<HTMLDivElement | null>(null);
const targetIsVisible = ref(false);
const emitLoggerEvent = useLoggerEmits().emitLoggerEvent;

const { stop } = useIntersectionObserver(connectionAvatarRef, (arr) => {
    if (arr?.[0]?.isIntersecting) {
        targetIsVisible.value = true;
    }
});

watch(
    () => targetIsVisible.value,
    (visible) => {
        if (visible) {
            emitLoggerEvent(
                {
                    logType: 'connectionAvatarShow',
                    logParam: {
                        uid: props.userInfo.userId,
                    },
                },
                props.taskInfo,
            );
        }
    },
);

function getAvatarProps(computedProps: AvatarProps) {
    const baseProps = props.uiPassThroughConfig?.base ?? {};
    const overrideProps = props.uiPassThroughConfig?.override ?? {};
    return {
        ...baseProps,
        ...computedProps,
        ...overrideProps,
    };
}
</script>

<template>
    <div ref="connectionAvatarRef" class="user-card">
        <Avatar
            v-if="userInfo.userId > -1"
            v-bind="
                getAvatarProps({
                    src: userInfo.avatar,
                    width: 44,
                })
            "
        />
        <div v-else class="icon" @click="emits('clickAvatar', userInfo.userId)">
            <img src="../assets/union.png" class="icon-union" />
        </div>
    </div>
</template>

<style scoped lang="scss">
.user-card {
    flex-shrink: 0;
    width: 44px;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:last-child {
        margin-right: 0;
    }
    .username {
        line-height: 13px;
        margin-top: 4px;
        font-size: 10px;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        color: #ab2132;
        text-align: center;
    }

    .icon {
        width: 44px;
        height: 44px;
        background-color: rgba(85, 0, 0, 0.03);
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        &-union {
            height: 15px;
            width: 15px;
        }
    }
}
</style>
