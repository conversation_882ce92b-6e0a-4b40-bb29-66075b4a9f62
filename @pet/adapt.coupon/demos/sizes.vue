<pet-info lang="json">
{ "title": "卡券尺寸", "description": "所有卡券的尺寸", "priority": 9 }
</pet-info>
<script setup lang="ts">
import Coupon from '../index.vue';
import type { PlatformCouponData } from '../types';

const displayTypeNumber: PlatformCouponData = {
    displayType: 'number',
    branding: false,
    name: '日常测试',
    icon: '',
    desc: '全平台可用（除少数商品外）',
    forwardUrl:
        'https://app.kwaixiaodian.com/page/kwaishop-pub-couponuse/v20211111?couponTargetName=平台通用券&layoutType=4&enableWK=1&__launch_options__=%7B%22enableLoading%22%3Atrue%2C%22enableProcess%22%3Afalse%7D&breakupTime=0&webview_bgcolor=%23f80031&breakupTime=0&hyId=couponus&entry_src=cnycoupongezi&source=cnycoupongezi&couponId=6201481043&couponTargetType=3&doorPrice=1000&reducePrice=100&couponStartTime=1660201848196&couponEndTime=1660205448196&couponSourceType=1',
    forwardText: '立即使用',
    status: 'unused',
    value: 999,
    valueText: '1',
    valueDesc: '满10元可用',
    expiredTime: 1660205448196,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeDiscount: PlatformCouponData = {
    displayType: 'discount',
    branding: false,
    name: '优惠券',
    icon: 'https://kcdn.staging.kuaishou.com/kos/nlav100106/encourage-1658904825620-pvIssv.jpg',
    desc: '仅限新客使用',
    forwardUrl: 'https://www.baidu.com/zzzzzzzzzz',
    forwardText: '立即使用',
    status: 'unused',
    value: 5.0,
    valueText: '5.0',
    valueDesc: '无门槛',
    expiredTime: 1661928815415,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeImage: PlatformCouponData = {
    displayType: 'image',
    branding: false,
    name: '直播勋章',
    desc: '五一活动直播勋章',
    forwardUrl:
        'kwai://liveaggregatesquare?liveSquareSource=1&sourceType=269&liveSource=HOMEPAGE_51_ACTIVITY_PAGE_2&internaljump=kwailive://giftpanel?tab=PropsPanel&sourceType=h5',
    forwardText: '去佩戴',
    status: 'unused',
    value: 0.0,
    image: 'http://blobstore-nginx.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1659064164269-oifvCN.png',
    expiredTime: 1665648351770,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeText: PlatformCouponData = {
    displayType: 'text',
    branding: false,
    name: '优惠券',
    icon: 'https://kcdn.staging.kuaishou.com/kos/nlav100106/encourage-1658904825620-pvIssv.jpg',
    desc: '仅限电商新客首单使用',
    forwardUrl: 'https://www.baidu.com/zzzzzzzzzz',
    forwardText: '立即使用',
    status: 'unused',
    value: 0.0,
    valueText: '首单免减',
    valueDesc: '满100可用',
    expiredTime: 1661928815415,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};
</script>

<template>
    <div class="wrapper">
        <div class="demo">
            <b>size mini</b>
            <p>用于比较小的卡面(如集卡)</p>
            <Coupon :info="displayTypeNumber" size="mini"></Coupon>
            <b>size small</b>
            <p>用于layer蒙层上</p>
            <Coupon :info="displayTypeNumber" size="small"></Coupon>
            <b>size small+no-bkg</b>
            <p>用于红包打开后</p>
            <Coupon :info="displayTypeNumber" size="mini" no-bkg></Coupon>
            <b>size medium</b>
            <p>用于弹窗内部</p>
            <Coupon :info="displayTypeNumber" size="medium"></Coupon>
            <b>size large</b>
            <p>用于钱包或者页面级别的展示</p>
            <Coupon :info="displayTypeNumber" size="large"></Coupon>
        </div>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    width: 100%;
    justify-content: center;
    background: #f5f5f5;
}

.demo {
    padding: 10px 0;
    b {
        display: block;
        margin: 10px 0;
    }
}
</style>
