<script setup lang="ts">
import AdaptTransition from '@pet/adapt.transition/index.vue';
import useCaptureDebugLog from '@pet/yau.logger';

import { useBubbleModel } from '@/models/city/bubbleModel';
import { useCityHomeModel } from '@/models/city/homeModel';
import { useSnapShotModel } from '@/models/city/snapShotModel';
import { replaceBracketsWithColor } from '@/utils/replace';

import RewardPoint from './RewardPoint.vue';

const logName = 'progressBubble';
const { log } = useCaptureDebugLog(logName);

const { processingListView } = useCityHomeModel();
const { showBubbleType } = useBubbleModel();
const { currentStep } = useSnapShotModel();

const processingViews = computed(() => processingListView.value?.processingViews ?? []);

// 兼容接口未下发现金红包金额
const defaultLLewd = '？？';
const ROUND_NAX_STEPS = 12;

const lastTopText = ref('');
const topText = ref('');

watch(
    () => topText.value,
    () => {
        lastTopText.value = topText.value;
    },
);

const computedTopText = () => {
    // log('__________ computedTopText', currentStep.value);
    if (currentStep.value === -1 || currentStep.value === 0) {
        // 初始态
        return `冲到终点必得{${processingListView.value?.totalLLewd ?? defaultLLewd}元！}`;
    }

    const processingViews = processingListView.value?.processingViews ?? [];
    // 第一个未到达节点（下个节点）
    const nextLLewdIdx = processingViews.findIndex((item) => item.opened === false);
    // 所有节点都已到达
    // if (nextLLewdIdx === -1) {
    //     return '最后 0 步冲大奖';
    // }
    const nextLLewd = processingViews[nextLLewdIdx];
    const remainStep = (nextLLewd?.step ?? 0) - currentStep.value;

    if (remainStep === 0 || currentStep.value === ROUND_NAX_STEPS) {
        log(`remainStep === 0 || currentStep === ${ROUND_NAX_STEPS}`);
        // 距离下个节点步数为 0, 可能是 processingViews 尚未更新，保留上一个文本
        return lastTopText.value;
    }

    if (nextLLewdIdx === processingViews.length - 1) {
        // 下一个节点为最终大奖
        return `最后${remainStep}步冲{大奖}`;
    }

    // 尚未到达第 n 个节点
    const hasCnQuestion = nextLLewd?.llrewdView?.includes('?');
    const hasEnQuestion = nextLLewd?.llrewdView?.includes('？');
    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
    if (hasCnQuestion || hasEnQuestion) {
        // 黑盒奖励（包含中英文符号）
        return `再冲${remainStep}步 必得{现金}`;
    }
    // 白盒奖励
    return `再冲${remainStep}步 必得{${nextLLewd?.llrewdView ?? defaultLLewd}元}`;
};

const pointList = computed(() => {
    if (processingViews.value?.length) {
        return processingViews.value?.map((item) => {
            const step = item.step ?? 0;
            return {
                ...item,
                opened: item.opened ?? false,
                step,
                icon: item.icon ?? '',
                llrewdView: item.llrewdView ?? defaultLLewd,
                text: `第${step}步`,
            };
        });
    }

    return [];
});

const TEXT_HIGHLIGHT_COLOR = '#FE3666';

const text = computed(() => {
    return replaceBracketsWithColor(topText.value, TEXT_HIGHLIGHT_COLOR);
});

const curStyles = ref({});
let retryCount = 0;
const MAX_RETRY = 3;
const transition = 'width 0.8s ease-in-out';

const computedCurStyles = (from: string) => {
    // log(`computedCurStyles from: ${from}`);

    // 第一个未到达节点
    const nextPointIdx = pointList.value.findIndex((item) => item.opened === false);
    if (pointList.value.length === 0 || nextPointIdx === 0) {
        // 所有节点都未到达
        curStyles.value = {
            width: 0,
            transition,
        };
        return false;
    }

    // 最后一个已到达节点
    const arrivedPointIdx = pointList.value.map((item) => item.opened).lastIndexOf(true);
    if (arrivedPointIdx === pointList.value.length - 1) {
        log(`computedCurStyles all arrived`);
        // 所有节点都到达
        curStyles.value = {
            width: '100%',
            transition,
        };
        return false;
    }

    const nextPoint = pointList.value[nextPointIdx];
    const nextPointEl = document.getElementById(`point_icon_${nextPoint.step}`);

    const arrivedPoint = pointList.value[arrivedPointIdx];
    const arrivedPointEl = document.getElementById(`point_icon_${arrivedPoint.step}`);
    if (!nextPointEl || !arrivedPointEl) {
        log(`computedCurStyles dom unReady`);

        if (retryCount < MAX_RETRY) {
            retryCount++;
            nextTick(() => {
                computedCurStyles('retry_' + retryCount);
            });
        }
        return false;
    }

    const baseArrivedWidth = arrivedPointEl?.offsetLeft + arrivedPointEl?.offsetWidth;

    curStyles.value = {
        width: `${baseArrivedWidth + (nextPointEl?.offsetLeft - baseArrivedWidth) / 2}px`,
        transition,
    };
    log(`computedCurStyles success: ${from}`);
};

watch(
    () => [pointList.value, showBubbleType.value],
    () => {
        if (showBubbleType.value === 'progress') {
            computedCurStyles('watch');
        }
    },
    {
        immediate: true,
    },
);

// 进度红包 或 当前步数 更新时，更新顶部文案
watch(
    () => [processingListView.value, currentStep.value],
    () => {
        topText.value = computedTopText();
    },
    {
        immediate: true,
    },
);
</script>

<template>
    <AdaptTransition name="scale-in-out-debounce" :duration="{ enter: 0, leave: 200 }">
        <div
            v-show="showBubbleType === 'progress'"
            :class="{ progress_bubble: true, mini_bubble: pointList.length < 5 }"
        >
            <div class="content_wrap">
                <!-- eslint-disable vue/no-v-html -->
                <div class="tip" v-html="text"></div>

                <div class="progress_wrap">
                    <div class="progress_ctn">
                        <div class="cur_line" :style="curStyles"></div>
                    </div>

                    <RewardPoint
                        v-for="(item, index) in pointList"
                        :key="index"
                        v-bind="item"
                        :is-last-point="index === pointList.length - 1"
                        :log-name="logName"
                    />
                </div>
                <div class="arrow"></div>
            </div>
        </div>
    </AdaptTransition>
</template>

<style lang="scss" scoped>
.progress_bubble {
    width: 358px;
    height: 126px;
    border-radius: 22.73px;
    background-color: #fff;
    transform-origin: bottom center;
    position: absolute;
    top: 0;

    .content_wrap {
        padding: 14px 14px 15px 14px;

        .tip {
            height: 20px;
            line-height: 20px;
            text-align: center;
            margin-bottom: 12px;

            font-family: PingFang SC;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0;
            color: #000;
        }

        .progress_wrap {
            display: flex;
            justify-content: space-between;
            position: relative;

            .progress_ctn {
                width: 305px;
                background: rgba(0, 0, 0, 0.08);
                position: absolute;
                height: 9px;
                left: 0;
                top: 20px;

                .cur_line {
                    width: 0;
                    height: 100%;
                    border-radius: 100px;
                    overflow: hidden;
                    background: linear-gradient(-86.42deg, #ffcd4f 7.43%, #fe3666 25.91%, #fe3666 94.81%);

                    position: absolute;
                    left: 0;
                    top: 0;
                }
            }
        }
    }

    .arrow {
        width: 26.5px;
        height: 6.5px;
        @include bg('./assets/arrow.png');
        background-size: 100% 100%;
        position: absolute;
        // TIPS: 多上移 0.1px，避免出现衔接线
        bottom: -6.4px;
        left: 50%;
        transform: translateX(-50%);
    }

    &.mini_bubble {
        width: 288px;

        .content_wrap .progress_wrap .progress_ctn {
            width: 235px;
        }
    }
}
</style>
