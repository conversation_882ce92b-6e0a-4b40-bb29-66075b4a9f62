<script lang="ts" setup>
import EmptyStatus from '@pet/adapt.empty-status/index.vue';
import type { IconName } from '@pet/adapt.empty-status/types';
import Frame from '@pet/adapt.frame/index.vue';
import Back from '@pet/adapt.back/index.vue';
import { isAtFourTab, isAtSearchTab, isIOS } from '@pet/yau.core/device';
import { useRoute, useRouter } from '@pet/yau.yoda/route/useRouterBack';
import { openUrlInIframe } from '@pet/yau.core/helper';
import { invoke } from '@yoda/bridge';
import type { Component as VueComponent } from 'vue-demi';
import { onMounted, computed } from 'vue-demi';

import { ErrorCode, URLS, getAppMarketUrl, NoLoginCode, LoginExceptionCode } from './const';
import { loginApp } from './loginApp';
import { useOpenPage } from '@pet/yau.yoda';

interface IconInfos {
    [key: string]: VueComponent;
}

interface Props {
    iconComponents?: IconInfos;
}

const props = withDefaults(defineProps<Props>(), {});

const route = useRoute();

function getAbsolutePath(path: string) {
    console.log('route', route);
    const url = new URL(window.location.href);
    const baseUrl = url.origin + url.pathname.replace(route.path, '');
    return baseUrl + path;
}
/**
 * 去除字符串中的HTML标签
 * @param input 可能包含HTML标签的字符串
 * @returns 去除了HTML标签的纯文本字符串
 */
function stripHtmlTags(input?: string) {
    if (!input) {
        return '';
    }
    const doc = new DOMParser().parseFromString(input, 'text/html');
    return doc.body.textContent ?? '';
}
// 这里为了防止文案输出被使用方改用v-html这里加强处理一下过滤掉所有标签
const errorMessage = stripHtmlTags(route?.query?.msg as string) ?? '系统繁忙';
const errorCode = route?.query?.code != null ? Number(route?.query?.code) : ErrorCode.NetworkError;
const backUrlRelative = (route?.query?.backUrl ?? '') as string;
const backUrl = getAbsolutePath(backUrlRelative);

const customTheme = computed(() => route?.query?.customClass);
const errorPageStyle = computed(() => {
    const color =
        typeof route?.query?.color === 'string'
            ? {
                color: route?.query?.color,
            }
            : {};

    const bgColorArray: string[] = route?.query?.bgColor?.toString().split(',') ?? [];
    let bgColor = {};
    if (bgColorArray?.length === 1) {
        bgColor = {
            background: bgColorArray[0],
        };
    } else if (bgColorArray?.length > 1) {
        bgColor = {
            background: `linear-gradient(${bgColorArray.join(',')})`,
        };
    }

    return {
        ...color,
        ...bgColor,
    };
});

const computedIcon = computed(() => props.iconComponents?.[Number(route?.query?.code)]);

const searchParams = new URLSearchParams(backUrl.split('?')[1]);

const atHalfWebView = searchParams.get('layoutType') === '3';

const needBackBtn = computed(() => !(isAtFourTab() || atHalfWebView || isAtSearchTab()));

function getIconName(errorCode: number): IconName {
    if ([...NoLoginCode, ...LoginExceptionCode].includes(errorCode)) {
        return 'noBody';
    }
    switch (errorCode) {
        case ErrorCode.NotStart:
            return 'notStart';
        case ErrorCode.End:
        case ErrorCode.TestEnd:
            return 'end';
        case ErrorCode.ProhibitedArea:
        case ErrorCode.JumpLowDevice:
        case ErrorCode.KpnNotSupport:
        case ErrorCode.OutSideBanned:
        case ErrorCode.PureHarmonyOS:
            return 'band';
        case ErrorCode.UserBand:
        case ErrorCode.UserSocialBand:
            return 'anomaly';
        case ErrorCode.LowClientVersion:
        case ErrorCode.LowSystemVersion:
            return 'download';
        case ErrorCode.ParentControl:
            return 'protect';
        case ErrorCode.ActivityHot:
        case ErrorCode.UserRequestLock:
        case ErrorCode.SystemError:
            return 'activityHot';
        case ErrorCode.NetworkError:
            return 'network';
        case ErrorCode.WARMUP_TO_EVE:
            return 'end';
        default:
            return 'network';
    }
}

const showErrorMessage = () => {
    if (NoLoginCode.includes(errorCode)) {
        return '未登录，请登录后再参与此活动';
    } else if (LoginExceptionCode.includes(errorCode)) {
        return '登录异常，请重新登录快手账号';
    } else {
        return errorMessage;
    }
};

const { doLogin } = loginApp();
const openPage = useOpenPage();
const goToPage = (link: string) => {
    link && openPage(link, { forceOpenInNewWebview: true, keepQuery: false });
};
function getButtonCtrl(errorCode: number) {
    if (NoLoginCode.includes(errorCode)) {
        return {
            button: true,
            buttonName: '去登录',
            onBtnClick: () => {
                doLogin(backUrl);
            },
        };
    }
    switch (errorCode) {
        case LoginExceptionCode[0]:
        case LoginExceptionCode[1]:
            return {
                buttonName: '立即刷新',
                onBtnClick: () => {
                    window.location.replace(backUrl);
                },
            };
        case ErrorCode.NetworkError:
        case ErrorCode.ActivityHot:
        case ErrorCode.UserRequestLock:
        case ErrorCode.SystemError:
        case ErrorCode.StationUpdateError:
            return {
                buttonName: '立即刷新',
                onBtnClick: () => {
                    window.location.replace(backUrl);
                },
            };
        case ErrorCode.End:
        case ErrorCode.TestEnd:
            return {
                buttonName: '查看奖励',
                onBtnClick: () => {
                    goToPage("kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=8147&themeStyle=1&entry_src=ks_2025sum_090");
                },
            };
        default:
            return {
                buttonName: '',
                onBtnClick: () => { },
            };
    }
}

const errorIcon = getIconName(errorCode);
const errorButtonAction = getButtonCtrl(errorCode);

async function nativeBack() {
    await invoke('webview.popBack');
}

async function hideLoadingPage() {
    try {
        await invoke('ui.hideLoadingPage');
    } catch { }
}

onMounted(async () => {
    hideLoadingPage();
    if (NoLoginCode.includes(errorCode)) {
        doLogin(backUrl);
    }
});

/**
 * 重置可能存在的上游页面加的滑动返回劫持
 * @param cb 按钮点击事件回调，当不传 onClick 回调，将重置设置的事件
 */
async function setPhysicalBackButton(cb?: () => void) {
    if (isIOS() || isAtFourTab() || isAtSearchTab()) {
        return;
    }
    try {
        await invoke('kwai.setPhysicalBackButton', {
            onClick: cb,
        });
    } catch (e) {
        console.log(e);
    }
}
/**
 * 系统返回重置为关闭当前webview
 */
setPhysicalBackButton(async () => {
    try {
        await invoke('webview.exitWebView');
    } catch (e) {
        console.error('webview.exitWebView:::', e);
    }
});
</script>

<template>
    <Frame :has-status-bar="false" class="error-page" :class="customTheme" :style="errorPageStyle">
        <Back v-if="needBackBtn" class="back-icon" :before-back="nativeBack">
            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M20.3924 14.293C20.0019 13.9025 19.3687 13.9025 18.9782 14.293L11.9071 21.3641C11.5166 21.7546 11.5166 22.3878 11.9071 22.7783L18.9782 29.8494C19.3687 30.2399 20.0019 30.2399 20.3924 29.8494C20.7829 29.4589 20.7829 28.8257 20.3924 28.4352L14.0284 22.0712L20.3924 15.7073C20.7829 15.3167 20.7829 14.6836 20.3924 14.293Z"
                    fill="#181C34"
                />
            </svg>
        </Back>
        <div class="header-title">百日心愿之旅</div>
        <EmptyStatus
            :icon="errorIcon"
            :button="!!errorButtonAction.buttonName"
            :button-text="errorButtonAction.buttonName"
            :in-component="false"
            @btn-click="errorButtonAction.onBtnClick"
        >
            <template v-if="computedIcon" #icon>
                <component :is="computedIcon" />
            </template>
            <div class="content-main">{{ showErrorMessage() }}</div>
        </EmptyStatus>
    </Frame>
</template>
<style>
:root {
    /* 页面背景 */
    --adapt-error-handler-background: #FFE6DE;
    /* icon与文字的颜色 */
    --adapt-error-handler-color: #222222;
    /* 图标透明度 */
    --adapt-empty-status-icon-opacity: 1;
}
</style>
<style lang="scss" scoped>
.error-page {
    --adapt-empty-status-icon-color: var(--adapt-error-handler-color);
    overflow: hidden;
    background: var(--adapt-error-handler-background);
    /* 为了避免iOS16弹性滚动，不判断改了通用布局 */
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
}
.header-title {
    position: absolute;
    left: 50%;
    line-height: 48px;
    transform: translate(-50%, 0%);
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 17px;
    color: #222222
}
.back-icon {
    position: absolute;
    left: 12px;
}
.content-main {
    white-space: pre-line;
}
</style>
