/**
 *  number   数字类型
 *  discount 折扣类型
 *  image    图片类型
 *  text     文字类型
 *  special  特价商品
 *  [中台券格式统一](https://docs.corp.kuaishou.com/d/home/<USER>
 */
export type PlatformCouponDisplayType = 'number' | 'discount' | 'image' | 'text' | 'special' | 'unknown';

/**
 * unStarted 未到使用时间
 * unused   未使用
 * used     已使用
 * expired  过期
 * invalid  失效
 * view     前往查看
 * reviewing 审核中
 * reviewFailure 审核失败
 * received 已接收
 */
export type PlatformCouponStatus =
    | 'unStarted'
    | 'unused'
    | 'used'
    | 'expired'
    | 'invalid'
    | 'view'
    | 'reviewing'
    | 'reviewFailure'
    | 'received';

export interface AddressInfo {
    receiverName: string;
    phoneNumberName: string;
    address: string;
}

export interface PlatformCouponData {
    /**
     * 卡券对应的视图类型
     */
    displayType: PlatformCouponDisplayType;
    /**
     * 是否为总冠
     */
    branding: boolean;
    /**
     * 标题
     */
    name: string;
    /**
     * 描述
     */
    desc: string;
    /**
     * 所属商家logo
     */
    icon?: string;
    /**
     * 跳转链接
     */
    forwardUrl: string;
    /**
     * 按钮文案
     */
    forwardText: string;
    /**
     * 状态
     */
    status: PlatformCouponStatus;
    /**
     * 卡券左侧配图
     */
    image?: string;
    /**
     * 券价值，对应数字类（单位分）折扣类（单位折）
     */
    value?: number;
    /**
     * 券价值
     */
    valueText?: string;
    /**
     * 券价值说明
     */
    valueDesc?: string;
    /**
     * 过期时间
     */
    expiredTime: number;
    /**
     * 原价
     */
    originalValue?: number;

    /**
     * 奖品 Id
     */
    prizeId: number;

    /**
     * 奖品类型
     */
    prizeType: string;

    shopCouponId: string;

    productItemId: number;

    commercialId?: string;

    [key: string]: any;

    /**
     * 记录 Id
     */
    recordId?: number;

    /**
     * 实物与虚拟奖品信息
     */
    addressInfo?: AddressInfo;
    /**
     * 有效开始时间
     */
    validTime?: number;
    /**
     * 发奖侧subBizId
     */
    subBizId?: number;

    /**
     * 自定义奖品 兑换码
     */
    cdKey?: string;
    /**
     * 权益ID
     */
    rightId?: string;
}

export enum PLATFORM_PRIZE_TYPE {
    /**
     * 实物奖品(非电商)
     */
    PHYSICAL = 'PHYSICAL',
    /**
     * 虚拟奖品(线下核销)
     */
    OFFLINE_VIRTUAL = 'OFFLINE_VIRTUAL',
    /**
     * 实物奖品0元购(电商)
     */
    PHYSICAL_PRODUCT = 'PHYSICAL_PRODUCT',

    /**
     * 虚拟奖品, 视频会员
     */
    CD_KEY = 'CD_KEY',
}

/**
 * rebate 减满券
 * discount 折扣券
 * default 图片券
 * benefit 利益点券
 * special 特价商品券
 */
export type CouponDisplayType = 'rebate' | 'discount' | 'default' | 'benefit' | 'special';

export type CouponSize = 'simple' | 'mini' | 'small' | 'medium' | 'large' | 'full';

export type UseStatus =
    | 'not_use'
    | 'has_used'
    | 'out_of_date'
    | 'review'
    | 'review_fail'
    | 'not_grant'
    | 'granted'
    | 'not_started'
    | 'received';

export type CouponUIConfig = {
    size?: CouponSize;
    showUseBtn?: boolean;
    btnText?: string;
    btnDisabled?: boolean;
    noBkg?: boolean;
};
