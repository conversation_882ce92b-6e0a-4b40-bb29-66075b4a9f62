<script setup lang="ts">
import { ref, computed } from 'vue-demi';

import XImage from '../index.vue';

const imgSrc = ref(
    'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-08-02/1690956811255.40e0e432d13f1fb0.png',
);

// const imgSrcSlow = computed(() => `https://deelay.me/2000/${imgSrc.value}`);
</script>

<template>
    <div class="demo">
        <div class="container is-scroll">
            <h5>向下滚动, 查看请求</h5>
            <XImage :src="imgSrc" class="image-item" loading="lazy" loading-ctrl="self" root-margin="0px 0px 0px 0px" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
}
.image-item {
    width: 150px;
    height: 46px;
}
.is-scroll {
    width: 300px;
    max-height: 200px;
    overflow: auto;
    h5 {
        margin-bottom: 400px;
    }
}
div {
    font-size: 16px;
}
</style>
