<!-- 页面头部组件，包含按钮挂件、标题，还管 用户退出时的逻辑 -->
<script setup lang="ts">
import IconWidget from '@pet/adapt.icon-widget/index.vue';
import IconMusic from '@pet/adapt.icons/music-svg.vue';
import IconRule from '@pet/adapt.icons/rule-bold-svg.vue';
// import IconScan from '@pet/adapt.icons/scan-svg.vue';
import IconService from '@pet/adapt.icons/service-svg.vue';
import ItemButton from '@pet/adapt.item-button/index.vue';
import Logo from '@pet/adapt.logo/index.vue';
import Popover from '@pet/adapt.popover/index.vue';
import StatusBar from '@pet/adapt.status-bar/expand-mode.vue';
import ToggleSwitch from '@pet/adapt.toggle-switch/index.vue';
// import { debounce, isAtFourTab, isAtSearchTab } from '@pet/yau.core';
import { useOpenPage } from '@pet/yau.yoda';
import { invoke } from '@yoda/bridge';

import { useLogger } from '@/init/logger';
import { useAudioModel } from '@/models/audioModel';
import { useConfigModel } from '@/models/city/configModel';
import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';
import { useCityHomeModel } from '@/models/city/homeModel';
import { useLlwardAnimation } from '@/models/city/llwardAnimation';
import { useNavigate } from '@/models/utils/useNavigate';
import { setPhysicalBackButton } from '@/utils/gesture';
// import { scan } from '@/utils/scan';
import { inHeadless } from '@/utils/ssg';

const { kconfUEConfig } = useConfigModel();
const { globalSwitch, toggleAudioMute } = useAudioModel();
const { showWallet } = useLlwardAnimation();
const { autoScroll } = useForwardRushGridModel();
const { gotoHomePage } = useNavigate();
const walletLoaded = ref(false);

const { titleInfo } = useCityHomeModel();
const openPage = useOpenPage();
const { sendClick } = useLogger();

const goToPage = (link: string) => {
    link && openPage(link, { forceOpenInNewWebview: true, keepQuery: false });
};

// 更多下拉菜单
const popoverShow = ref(false);

const goBack = () => {
    gotoHomePage();
};

const onMoreClick = () => {
    popoverShow.value = !popoverShow.value;
    sendClick('OP_ACTIVITY_ICON_BUTTON', {
        title: '更多',
    });
};

// 点击返回
const customBack = async () => {
    sendClick('OP_ACTIVITY_ICON_BUTTON', {
        title: '返回',
    });
    goBack();
};

// 物理键返回
setPhysicalBackButton(() => {
    customBack();
});

onMounted(async () => {
    try {
        await invoke('kwai.setPhysicalBackButton', {
            onClick: () => {
                customBack();
            },
        });
    } catch (err) {
        console.log('监听原生系统后退行为失败', err);
    }
});
</script>

<template>
    <StatusBar
        ref="statusBar"
        class="status-bar"
        background="rgba(0,0,0,0)"
        fold-icon-color="#fff"
        :title="titleInfo?.title ?? ''"
        :show-back="!autoScroll"
        :display-fold-bar="false"
        :custom-back="customBack"
    >
        <template #mainTitleTop>
            <Logo
                v-if="!inHeadless()"
                v-log="{
                    type: 'show',
                    action: 'OP_ACTIVITY_BUSINESS_LOGO',
                    params: {
                        url: titleInfo?.logo || undefined,
                    },
                }"
                :src="titleInfo?.logo || undefined"
            />
        </template>
        <template #mainTitle>
            <div v-if="!inHeadless()" class="title">{{ titleInfo?.title ?? '' }}</div>
        </template>
        <template #mainTitleBottom>
            <div v-if="!inHeadless()" class="sub-title">
                {{ titleInfo?.subTitle ?? '' }}
            </div>
            <div v-else class="empty-subtitle"></div>
        </template>
        <template #left>
            <div id="wallet" class="wallet-area">
                <IconWidget
                    v-click-log="{
                        action: 'OP_ACTIVITY_ICON_BUTTON',
                        params: {
                            title: '钱包',
                        },
                    }"
                    type="wallet"
                    class="wallet-icon"
                    :class="{ 'wallet-hidden': showWallet && walletLoaded }"
                    @click="goToPage(kconfUEConfig?.walletUrl)"
                />
            </div>
        </template>
        <template #right>
            <div class="more-wrapper" @click="onMoreClick">
                <IconWidget type="more" class="more-btn" />
                <Popover
                    :show="popoverShow"
                    class="MorePop"
                    :position="{ left: -128 }"
                    arr-align="end"
                    :arr-distance="15"
                    @close="popoverShow = false"
                >
                    <ItemButton
                        v-click-log="{
                            action: 'OP_ACTIVITY_ICON_BUTTON_MORE',
                            params: {
                                title: '音效开关',
                            },
                        }"
                        @click.passive.stop
                    >
                        <template #icon><IconMusic /></template>音效开关
                        <template #addon>
                            <ToggleSwitch :show="!globalSwitch" @toggle="toggleAudioMute" />
                        </template>
                    </ItemButton>
                    <!-- <ItemButton
                        v-click-log="{
                            action: 'OP_ACTIVITY_ICON_BUTTON_MORE',
                            params: {
                                title: '活动规则',
                            },
                        }"
                        @click="goToPage(kconfUEConfig.activityRuleUrl)"
                    >
                        <template #icon><IconRule /></template>活动规则
                    </ItemButton> -->
                    <ItemButton
                        v-click-log="{
                            action: 'OP_ACTIVITY_ICON_BUTTON_MORE',
                            params: {
                                title: '联系客服',
                            },
                        }"
                        @click="goToPage(kconfUEConfig.customerServiceUrl)"
                    >
                        <template #icon><IconService /></template>联系客服
                    </ItemButton>
                    <!-- <ItemButton @click="scan">
                        <template #icon><IconScan /></template>
                        <span> 扫一扫 </span>
                    </ItemButton> -->
                </Popover>
            </div>
            <IconWidget
                v-click-log="{
                    action: 'OP_ACTIVITY_ICON_BUTTON',
                    params: {
                        title: '规则',
                    },
                }"
                type="rule"
                @click="goToPage(kconfUEConfig.activityRuleUrl)"
            />
        </template>
    </StatusBar>
</template>

<style lang="scss" scoped>
.status-bar {
    width: 100%;
    --adapt-status-bar-main-title-height: 34px;
    /* 挂件导航标题间的间距 */
    --adapt-status-bar-main-title-gap: 6px;
    .title {
        font-family: KuaiYuanHuiTi;
        font-weight: 400;
        font-size: 34px;
        line-height: 34px;
        letter-spacing: 0;
        text-align: center;
        vertical-align: bottom;
        color: #fff;
    }
    .sub-title {
        height: 100%;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        line-height: 100%;
        letter-spacing: 0;
        text-align: center;
        color: rgba(255, 255, 255, 0.6);
    }
    .empty-subtitle {
        height: 13px;
    }
    .more-wrapper {
        position: relative;
        .more-btn {
            pointer-events: none;
        }
        .MorePop {
            --adapt-tool-tip-inner-padding: 0;
            width: 166px;
            left: -2px;
            top: 49px;
        }
    }
    .wallet-area {
        position: relative;
        width: 36px;
        height: 36px;
        .wallet-icon {
            position: absolute !important;
            top: 0;
            left: 0;
        }
        .wallet-hidden {
            animation: wallet-hidden-ani 500ms 20ms linear forwards;
        }
    }
}
@keyframes wallet-hidden-ani {
    0% {
        opacity: 1;
    }
    21.4% {
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}
</style>
