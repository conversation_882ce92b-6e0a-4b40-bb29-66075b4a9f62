<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import Header from '@pet/adapt.heading/index.vue';
import Logo from '@pet/adapt.logo/index.vue';
import Popup from '@pet/adapt.popup/index.vue';
import AdaptTransition from '@pet/adapt.transition/index.vue';

import { useLogger } from '@/init/logger';
import { useSelectProductModel } from '@/models/selectProductModel';
import type { _INTERNAL_SUPERCLASS_AbstractPrizeDetailView } from '@/services/open-api-docs/home/<USER>/schemas';

import { PAY_ACCOUNT_TYPE, payAccountTypeName } from '../direct-pay-layer/type';

const props = defineProps<{
    /** logo图片链接 */
    sponsorLogo?: string;
    /** logo降级文案 */
    sponsorText?: string;
    title?: string; // 打卡100天
    subTitle?: string; // 打卡100天必得现金
    /** SummerCommonButtonView */
    mainButton?: {
        /** 按钮文字 */
        linkText: string;
    };
    /** 奖励 类型应该总是 LLCH_TRANSFER_CARD */
    llpeDetail: _INTERNAL_SUPERCLASS_AbstractPrizeDetailView & {
        /** 描述: 现金打款0.50元 */
        desc?: string;
        /** 转账备注 */
        payAccountName: string;
        /** 提示文案，只有在提现失败时会有 */
        toast?: string | null | undefined;
        /** 支付状态的文案描述 */
        paymentStatusDes?: string | null | undefined;
    };
    /** 自动关闭时间，单位毫秒， 为零不自动关闭 */
    autoCloseTime?: number;
}>();
const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'end'): void;
}>();
const { sendShow, sendClick } = useLogger();
const { setChoiceProductAnim } = useSelectProductModel();

sendShow('OP_ACTIVITY_NEW_GUID', { type: 'pop' });

const onMainClick = () => {
    sendClick('OP_ACTIVITY_NEW_GUID', { type: 'pop' });
    setChoiceProductAnim(true);
    emits('close');
};

const onCloseClick = () => {
    setChoiceProductAnim(true);
};

const headerFooterTransInfo = computed(() => ({
    name: 'fade' as const,
    delay: { enter: 200 },
    duration: { enter: 133 },
}));

if (props.autoCloseTime) {
    onMounted(() => {
        const timer = setTimeout(() => {
            setChoiceProductAnim(true);
            emits('close');
        }, props.autoCloseTime);

        onUnmounted(() => {
            clearTimeout(timer);
        });
    });
}
</script>

<template>
    <Popup
        class="DirectPayPopup"
        ani-type="pop-25cny"
        :enter-duration="533"
        fly-to-target="progress-view"
        @close="onCloseClick"
        @afterLeave="emits('end')"
    >
        <template #addons>
            <AdaptTransition
                appear
                :name="headerFooterTransInfo.name"
                :delay="headerFooterTransInfo.delay"
                :duration="headerFooterTransInfo.duration"
            >
                <div>
                    <Logo v-if="sponsorLogo" :src="sponsorLogo" :brand-logo-degrade-text="sponsorText" />
                    <Header class="header" :title="title" />
                </div>
            </AdaptTransition>
        </template>
        <div class="container">
            <div class="title">
                <div class="pay_watermark">
                    <svg width="103" height="86" viewBox="0 0 103 86" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            opacity="0.1"
                            d="M41.1187 49.5045C40.5995 49.7681 40.0678 49.8939 39.4224 49.8939C37.979 49.8939 36.8016 49.1151 36.1431 47.9468L35.8775 47.4324L25.5345 25.0777C25.4079 24.8141 25.4079 24.5631 25.4079 24.2995C25.4079 23.2571 26.1929 22.4783 27.2437 22.4783C27.6363 22.4783 28.0287 22.6039 28.4213 22.8675L40.5995 31.4453C41.5111 31.9597 42.562 32.3493 43.739 32.3493C44.3974 32.3493 45.0433 32.2234 45.7017 31.9597L102.809 6.75476C92.5931 -5.20109 75.7047 -13 56.5762 -13C25.4079 -13 0 7.92287 0 33.7687C0 47.7966 7.59585 60.5306 19.5088 69.1084C20.4199 69.7614 21.0783 70.9296 21.0783 72.0972C21.0783 72.4866 20.9515 72.876 20.8126 73.2655C19.9014 76.7692 18.319 82.4961 18.319 82.7476C18.1921 83.1363 18.0524 83.6515 18.0524 84.1787C18.0524 85.2212 18.8375 86 19.8884 86C20.2808 86 20.6734 85.8743 20.9392 85.6106L33.2441 78.4648C34.1559 77.9496 35.2066 77.5603 36.2575 77.5603C36.7761 77.5603 37.4346 77.686 37.9538 77.8239C43.7137 79.5073 50.0058 80.424 56.4241 80.424C87.5925 80.424 113 59.5011 113 33.6553C113 25.8564 110.646 18.4591 106.582 11.9543L41.5111 49.2407L41.1187 49.5043V49.5045Z"
                            fill="white"
                        />
                    </svg>
                </div>
                <div class="title_desc">
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M7.36153 10.8612C7.29338 10.8959 7.2236 10.9125 7.13889 10.9125C6.94945 10.9125 6.79493 10.8099 6.7085 10.6561L6.67363 10.5883L5.31616 7.64477C5.29954 7.61005 5.29954 7.57701 5.29954 7.5423C5.29954 7.40504 5.40257 7.30248 5.54048 7.30248C5.59201 7.30248 5.64351 7.31903 5.69504 7.35374L7.29338 8.48322C7.41302 8.55096 7.55095 8.60225 7.70542 8.60225C7.79184 8.60225 7.87662 8.58569 7.96303 8.55096L15.4582 5.23208C14.1174 3.65779 11.9008 2.63086 9.39026 2.63086C5.29954 2.63086 1.96484 5.38589 1.96484 8.78916C1.96484 10.6363 2.96177 12.3131 4.5253 13.4425C4.64488 13.5285 4.73129 13.6824 4.73129 13.8361C4.73129 13.8874 4.71466 13.9386 4.69642 13.9899C4.57683 14.4513 4.36914 15.2054 4.36914 15.2385C4.35249 15.2897 4.33416 15.3575 4.33416 15.4269C4.33416 15.5642 4.4372 15.6668 4.57513 15.6668C4.62663 15.6668 4.67816 15.6502 4.71304 15.6155L6.32801 14.6746C6.44767 14.6067 6.58558 14.5555 6.72351 14.5555C6.79157 14.5555 6.878 14.572 6.94615 14.5902C7.7021 14.8118 8.52793 14.9325 9.3703 14.9325C13.461 14.9325 16.7957 12.1775 16.7957 8.77423C16.7957 7.7473 16.4867 6.77326 15.9533 5.91673L7.41302 10.8265L7.36153 10.8612V10.8612Z"
                            fill="white"
                        />
                    </svg>
                    <span>{{ subTitle }}</span>
                </div>
                <div class="amount">
                    <span>{{ llpeDetail.displayAmount }}</span>
                    <span class="unit u-font-family">{{ llpeDetail.displayUnit }}</span>
                </div>
                <div class="toast">{{ llpeDetail.paymentStatusDes }}</div>
            </div>
            <div class="content">
                <div class="info">
                    <div class="channel">
                        <span>转账方式</span>
                        <span>{{ decodeURI(payAccountTypeName[PAY_ACCOUNT_TYPE.WECHAT]) }}支付</span>
                    </div>
                    <div class="divide"></div>
                    <div class="user">
                        <span>转账备注</span>
                        <span>{{ llpeDetail.payAccountName }}</span>
                    </div>
                </div>
                <div class="button">
                    <Button v-if="mainButton" :height="60" class="confirm" @click="onMainClick">
                        {{ mainButton.linkText }}
                    </Button>
                </div>
            </div>
        </div>
    </Popup>
</template>

<style lang="scss" scoped>
.DirectPayPopup {
    --direct-pay-layer-bg: #15ce6d;
    --adapt-popup-close-btn-top-y-value: 26px;

    :deep(.popup-extension) {
        z-index: 2;
        pointer-events: none;
    }

    .header {
        margin-bottom: 24px;
    }

    .container {
        width: 308px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 38px;
        position: relative;
        overflow: hidden;

        .title {
            width: 100%;
            height: 413px;
            background: var(--direct-pay-layer-bg);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;

            .pay_watermark {
                position: absolute;
                top: 0;
                right: 0;
                width: 103px;
                height: 98px;
            }

            .title_desc {
                margin-top: 28px;
                font-weight: 600;
                font-size: 20px;
                height: 20px;
                line-height: 18px;
                color: #ffffff;
                display: flex;
                align-items: center;
                position: relative;

                > span {
                    margin-left: 4px;
                }
            }
        }

        .amount {
            margin-top: 20px;
            font-size: 48px;
            line-height: 60px;
            font-family: 'KuaiYuanHuiTi', sans-serif;
            font-weight: 400;
            position: relative;
            color: #fff;
            display: flex;

            .unit {
                position: absolute;
                font-size: 14px;
                font-weight: 600;
                bottom: 0;
                line-height: 26px;
                right: 0;
                transform: translateX(100%);
            }
        }

        .toast {
            margin-top: 8px;
            color: #ffffff;
            opacity: 0.8;
            font-size: 14px;
            line-height: 22px;
        }
    }

    .content {
        width: 100%;
        position: absolute;
        bottom: 0;
        height: 254px;
        @include bg('./assets/bg.png');
        background-position: 0;
        background-repeat: no-repeat;
        background-size: cover;
        padding: 54px 26px 0;
        box-sizing: border-box;

        .info {
            padding: 0 12px;
            display: flex;
            flex-direction: column;
            font-weight: 400;

            .divide {
                border-style: solid;
                border-color: rgba(156, 156, 156, 1);
                border-width: 0;
                border-top-width: 1px;
                width: 100%;
                opacity: 0.1;
                margin: 9px 0;
            }

            .user,
            .channel {
                display: flex;
                justify-content: space-between;
                color: #222222;
                opacity: 0.75;
                font-size: 16px;
                line-height: 22.4px;
            }
        }

        .button {
            margin-top: 26px;
            display: flex;
            justify-content: center;

            --adapt-dialog-main-button-primary-background-image: linear-gradient(
                172deg,
                #ff7001 0%,
                #fc2d39 50%,
                #f31906 100%
            );

            .confirm {
                --adapt-button-primary-background-color: var(--adapt-dialog-main-button-primary-background-color);
                --adapt-button-primary-background-image: var(--adapt-dialog-main-button-primary-background-image);
                --adapt-button-main-font-family: var(--adapt-dialog-main-button-primary-font-family);
                --adapt-button-primary-font-linear: var(--adapt-dialog-main-button-primary-font-linear);
                --adapt-button-primary-letter-spacing: 1px;
                --adapt-button-line-height: 28px;
                --adapt-button-font-size: 22px;
                --adapt-button-font-weight: 400;
                --adapt-button-padding: 0 29px;
                --adapt-button-height: 66px;
                --adapt-button-width: 190px;

                animation: breath 534ms infinite reverse cubic-bezier(0.33, 0, 0.67, 1);

                @keyframes breath {
                    0% {
                        transform: scale(1);
                    }
                    50% {
                        transform: scale(1.08);
                    }
                    100% {
                        transform: scale(1);
                    }
                }
            }
        }
    }
}
</style>
