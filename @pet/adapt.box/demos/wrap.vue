<pet-info lang="json">
{ "title": "换行", "description": "" }
</pet-info>
<script setup lang="ts">
import Box from '../box.vue';
</script>

<template>
    <div>
        <Box class="box" wrap>
            <img
                src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                class="img"
            />
            <img
                src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                class="img"
            />
            <img
                src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                class="img"
            />
            <img
                src="https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg"
                class="img"
            />
        </Box>
    </div>
</template>

<style lang="scss" scoped>
.box {
    width: 200px;
    height: 200px;
    background: #f5f5f5;
    position: relative;
    &::before {
        position: absolute;
        top: 0;
        left: 0;
        content: 'flex-box';
        background: #999;
        color: #fff;
        font-size: 12px;
        padding: 2px 4px;
        border-radius: 0 0 5px 0;
    }
    & > img {
        position: relative;
        width: 100px;
        height: 100px;
    }
}
</style>
