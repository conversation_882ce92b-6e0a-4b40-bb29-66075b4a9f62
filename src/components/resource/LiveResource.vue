<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import { computed, ref } from 'vue';

const props = withDefaults(
    defineProps<{
        source: string;
        sourceSize?: string;
        textIcon?: string;
        liveStreamId: string;
        innerCircleWidth?: string;
        outerCircleWidth?: string;
    }>(),
    {
        sourceSize: '48px',
        innerCircleWidth: '2px',
        outerCircleWidth: '1.5px',
        textIcon: '',
    },
);
const { effectShowStatus } = useDowngradeLevel();
const hasError = ref(!props.source);

const wrapStyle = computed(() => ({
    width: props.sourceSize,
    height: props.sourceSize,
}));

const sourceStyle = computed(() => {
    return {
        width: props.sourceSize,
        height: props.sourceSize,
        borderRadius: '50%',
    };
});

const onError = () => {
    hasError.value = true;
};
</script>

<template>
    <div class="source-wrap" :style="wrapStyle">
        <img
            :src="source"
            :class="{ 'source-living-anim': !!liveStreamId && effectShowStatus.L3 }"
            :style="sourceStyle"
            @error="onError"
        />
        <div v-if="liveStreamId" class="living-circle" :style="{ borderWidth: innerCircleWidth }"></div>
        <div
            v-if="liveStreamId"
            class="living-circle"
            :class="{ 'outer-living-circle': effectShowStatus.L3 }"
            :style="{ borderWidth: outerCircleWidth }"
        ></div>
        <img v-if="!!textIcon" class="live-text" :src="textIcon" alt="直播中" />
    </div>
</template>

<style lang="scss" scoped>
.source-wrap {
    display: flex; //这个属性并非多余属性，如果祖先容器中用了flex布局，这里不设置flex布局，可能会导致高度异常或内部元素布局异常
    position: relative;
    padding: 0.5px;
    width: 56px;
    height: 56px;
}

.living-circle {
    border-style: solid;
    border-color: #fe3666;
    border-radius: 50%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.outer-living-circle {
    animation:
        outer_living_circle_opacity 1s infinite,
        outer_living_circle_scale 1s infinite;
}

.source-living-anim {
    animation: living_source_scale 1s infinite;
}

.live-text {
    position: absolute;
    width: 44px;
    height: 18px;
    bottom: -4px;
    border-radius: 20px;
    left: 0;
    right: 0;
    margin: 0 auto;
}

@keyframes living_source_scale {
    0% {
        transform: scale(1);
    }
    56% {
        transform: scale(0.88);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes outer_living_circle_scale {
    10% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.2);
    }
}

@keyframes outer_living_circle_opacity {
    40% {
        opacity: 0.8;
    }
    100% {
        opacity: 0;
    }
}
</style>
