<pet-info lang="json">
{ "title": "卡券配置", "description": "自定义卡券", "priority": 7 }
</pet-info>
<script setup lang="ts">
import Coupon from '../index.vue';
import type { PlatformCouponData } from '../types';

const displayTypeNumber: PlatformCouponData = {
    displayType: 'number',
    branding: false,
    name: '日常测试',
    icon: '',
    desc: '全平台可用（除少数商品外）',
    forwardUrl:
        'https://app.kwaixiaodian.com/page/kwaishop-pub-couponuse/v20211111?couponTargetName=平台通用券&layoutType=4&enableWK=1&__launch_options__=%7B%22enableLoading%22%3Atrue%2C%22enableProcess%22%3Afalse%7D&breakupTime=0&webview_bgcolor=%23f80031&breakupTime=0&hyId=couponus&entry_src=cnycoupongezi&source=cnycoupongezi&couponId=6201481043&couponTargetType=3&doorPrice=1000&reducePrice=100&couponStartTime=1660201848196&couponEndTime=1660205448196&couponSourceType=1',
    forwardText: '立即使用',
    status: 'unused',
    value: 999,
    valueText: '1',
    valueDesc: '满10元可用',
    expiredTime: 1660205448196,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeDiscount: PlatformCouponData = {
    displayType: 'discount',
    branding: false,
    name: '优惠券',
    icon: 'https://kcdn.staging.kuaishou.com/kos/nlav100106/encourage-1658904825620-pvIssv.jpg',
    desc: '仅限新客使用',
    forwardUrl: 'https://www.baidu.com/zzzzzzzzzz',
    forwardText: '立即使用',
    status: 'unused',
    value: 5.0,
    valueText: '5.0',
    valueDesc: '无门槛',
    expiredTime: 1661928815415,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeImage: PlatformCouponData = {
    displayType: 'image',
    branding: false,
    name: '直播勋章',
    desc: '五一活动直播勋章',
    forwardUrl:
        'kwai://liveaggregatesquare?liveSquareSource=1&sourceType=269&liveSource=HOMEPAGE_51_ACTIVITY_PAGE_2&internaljump=kwailive://giftpanel?tab=PropsPanel&sourceType=h5',
    forwardText: '去佩戴',
    status: 'unused',
    value: 0.0,
    image: 'http://blobstore-nginx.staging.kuaishou.com/bs2/zt-encourage-prize/encourage-1659064164269-oifvCN.png',
    expiredTime: 1665648351770,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeText: PlatformCouponData = {
    displayType: 'text',
    branding: false,
    name: '优惠券',
    icon: 'https://kcdn.staging.kuaishou.com/kos/nlav100106/encourage-1658904825620-pvIssv.jpg',
    desc: '仅限电商新客首单使用',
    forwardUrl: 'https://www.baidu.com/zzzzzzzzzz',
    forwardText: '立即使用',
    status: 'unused',
    value: 0.0,
    valueText: '首单免减',
    valueDesc: '满100可用',
    expiredTime: 1661928815415,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

function jumpUrl(url: string) {
    if (url.startsWith('http')) {
        console.log('yoda open:', url);
    } else {
        console.log('window open:', url);
    }
}
</script>

<template>
    <div class="wrapper">
        <div class="demo">
            <b>卡券标签</b>
            <Coupon :info="displayTypeImage" tag="平台券"></Coupon>
            <b>隐藏按钮</b>
            <Coupon :info="displayTypeImage" size="mini" :show-use-btn="false" click-all></Coupon>
            <b>按钮文案</b>
            <Coupon :info="displayTypeImage" btn-text="去领取"></Coupon>
            <b>按钮样式</b>
            <Coupon :info="displayTypeImage" btn-text="去使用" btn-type="plain"></Coupon>
            <b>按钮禁用</b>
            <Coupon :info="displayTypeImage" btn-disabled btn-text="审核中"></Coupon>
            <b>按钮加载</b>
            <Coupon :info="displayTypeImage" btn-loading></Coupon>
            <b>跳转方法</b>
            <Coupon :info="displayTypeImage" btn-text="去佩戴" :jump-fn="jumpUrl"></Coupon>
        </div>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    width: 100%;
    justify-content: center;
    background: #f5f5f5;
}

.demo {
    padding: 10px 0;
    b {
        display: block;
        margin: 10px 0;
    }
}
</style>
