
import { createShareEventPlugin, ShareErrorCode, type ShareOptions,type ShareParam, ShareError } from '@ks-share/share-utils';
import { addListener, removeListener, invoke, isBridgeError } from '@yoda/bridge';

export type InsertUserInfo = {
    user_id: number;
    user_name: string;
    headurl: string;
}

const shareEventPlugin = createShareEventPlugin(addListener, removeListener);

export type ExtendShareParam = ShareParam & {
    insertUserInfos?: InsertUserInfo[];
}

function getExtendShareParam(param: ExtendShareParam) {
    const { subBiz, shareObjectId = 'default_id', logExt = {}, placeholder = {}, insertUserInfos = [] } = param;
    const logExtStr = JSON.stringify(logExt);
    return {
        param: {
            subBiz,
            shareObjectId,
            showSharePanel: true,
            commonConfigs: {
                logExt: logExtStr,
            },
            tokenStoreParams: placeholder,
            shareInitConfigs: {
                extTransientParams: {
                    logExt: logExtStr,
                },
                extTokenStoreParams: placeholder,
            },
            imShareUsersInfo: {
                userList: insertUserInfos,
            }
        },
    }
}

export async function imHeadsInsertNormalShare(
    params: ExtendShareParam,
    options: ShareOptions,
) {
    // TODO @zhangyashan app版本判断

    // 参数转换
    const extendShareParam = getExtendShareParam(params);
   
    // 事件监听
    await shareEventPlugin(options);

    try {
        
        return await invoke('social.share', extendShareParam);

    } catch (error) {
        if (isBridgeError(error)) {
            const { message, code, context } = error;

            if (code !== 0) {
                // return {
                //     code: ShareErrorCode.BRIDGE_ERROR,
                //     message: `social.share Bridge Error code: ${code}, message: ${message}`,
                //     context,
                // }
                throw new ShareError(message, code, context);
            }

            return {
                code,
                message: 'Share Cancel',
            };
        }

        return {};
    }

}