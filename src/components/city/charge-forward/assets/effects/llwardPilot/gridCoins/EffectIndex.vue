<script setup lang="ts">
/**
 * pet组件使用示例。带动效平台导出尺寸，可直接使用
 * 动效预览：https://vision.corp.kuaishou.com/effect/8318
 *
 * 依赖pet组件 effect-item@^0.4.11
 * 更多组件使用方式、功能见：
 * https://pet-web.corp.kuaishou.com/gallery/detail/?name=@pet/vision.effect-item&version=
 */
import EffectItem from '@pet/vision.effect-item/index.vue';
import { preload } from '@vision/runtime';

const emit = defineEmits<{
    (e: 'ended'): void;
}>();
const effectData = import('./config').then((mod) => {
    const appEle = document.getElementById('app');
    const startElement = document.getElementById('ip');
    const endElement = document.getElementById('earn-card-amount');

    if (startElement && endElement && appEle) {
        const appRect = appEle.getBoundingClientRect();
        const start = {
            top: 0,
            left: 0,
        };
        const end = {
            top: 0,
            left: 0,
        };
        // 计算缩放比例,
        const scale = 414 / appRect.width; //414是我们的基准
        const hScale = 896 / appRect.height; //896是我们的基准

        const startRect = startElement.getBoundingClientRect();
        const endRect = endElement.getBoundingClientRect();

        // 计算中心位置并应用缩放
        start.top = (startRect.top - appRect.top) * hScale + 40;
        start.left = (startRect.left - appRect.left) * scale + 30;
        end.top = (endRect.top - appRect.top) * hScale;
        end.left = (endRect.left - appRect.left) * scale;

        const startCenterX = start.left + (startRect.width * scale) / 2;
        const startCenterY = start.top + (startRect.height * hScale) / 2;
        const endCenterX = end.left + (endRect.width * scale) / 2;
        const endCenterY = end.top + (endRect.height * hScale) / 2;

        for (const layer of mod.default.entryData.layers) {
            // 设置起始中心位置
            layer.ks.p.k[0].s[0] = startCenterX * 2;
            layer.ks.p.k[0].s[1] = startCenterY * 2;
            // 设置结束中心位置
            layer.ks.p.k[1].s[0] = endCenterX * 2;
            layer.ks.p.k[1].s[1] = endCenterY * 2;
        }
    }

    return mod.default;
});
</script>

<template>
    <EffectItem class="effect-wrapper" :data="effectData" :loop="false" autoplay @ended="emit('ended')"></EffectItem>
</template>

<style lang="scss" scoped>
.effect-wrapper {
    position: absolute !important;
    width: 414px;
    height: 896px;
    overflow: hidden;
    top: 0;
    left: 0;
    z-index: 999 !important;
}
</style>
