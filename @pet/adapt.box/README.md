# @pet/adapt.box

布局盒子

这是一个可用于快速布局的组件集，其中包括 box 以及 box-item,主要提供了`flex`的基础布局能力。

## Box

布局父容器，默认是一个 flex 盒子

### 基本 flex

```html
<Box justify="center" align="center"></Box>
```

### 布局方向

```html
<Box direction="row" class="box"></Box>
```

### 对齐

```html
<Box justify="center"></Box>

<Box align="center"></Box>
```

### 换行

```html
<Box wrap></Box>
```

### 子组件

```html
<Box :gap-row="40" :gap-col="80" wrap :items="3">
    <BoxItem v-for="n in 6" :key="n">
        <div class="box-item">{{ n }}</div>
    </BoxItem>
</Box>
```

## 属性

| 名称      | 类型                                                              | 默认值  | 说明                                                         |
| --------- | ----------------------------------------------------------------- | ------- | ------------------------------------------------------------ |
| tag       | string                                                            | div     | 渲染 dom 节点的 tagName                                      |
| wrap      | boolean                                                           | false   | 折行展示子元素                                               |
| direction | row \| col \| row-rev \| col-rev                                  | row     | row:横向<br>col:纵向<br>row-rev:横向反转<br>col-rev:纵向反转 |
| align     | start \| end \| center \| baseline \| stretch                     | stretch | 对应 align-items 属性                                        |
| justify   | start \| end \| center \| baseline \| between \| around \| evenly | stretch | 对应 justify-content 属性                                    |
| inline    | boolean                                                           | false   | 是否为行内元素                                               |
| classic   | boolean                                                           | false   | display 切换为 block                                         |
| items     | number                                                            | 0       | 将主轴分为几栏                                               |
| gap-row   | number                                                            | 0       | row 方向子元素间距                                           |
| gap-col   | number                                                            | 0       | colum 方向子元素间距                                         |

## 插槽

| 名称    | 说明     |
| ------- | -------- |
| default | 默认插槽 |

## BoxItem

box-item 默认，为`flex: 1 1 0%`的子元素

| 名称   | 类型             | 默认值    | 说明                    |
| ------ | ---------------- | --------- | ----------------------- |
| tag    | string           | div       | 渲染 dom 节点的 tagName |
| order  | number           | undefined | 子元素的顺序            |
| basis  | number \| string | undefined | 所分轴的长度            |
| grow   | number \| string | undefined | flex-grow               |
| shrink | number           | undefined | flex-shrink             |
| align  | string           | undefined | flex-align              |

## 插槽

| 名称    | 说明     |
| ------- | -------- |
| default | 默认插槽 |
