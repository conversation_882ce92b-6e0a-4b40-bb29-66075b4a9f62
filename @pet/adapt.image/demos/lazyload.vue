<script setup lang="ts">
import { ref, computed } from 'vue-demi';

import XImage from '../index.vue';

const imgSrc = ref(
    'https://h1.static.yximgs.com/kos/nlav10721/chrome-plugin-upload/2023-07-20/1689828764017.0c23a1b4aa448f60.png',
);

// const imgSrcSlow = computed(() => `https://deelay.me/2000/${imgSrc.value}`);
</script>

<template>
    <div class="demo">
        <div class="container is-scroll">
            <h5>向下滚动, 查看请求</h5>
            <XImage :src="imgSrc" class="image-item" loading="lazy" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
}
.image-item {
    width: 64px;
    height: 64px;
}
.is-scroll {
    width: 300px;
    max-height: 200px;
    overflow: auto;
    h5 {
        margin-bottom: 400px;
    }
}
div {
    font-size: 16px;
}
</style>
