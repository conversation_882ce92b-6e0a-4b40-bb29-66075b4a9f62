<pet-info lang="json">
{ "title": "自定义扩展", "description": "通过插槽实现自定义" }
</pet-info>
<script lang="ts" setup>
import Card from '../index.vue';
const userInfo = {
    avatar: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '快手',
    desc: '7月23日 15:56',
    attachedText: '6.99元',
};
</script>

<template>
    <div class="card-wrapper">
        <Card class="card" v-bind="userInfo">
            <template #ctrl>
                <div>
                    <b class="num">+50.2</b><b class="num">元</b>
                    <div class="desc">审核中</div>
                </div>
            </template>
        </Card>
    </div>
</template>

<style scoped>
.card-wrapper {
    width: 100%;
    background: #f5f5f5;
    padding: 10px 0;
}
.card {
    --adapt-card-background: none;
}

.rank {
    font-family: 'Avenir';
    font-style: italic;
    font-weight: 900;
    font-size: 16px;
    line-height: 18px;
    display: block;
    width: 28px;
    text-align: center;
    margin-left: -4px;
    margin-right: 12px;
}

.num {
    color: #ff1d50;
    font-size: 16px;
    line-height: 22px;
}

.num + .num {
    margin-left: 3px;
}
.desc {
    margin-top: 4px;
    text-align: right;
    color: #fe3666;
    font-size: 12px;
    font-weight: 400;
    line-height: normal;
    opacity: 0.6;
}
</style>
