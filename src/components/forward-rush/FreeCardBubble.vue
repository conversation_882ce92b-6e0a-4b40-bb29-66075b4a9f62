<script setup lang="ts">
import Picture from '@pet/quantum.PictureImg';

import signFreeIcon from '@/assets/forward-rush/bubble-sign-free-icon.png?preset=modern';

defineProps<{ mainText: string; subText: string }>();
</script>
<template>
    <div class="free-bubble common-bg flex-row-center">
        <Picture class="bubble-sign-free-icon" :src="signFreeIcon" alt="" />
        <div class="bubble-sign-free-text">
            <span class="bubble-text">{{ mainText }}</span>
            <span class="bubble-text bubble-text-space">{{ subText }}</span>
        </div>
    </div>
</template>
<style lang="scss" scoped>
@use './bubble.scss';
.free-bubble {
    width: 204px;
    height: 96px;
    @include bg('@/assets/forward-rush/bubble-bg.png');
    .bubble-sign-free-icon {
        width: 45px;
        height: 44.5px;
    }
    .bubble-sign-free-text {
        margin-left: 4px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
}
</style>
