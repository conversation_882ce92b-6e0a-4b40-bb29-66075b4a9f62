<pet-info lang="json">
{
    "title": "数据更新",
    "description": "接口数据发生变化"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import { MarqueeSlide } from '../index';

const messageListChange = ref([
    { name: '原始数据', tag: '1' },
    { name: '原始数据', tag: '2' },
    { name: '原始数据', tag: '3' },
]);
const CHANGE_DATA = [
    { name: '更新数据', tag: '-11' },
    { name: '更新数据', tag: '-22' },
    { name: '更新数据', tag: '-33' },
];

let count = 0;

function addMarqueeData() {
    count++;
    messageListChange.value = [...messageListChange.value, { name: `新建数据`, tag: `${count}` }];
}

function minusMarqueeData() {
    count--;
    messageListChange.value = messageListChange.value.slice(0, -1);
}

function changeData() {
    messageListChange.value = CHANGE_DATA;
}

// setTimeout(() => {
//     changeData()
// }, 12500);
function handleUP(val: number) {
    console.log('val', val);
    // if (val === messageListChange.value.length - 1) {
    //     changeData();
    // }
}
</script>

<template>
    <div class="container">
        <MarqueeSlide :list="messageListChange" class="marquee-box" :animation-duration="1000" @stop="handleUP">
            <template #item="{ data }">
                <div class="line">data: {{ data.name }} ^_^ {{ data.tag }}</div>
            </template>
        </MarqueeSlide>
        <pre class="code-box">{{ messageListChange }}</pre>
        <button class="btn" @click="addMarqueeData">增加一条数据</button>
        <div></div>
        <button class="btn" @click="minusMarqueeData">减去一条数据</button>
        <div></div>
        <button class="btn" @click="changeData">数据更新</button>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.marquee-box {
    font-size: 16px;
    width: 300px;
    height: 24px;
    margin-bottom: 10px;
}
.code-box {
    font-size: 14px;
    color: #008000;
}
.btn {
    background-color: red;
    margin-bottom: 10px;
}
</style>
