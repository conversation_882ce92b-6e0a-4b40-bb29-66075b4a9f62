<pet-info lang="json">
{
    "title": "事件",
    "description": "对外抛出的事件"
}
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';

import { MarqueeSlide } from '../index';

const messageList = ['获得1000枚金币', '获得2000积分', '获得100元'];
const currentIndex = ref(0);

function handleStep(val: number) {
    currentIndex.value = val;
}
</script>

<template>
    <div class="container">
        <div>每次@stop增加一次计数</div>
        <div class="info">当前处于：{{ currentIndex }}</div>
        <MarqueeSlide :list="messageList" class="marquee-box" @stop="handleStep">
            <template #item="{ data, index }">
                <div class="row">
                    <div class="line">
                        <b>index: {{ index }}</b> data: {{ data }}
                    </div>
                </div>
            </template>
        </MarqueeSlide>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.marquee-box {
    font-size: 14px;
    width: 300px;
    height: 20px;
    margin-bottom: 10px;
}
.info {
    color: #008000;
    font-size: 16px;
}
</style>
