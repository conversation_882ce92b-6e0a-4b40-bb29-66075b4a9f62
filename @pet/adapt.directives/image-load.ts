import { retryLoadImage, bindObserver, unbindObserver } from '@pet/yau.core/helper';
import type { App, DirectiveBinding, ObjectDirective } from 'vue';

type ImageOptions = {
    src: string;
    fallbackSrc?: string;
    retryTimes?: number;
    onLoad?: () => void;
    onError?: () => void;
    loading?: 'lazy' | 'eager';
    loadingCtrl?: 'self' | 'browser';
    rootMargin?: string;
};

const emptyImage =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mN89B8AAskB44g04okAAAAASUVORK5CYII=';

function load(el: HTMLImageElement, options: ImageOptions) {
    const imgElm = el;
    const {
        src,
        fallbackSrc = emptyImage,
        retryTimes = 1,
        onLoad,
        onError,
        loading,
        loadingCtrl = 'browser',
        rootMargin = '0px 50% 50% 0px',
    } = options;
    if ('loading' in HTMLImageElement.prototype && loadingCtrl !== 'self') {
        if (Boolean(loading)) {
            imgElm.loading = loading!;
        }
        retryLoadImage(imgElm, src, fallbackSrc, retryTimes, onLoad, onError);
    } else {
        if (loading === 'lazy') {
            bindObserver(
                imgElm,
                () => {
                    retryLoadImage(imgElm, src, fallbackSrc, retryTimes, onLoad, onError);
                },
                () => {},
                {
                    rootMargin,
                },
            );
        } else {
            retryLoadImage(imgElm, src, fallbackSrc, retryTimes, onLoad, onError);
        }
    }
}

export const imageLoadRetry: ObjectDirective<any, ImageOptions> = {
    created(el: HTMLImageElement, binding: DirectiveBinding) {
        load(el, binding.value);
    },
    updated(el: HTMLImageElement, binding: DirectiveBinding) {
        load(el, binding.value);
    },
    beforeUnmount(el: HTMLImageElement, binding: DirectiveBinding) {
        const { loading } = binding.value;
        if (!('loading' in HTMLImageElement.prototype) && loading === 'lazy') {
            unbindObserver(el);
        }
    },
};

export const vImage = imageLoadRetry;

export default {
    install: (Vue: App) => {
        Vue.directive('v-image', imageLoadRetry as any);
    },
};
