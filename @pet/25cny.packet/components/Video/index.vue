<script setup lang="ts">
import Player from './player/index.vue';
import { PlayerType } from '@ks-kplayer/vue';
import { getLocalWarmupResult } from '@pet/yau.yoda/utils';
import { onMounted, ref, computed } from 'vue';
import { popupMuted } from './muted';
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import { useCdnLevel } from '@pet/25cny.cdn-image/useCdnLevel';

export interface VideoParams {
    prizeType: 'video';
    poster: string;
    warmupSource: string;
    // 传入 string
    source: string;
    videoH264CdnUrl?: string;
}

const props = defineProps<VideoParams>();
const emits = defineEmits<{
    (event: 'videoPlaying', val: boolean): void;
    (event: 'videoEnded', val: boolean): void;
    (event: 'videoError', val: boolean): void;
}>();

const { isVideoDowngrade: videoDowngrade } = useDowngradeLevel();
const { downgradeState } = useCdnLevel();

const isVideoDowngrade = computed(() => videoDowngrade.value || downgradeState.value.P1);

const videoHasError = ref<boolean>(false);
const onError = (): void => {
    emits('videoError', popupMuted.value);
    videoHasError.value = true;
};

const videoUrl = ref<string>('');
const videoMounted = ref<boolean>(false);
const showControls = computed<boolean>(() => {
    return !isVideoDowngrade.value && !videoHasError.value;
});

const mountVideo = () => {
    setTimeout(() => {
        videoMounted.value = true;
    }, 200);
};

onMounted(async () => {
    if (props.warmupSource) {
        const warmupResult = await getLocalWarmupResult(props.warmupSource);
        if (warmupResult) {
            videoUrl.value = props.warmupSource;
            mountVideo();
            return;
        }
    }

    videoUrl.value = props.source;
    mountVideo();
});
</script>

<template>
    <div class="video">
        <Player
            v-if="videoMounted"
            :type="PlayerType.VIDEO"
            :poster="poster"
            :source="videoUrl"
            :fallback-source="props.videoH264CdnUrl"
            :muted=" popupMuted"
            :downgrade="isVideoDowngrade"
            activity-tag="25summer-popup-ad"
            @playing="emits('videoPlaying', popupMuted)"
            @ended="emits('videoEnded', popupMuted)"
            @error="onError"
        >
            <template v-if="showControls" #controls>
                <div class="video-controls" @click="popupMuted = !popupMuted">
                    <img v-show="popupMuted" src="./assets/images/sound-off.png" alt="" />
                    <img v-show="!popupMuted" src="./assets/images/sound-on.png" alt="" />
                </div>
            </template>
        </Player>
    </div>
</template>

<style lang="scss" scoped>
.video {
    margin-bottom: -27px;
    width: 228px;
    height: 125px;
    border-radius: 14px;
    overflow: hidden;

    &-controls {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        color: white;

        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
