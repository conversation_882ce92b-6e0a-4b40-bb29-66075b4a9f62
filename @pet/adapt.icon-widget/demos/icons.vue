<pet-info lang="json">
{ "title": "基础", "description": "" }
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';
import IconWidget from '../index.vue';
import type { WidgetType } from '../types';

const iconWidgetType = ref('scan');

function handleClick(type: WidgetType) {
    iconWidgetType.value = type;
}
</script>

<template>
    <div>
        <h4>基础组件</h4>
        <h4>点击 {{ iconWidgetType }}</h4>
        <div class="icons-container">
            <IconWidget type="scan" @click="handleClick('scan')"> </IconWidget>

            <IconWidget
                type="scan-icon"
                :scan-config="{
                    targetHandlerActionList: ['KwaiCnyToken'],
                }"
                scan-target="_self"
            />
            <IconWidget type="back" @click="handleClick('back')" />

            <IconWidget type="wallet" @click="handleClick('wallet')" />
            <IconWidget type="share" @click="handleClick('share')" />
            <IconWidget type="rule" @click="handleClick('rule')" />
            <IconWidget type="more" @click="handleClick('more')" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
h4 {
    font-size: 18px;
    line-height: 28px;
    margin: 16px 0;
}
.icons-container {
    display: flex;
}
</style>
