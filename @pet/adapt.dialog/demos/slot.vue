<pet-info lang="json">
{ "title": "扩展插槽", "description": "" }
</pet-info>
<script setup lang="ts">
import AdaptDialog from '../index.vue';
import Button from '@pet/adapt.button/index.vue';
</script>

<template>
    <div class="demo-wrapper">
        <AdaptDialog class="static dialog-all demo-dialog" show>
            <template #header><div class="slot header">弹窗外部顶部区域插槽</div></template>
            <template #inheader><div class="slot inner-header">这是标题处插槽</div></template>
            <template #icon><div class="slot">icon位置插槽,icon高默认150px</div> </template>
            <div class="demo-content">这是默认位置插槽</div>
            <template #btn><Button>按钮插槽</Button></template>
            <template #plus><div class="slot">这是按钮下方plugin处插槽</div></template>
        </AdaptDialog>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.slot {
    display: flex;
    align-items: center;
    justify-content: center;
}
.header {
    height: 40px;
    background-color: #f2f2f2;
}
</style>
