<script setup lang="ts">
interface Props {}
const props = withDefaults(defineProps<Props>(), {});
</script>

<template>
    <div class="homepage">
        <h3>
            <img src="https://s2-111422.kwimgs.com/kos/nlav111422/pc-vision/img/new-logo.79c3bfe1.svg" alt="kuaishou" />
            HomePage
        </h3>
    </div>
</template>

<style lang="scss" scoped>
.homepage {
    font-size: 36px;
    font-weight: bold;
    padding-top: 200px;
    text-align: center;

    h3 {
        background-color: #222;
        color: #fff;
        font-size: 28px;
        img {
            vertical-align: -4px;
        }
    }
}
</style>
