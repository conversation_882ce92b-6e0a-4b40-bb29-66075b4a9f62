<script lang="ts">
export default {
    name: 'AdaptMarquee',
};
</script>

<script lang="ts" setup>
import { computed } from 'vue-demi';
import type { Direction } from './types';
interface MarqueeProps {
    /**
     * 方向
     */
    direction?: Direction;
    /**
     * 执行时长
     */
    duration?: number;
    /**
     * 是否循环
     */
    loop?: boolean;
    /**
     * 暂停
     */
    paused?: boolean;
    /**
     * 反向
     */
    reverse?: boolean;
    /**
     * 边缘淡出遮罩
     */
    mask?: boolean;
}

const props = withDefaults(defineProps<MarqueeProps>(), {
    direction: 'row',
    duration: 3000,
    loop: true,
    paused: false,
    reverse: false,
    mask: true,
});

const CONTENT_REPEAT = 2;
const marqueeItemClass = computed(() => [
    'marquee-item',
    `marquee-item-${props.direction}`,
    props.loop ? 'marquee-loop' : undefined,
]);
const marqueeItemStyle = computed(() => ({
    animationDuration: `${props.duration * CONTENT_REPEAT}ms`,
    animationDirection: props.reverse ? 'reverse' : undefined,
}));
const marqueeWrapperClass = computed(() => ['marquee-wrap', props.mask && `marquee-wrap-${props.direction}-mask`]);
const marqueeContentClass = computed(() => ['marquee-content', `marquee-content-${props.direction}`]);
</script>

<template>
    <div :class="marqueeWrapperClass">
        <div :class="marqueeContentClass">
            <div v-for="n in CONTENT_REPEAT" :key="n" :class="marqueeItemClass" :style="marqueeItemStyle">
                <slot />
            </div>
        </div>
    </div>
</template>

<style>
:root {
    /* 横向跑马灯遮罩 */
    --adapt-marquee-mask-row: linear-gradient(to right, rgba(0, 0, 0, 0), #000 5%, #000 95%, rgba(0, 0, 0, 0) 100%);
    /* 纵向跑马灯遮罩 */
    --adapt-marquee-mask-col: linear-gradient(to bottom, rgba(0, 0, 0, 0), #000 10%, #000 90%, rgba(0, 0, 0, 0) 100%);
}
</style>

<style lang="scss" scoped>
.marquee-wrap {
    width: 100%;
    overflow: hidden;
}
.marquee-wrap-row-mask {
    -webkit-mask-image: var(--adapt-marquee-mask-row);
}
.marquee-wrap-col-mask {
    -webkit-mask-image: var(--adapt-marquee-mask-col);
}
.marquee-content {
    display: flex;
}
.marquee-content-row {
    flex-direction: row;
}
.marquee-content-col {
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}
.marquee-item {
    white-space: nowrap;
    animation-timing-function: linear;
}
.marquee-item-col {
    width: 100%;
    animation-name: an-marquee-col;
}
.marquee-item-row {
    display: flex;
    animation-name: an-marquee-row;
}
.marquee-loop {
    animation-iteration-count: infinite;
}
.marquee-group {
    flex: 1;
}
.marquee-paused .marquee-item {
    animation-play-state: paused !important;
}

@keyframes an-marquee-row {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(-100%, 0);
    }
}

@keyframes an-marquee-col {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(0, -100%);
    }
}
</style>
