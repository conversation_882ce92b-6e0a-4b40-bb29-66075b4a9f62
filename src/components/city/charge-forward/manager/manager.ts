import { sleep } from '@pet/yau.core';
import { ref, type Ref, reactive, type Reactive } from 'vue';

import { setLocalMapConfig } from '@/utils/ssg';

import {
    BuildingSideEnum,
    CameraHeight,
    CameraRotate,
    GridllwardType,
    IPAfterJumpTime,
    IPBeforeJumpTime,
    IPConfig,
    IPMoveDirection,
    IPMoveTime,
    BackBtnConfig,
    MidIconConfig,
    type GridllwardConfig,
    type MapConfig,
    type RenderBuildingType,
    type RenderMapType,
    type RenderStationType,
    type StationConfig,
    type SkinConfig,
    GridShape,
} from './config';
import { RenderZIndex, MapKeyType } from './constant';

export class ChargeForwardManager {
    mapConfig: MapConfig;
    private gridllwardConfig: GridllwardConfig;
    private stationConfig: StationConfig;
    private gridSkinConfig: SkinConfig;

    isMoving: Ref<boolean> = ref(false);
    isIPHigher = true;
    renderMap: Reactive<RenderMapType> = reactive({
        background: {
            offset: 0, // 背景偏移量
        },
        popover: {
            offset: 0, // 气泡偏移量
        },
        grids: [], // 奖励格子
        buildings: [], // 建筑
        stations: [], // 站点格子
        skins: [], // 格子商业化贴皮
        ip: {
            onStation: false, // 是否在站点上
            onBirth: false, // 是否在出生点
            llwardGridName: null, // 是否在奖励格子上
            // 人
            position: {
                x: 0,
                y: 0,
            },
        },
        backBtn: {
            position: {
                x: 0,
                y: 0,
            },
        },
        midIcon: {
            position: {
                x: 0,
                y: 0,
            },
        },
        currentGrid: {
            position: {
                x: 0,
                y: 0,
            },
            shape: GridShape.Square,
            step: 0,
        },
    });
    private signedStep: number[] = []; // 站点对应的格子

    private currentStep = 0; // 当前步数

    // ip在格子间移动的比例
    private ipOffset = 0;
    // 开始移动的时间
    private startMoveTime = 0;
    // 移动方向
    private moveDirection = IPMoveDirection.Forward;

    // 相机位置(z轴)
    private cameraPosition = 0;
    // ip的配置信息
    private ipConfig: typeof IPConfig = JSON.parse(JSON.stringify(IPConfig));
    // 返回主会场按钮配置信息
    private backBtnConfig: typeof BackBtnConfig = JSON.parse(JSON.stringify(BackBtnConfig));
    // 地图中央icon配置信息
    private midIconConfig: typeof MidIconConfig = JSON.parse(JSON.stringify(MidIconConfig));
    // 视图大小
    private viewSize: {
        width: number;
        height: number;
    };

    private moveTime = IPMoveTime;
    private viewBottomStep = 0; // ip以下的格子数量
    private moveSnapShot = {
        start: { x: 0, y: 0 },
        target: { x: 0, y: 0 },
        offset: {
            x: 0,
            y: 0,
        },
    };
    private maskHeight = 0;
    private localPrefix = '';

    private moveEndCallback: (() => void) | undefined = undefined;
    // 地图key
    private mapKey = MapKeyType.Default;
    constructor(config: {
        currentStep: number;
        signedStep: number[];
        mapConfig: MapConfig;
        gridllwardConfig: GridllwardConfig;
        stationConfig: StationConfig;
        gridSkinConfig: SkinConfig;
        viewSize: {
            width: number;
            height: number;
        };
        mapKey: string | null | undefined;
        localPrefix: string;
    }) {
        this.mapConfig = config.mapConfig;
        this.gridllwardConfig = config.gridllwardConfig;
        this.stationConfig = config.stationConfig;
        this.currentStep = config.currentStep;
        this.signedStep = config.signedStep;
        this.gridSkinConfig = config.gridSkinConfig;
        this.viewSize = {
            width: 414,
            height: (414 * config.viewSize.height) / config.viewSize.width,
        };
        // this.maskHeight = this.ui23DPos(this.mapConfig.maskHeight);
        // 小屏适配抬高视角，环形地图固定在地图中央，无需抬高
        // this.ipConfig.birthPos.y += this.ui23DPos(CameraHeight / 2 - this.viewSize.height / 2);

        const cur = this.getGridPosByStep(this.currentStep);
        this.moveSnapShot.start = cur;
        this.moveSnapShot.target = cur;

        this.moveSnapShot.offset = {
            x: this.moveSnapShot.target.x - this.moveSnapShot.start.x,
            y: this.moveSnapShot.target.y - this.moveSnapShot.start.y,
        };
        this.setRenderMap();

        Object.seal(this);
        this.localPrefix = config.localPrefix;
    }

    /**
     * 移动到相邻格子, 此非外部可用接口, 没锁
     * @param direction 1:向前移动, -1:向后移动
     */
    private async moveToNext(direction = IPMoveDirection.Forward, animated: boolean, immediate: boolean) {
        if (this.startMoveTime !== 0) {
            return;
        }
        !immediate && (await sleep(IPBeforeJumpTime));

        this.moveDirection = direction;
        // 计算一下下一步的位置
        this.moveSnapShot.target = this.getGridPosByStep(this.currentStep + 1);

        // console.log('move ToNext', this.moveSnapShot.target);

        let resolver: (value: unknown) => void;
        let rejector: (reason?: any) => void;
        const promise = new Promise((res, rej) => {
            resolver = res;
            rejector = rej;
        });

        this.startMoveTime = Date.now();

        this.frameUpdate(
            () => {
                resolver(undefined);
            },
            {
                animated,
            },
        );

        await promise;
        !immediate && (await sleep(IPAfterJumpTime));
    }
    //
    private quad(t: number, p1: number[], cp: number[], p2: number[]) {
        const [x1, y1] = p1;
        const [cx, cy] = cp;
        const [x2, y2] = p2;
        const x = (1 - t) ** 2 * x1 + 2 * (1 - t) * t * cx + t ** 2 * x2;
        const y = (1 - t) ** 2 * y1 + 2 * (1 - t) * t * cy + t ** 2 * y2;
        return [x, y];
    }
    private frameUpdate = (callback: () => void, options: { animated: boolean }) => {
        const now = Date.now();
        if (now - this.startMoveTime >= this.moveTime) {
            // 动画执行完成
            this.currentStep += this.moveDirection;
            this.startMoveTime = 0;
            this.ipOffset = 0;
            this.moveSnapShot.start = this.moveSnapShot.target;
            this.moveSnapShot.offset = {
                x: 0,
                y: 0,
            };
            this.setRenderMap();
            this.moveEndCallback?.();
            callback();
            return;
        }
        // 也许可以在这里加一个贝塞尔函数
        this.ipOffset = Math.min(1, (now - this.startMoveTime) / this.moveTime) * this.moveDirection;

        if (options?.animated) {
            const [x, y] = this.quad(
                this.ipOffset,
                [this.moveSnapShot.start.x, this.moveSnapShot.start.y],
                [
                    (this.moveSnapShot.target.x + this.moveSnapShot.start.x) / 2,
                    (this.moveSnapShot.target.y + this.moveSnapShot.start.y) / 2,
                ],
                // [this.moveSnapShot.target.x, this.moveSnapShot.target.y + this.moveDirection * 100],
                [this.moveSnapShot.target.x, this.moveSnapShot.target.y],
            );
            this.moveSnapShot.offset = {
                x: x - this.moveSnapShot.start.x,
                y: y - this.moveSnapShot.start.y,
            };
        } else {
            const idx = this.currentStep % this.ipConfig.directionLoop.length;
            const vidx = this.currentStep % this.ipConfig.verticalLoop.length;

            this.moveSnapShot.offset = {
                x: this.ipOffset * this.ipConfig.directionLoop[idx] * this.ipConfig.move.x,
                y: this.ipOffset * this.ipConfig.verticalLoop[vidx] * this.ipConfig.move.y,
            };
        }

        this.setRenderMap();
        requestAnimationFrame(() => {
            this.frameUpdate(callback, options);
        });
    };

    /**
     * 设置渲染信息
     */
    private setRenderMap() {
        /** ip */
        let { x: ipRealX, y: ipRealY } = this.moveSnapShot.start;
        ipRealX += this.moveSnapShot.offset.x;
        ipRealY += this.moveSnapShot.offset.y;
        /** 相机(视点)位置 */
        // const cameraOffset =
        //     this.getGridPosByStep(this.currentStep).y + this.ipOffset * this.ipConfig.move.y - this.ipConfig.birthPos.y;
        /** render map 填充 */
        // ip
        // 可视奖励格子
        const visiblellwardStep =
            Math.ceil((this.halfVisibleHeight - this.ipConfig.birthPos.y - this.maskHeight) / this.ipConfig.move.y) + 2;
        // 格子奖励
        const grids: RenderMapType['grids'] = [];
        Object.entries(this.gridllwardConfig ?? {}).forEach(([key, value]) => {
            value.forEach((el) => {
                if (
                    el.gridLocation &&
                    el.gridLocation > this.currentStep &&
                    el.gridLocation <= this.currentStep + visiblellwardStep
                ) {
                    const { x: realX, y: realY } = this.getGridPosByStep(el.gridLocation);
                    grids.push({
                        ...el,
                        type: key as GridllwardType,
                        position: {
                            zIndex: this.getGridZIndexByStep(el.gridLocation),
                            step: el.gridLocation,
                            x: this.viewSize.width / 2 + realX,
                            y: this.viewSize.height / 2 - this.threeD2UIPos(realY),
                        },
                    });
                }
            });
        });
        /** render map 填充 */
        this.renderMap.ip.position.x = this.viewSize.width / 2 + ipRealX;
        this.renderMap.ip.position.y = this.viewSize.height / 2 - this.threeD2UIPos(ipRealY);
        // this.renderMap.ip.position.y = this.viewSize.height / 2 - ipRealY;
        this.renderMap.ip.onStation = this.signedStep.includes(this.currentStep) && this.currentStep !== 0;
        this.renderMap.ip.onBirth = this.currentStep === 0;

        this.renderMap.backBtn.position.x = this.viewSize.width / 2 + this.backBtnConfig.position.x;
        this.renderMap.backBtn.position.y = this.viewSize.height / 2 - this.threeD2UIPos(this.backBtnConfig.position.y);

        this.renderMap.midIcon.position.x = this.viewSize.width / 2 + this.midIconConfig.position.x;
        this.renderMap.midIcon.position.y = this.viewSize.height / 2 - this.threeD2UIPos(this.midIconConfig.position.y);
        this.renderMap.grids = grids;
    }

    get halfVisibleHeight() {
        return this.ui23DPos(this.viewSize.height / 2);
    }

    // 3d坐标(z) 转换为2d坐标(y)
    private threeD2UIPos(len: number) {
        return len * Math.sin(CameraRotate);
    }

    // 2d坐标(y) 转换为3d坐标(z)
    private ui23DPos(len: number) {
        return len / Math.sin(CameraRotate);
    }

    // 根据step获取格子的3d场景位置
    private getGridPosByStep(step: number) {
        // console.log('getGridPosByStep step', step);
        if (step === 0) {
            return {
                x: this.ipConfig.zeroBirthPos.x,
                y: this.ipConfig.zeroBirthPos.y,
            };
        }
        const idx = step % this.ipConfig.directionLoop.length;
        [4, 5, 6].includes(idx) ? (this.isIPHigher = false) : (this.isIPHigher = true);
        const vidx = step % this.ipConfig.verticalLoop.length;

        const xPos =
            this.ipConfig.directionLoop.slice(0, idx).reduce((acc, cur) => {
                return acc + cur * this.ipConfig.move.x;
            }, 0) + this.ipConfig.birthPos.x;
        const yPos =
            this.ipConfig.verticalLoop.slice(0, vidx).reduce((acc, cur) => {
                return acc + cur * this.ipConfig.move.y;
            }, 0) + this.ipConfig.birthPos.y;

        return {
            x: xPos,
            y: yPos,
        };
    }
    private getGridZIndexByStep(step: number) {
        // [4,5,6] 三个位置 zIndex 应 < 48，49 < 其他位置 zIndex < 51
        if ([12].includes(step)) {
            return 51;
        }
        if ([4, 5, 6, 7].includes(step)) {
            return 47;
        }
        return 50;
    }

    /**
     * 移动到指定的格子
     * @param target 目标格子对应的step
     */
    async moveTo(target?: number, animated = true, immediate = false) {
        // console.log('move To target', target);
        const targetStep = target ?? this.currentStep + 1;
        this.renderMap.ip.llwardGridName = null;
        if (this.isMoving.value) {
            // console.log('moveto blocked');
            return false;
        }

        this.isMoving.value = true;
        const offsetStep = Math.abs(targetStep - this.currentStep);
        const direction = targetStep > this.currentStep ? 1 : -1;
        for (let i = 0; i < offsetStep; i++) {
            await this.moveToNext(direction, !!animated, immediate);
        }

        this.isMoving.value = false;
        return true;
    }

    /**
     * 小人儿每次移动结束后调用
     */
    onMoveEnd(callback: () => void) {
        this.moveEndCallback = callback;
        // setLocalMapConfig(
        //     `${this.localPrefix}forwardRushGridBackGroundOffset`,
        //     `${Math.floor(this.renderMap.background.offset)}`,
        // );
        Object.entries(this.gridllwardConfig ?? {}).forEach(([key, value]) => {
            const locations = value.map((el) => el.gridLocation) ?? [];
            // ip当前是否在格子奖励里
            if (locations.includes(this.currentStep)) {
                this.renderMap.ip.llwardGridName = key as keyof typeof GridllwardType;
            }
        });
    }

    /**
     * 可以设置非常短的移动时间，实现小人儿快快的走
     */
    async moveToImmediately(step: number, time?: number) {
        this.moveTime = time ?? 0;
        await this.moveTo(step, false, true);
        this.moveTime = IPMoveTime;
    }
}
