<template>
    <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class="icon-close-small-solid"
    >
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM9.95 5.07441C10.2192 4.80517 10.6558 4.80518 10.925 5.07441C11.1942 5.34365 11.1942 5.78018 10.925 6.04941L8.97476 7.99966L10.9246 9.94951C11.1938 10.2188 11.1938 10.6553 10.9246 10.9245C10.6554 11.1938 10.2188 11.1938 9.94961 10.9245L7.99976 8.97466L6.05 10.9244C5.78076 11.1937 5.34424 11.1937 5.075 10.9244C4.80576 10.6552 4.80576 10.2187 5.075 9.94941L7.02476 7.99966L5.07461 6.04951C4.80537 5.78027 4.80537 5.34375 5.07461 5.07451C5.34385 4.80527 5.78037 4.80527 6.04961 5.07451L7.99976 7.02466L9.95 5.07441Z"
            fill="currentColor"
        />
    </svg>
</template>

<style>
:root {
    /* 箭头默认颜色 */
    --adapt-icon-close-small-solid-color: #ff4185;
}
</style>

<style lang="scss" scoped>
.icon-close-small-solid {
    color: var(--adapt-icon-close-small-solid-color);
}
</style>
