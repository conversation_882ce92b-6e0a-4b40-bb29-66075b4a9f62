<script setup lang="ts">
import type { Props as PilotProps } from '@pet/adapt.bezier-path-fly/index.vue';
import type { LightType } from '@pet/adapt.popup/usePopupLight';
import type { AdaptTransitionName } from '@pet/adapt.transition/types';
import type { AudioItem } from '@yoda/audio';

import CommonModal from '@/components/common-modals/CommonModal.vue';
import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';
import type { PopupBtnClick } from '@/utils/popupTransform/types';

const props = withDefaults(
    defineProps<{
        popupType?: PopupType;
        type?: 'dialog' | 'layer';
        show?: boolean;
        showClose?: boolean;
        hideMask?: boolean;
        sponsorLogo?: string;
        title?: string;
        subTitle?: string;
        desc?: string;
        icon?: string;
        message?: string;
        mainButton?: string;
        subButton?: string;
        btnClick?: PopupBtnClick;
        flyToTarget?: string;
        cloneFlyToTarget?: string;
        notFlyToTarget?: Ref<boolean>;
        cloneTargetSelector?: string;
        clonePilotProps?: PilotProps;
        lightType?: LightType;
        sponsorText?: string;
        bottomText?: string;
        playPopupShowSound?: AudioItem;
        aniType?: AdaptTransitionName;
        btnLoading?: MaybeRef<boolean>;
        bottomDesc?: string;
    }>(),
    {
        showClose: true,
        hideMask: false,
        lightType: 'normal',
    },
);

const getCustomStyle = computed(() => {
    return {
        '--adapt-layer-main-margin-top': '0px',
        '--adapt-layer-image-size': props.popupType === PopupType.LS_ATTRACTION_TICKETS ? '200px' : '190px',
        '--adapt-layer-main-button-primary-background-image':
            'linear-gradient(to right, #FF7001 0%, #FC2D39 49%, #F31906 100%)',
    };
});
</script>

<template>
    <CommonModal v-bind="props" :custom-style="getCustomStyle">
        <slot name="bottomDesc">
            <div class="addon-tips">{{ bottomDesc }}</div>
        </slot>
    </CommonModal>
</template>

<style scoped lang="scss">
.addon-tips {
    position: relative;
    top: 10px;
    color: #fff4ca;
    text-align: center;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
</style>
