<script setup lang="ts">
import { useABTestModel } from '@/models/abTestModel';

import DoubleLLwd from './DoubleLLwd.vue';

const { experimentBubbleInfo, isLoin } = useABTestModel();
</script>
<template>
    <div :class="['exp', isLoin ? 'exp-loin' : 'exp-packet']">
        <div class="bubble-title">{{ experimentBubbleInfo?.bubbleTitle }}</div>
        <DoubleLLwd v-if="experimentBubbleInfo" :data="experimentBubbleInfo" :is-loin="isLoin" />
    </div>
</template>
<style lang="scss" scoped>
.exp {
    background-size: 100% 100%;
    transform-origin: bottom center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    .bubble-title {
        text-align: center;
        font-family: KuaiYuanHuiTi;
        font-size: 15px;
        font-weight: 400;
        height: 21px;
        line-height: 21px;
        letter-spacing: 0;
        background: linear-gradient(271.07deg, #000000 0.92%, #4e010a 76.82%, #ab2132 100.26%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.exp-loin {
    width: 248px;
    height: 145.5px;
    @include bg('@/assets/forward-rush/bubble-bg-experiment.png');
    padding-top: 11px;
}

.exp-packet {
    width: 248px;
    height: 160.5px;
    @include bg('@/assets/first-day-0714-exp/bg-bubble-0714-exp-packet.png');
    padding-top: 12px;
}
</style>
