<pet-info lang="json">
{ "title": "文本弹窗", "description": "二次确认弹窗，用于文字确认", "priority": 6 }
</pet-info>
<script setup lang="ts">
import { toast } from '@pet/adapt.toast';
import AdaptDialog from '../index.vue';
const confirmMessage = '副文本居中根据需求内容自由拉伸文本高度，副文本居中根据需求内容自由拉伸文本高度';
function handleConfirm() {
    toast('触发 confirm 事件');
}

function handleBottomClick() {
    toast('触发 bottom-text-btn-click 事件');
}
function handleClose() {
    toast('触发 close 事件');
}
</script>

<template>
    <div class="demo-wrapper">
        <AdaptDialog
            class="static dialog-all demo-dialog"
            show
            cartoon
            title="此处为主标题"
            :message="confirmMessage"
            confirm-text="去邀请"
            cancel-text="再想想"
            bottom-message="此处为辅助信息或"
            bottom-btn-text="次级按钮"
            @confirm="handleConfirm"
            @close="handleClose"
            @bottom-text-btn-click="handleBottomClick"
        >
        </AdaptDialog>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped></style>
