<script setup lang="ts">
import { ref, watch } from 'vue';

const props = withDefaults(
    defineProps<{
        flip: boolean;
        needScale?: boolean;
        reverse?: boolean;
    }>(),
    {
        needScale: false,
        reverse: false,
    },
);

// 判断是否是初始化状态。初始化不需要翻转
const isInit = ref(true);

watch(
    () => [props.flip, props.needScale],
    () => {
        isInit.value = false;
    },
);
</script>

<template>
    <div
        class="flip-card"
        :class="
            !isInit && needScale
                ? {
                      front: !flip,
                      back: flip,
                  }
                : {}
        "
    >
        <div
            class="flip-card-inner"
            :class="
                !isInit
                    ? {
                          front: !flip,
                          back: flip,
                      }
                    : {}
            "
        >
            <div v-if="reverse" class="flip-card-front" :style="{ zIndex: flip ? 1 : 2 }">
                <slot name="back"></slot>
            </div>
            <div v-else class="flip-card-front" :style="{ zIndex: flip ? 1 : 2 }">
                <slot name="front"></slot>
            </div>
            <div v-if="reverse" class="flip-card-back" :style="{ zIndex: flip ? 2 : 1 }">
                <slot name="front"></slot>
            </div>
            <div v-else class="flip-card-back" :style="{ zIndex: flip ? 2 : 1 }">
                <slot name="back"></slot>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.flip-card {
    width: 100%;
    height: 100%;
    background-color: transparent;
    perspective: 9rem;
    border-radius: 26px;

    /* 缩放效果 */
    &.back {
        transform-origin: center center;
        animation:
            hash34fc5153_1_keyframe_0 0.4s 0s cubic-bezier(0.32, 0.977, 0.667, 1),
            hash34fc5153_1_keyframe_1 0.367s 0.4s cubic-bezier(0.333, 0, 0.667, 1) forwards;
    }

    &.front {
        transform-origin: center center;
        animation:
            hash56249cc6_1_keyframe_0 0.4s 0s cubic-bezier(0.32, 0.977, 0.667, 1),
            hash56249cc6_1_keyframe_1 0.367s 0.4s cubic-bezier(0.333, 0, 0.667, 1) forwards;
    }

    .flip-card-inner {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 26px;
        text-align: center;
        transform-style: preserve-3d;

        &.back {
            animation:
                hash34fc5153_0_keyframe_0 0.2s 0s cubic-bezier(0.333, 0, 0.667, 0.477),
                hash34fc5153_0_keyframe_1 0.2s 0.2s cubic-bezier(0.333, 0.479, 0.667, 1) forwards;
        }

        &.front {
            animation: hash56249cc6_0_keyframe_0 0.4s 0s cubic-bezier(0.333, 0, 0.667, 0.477) forwards;
        }

        .flip-card-front,
        .flip-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }

        .flip-card-back {
            /* 反面初始位置 */
            transform: rotateY(-180deg) translateZ(1px);
        }
    }
}

@keyframes hash34fc5153_0_keyframe_0 {
    0% {
        transform: rotateY(0deg);
    }
    100% {
        transform: rotateY(-86deg);
    }
}

@keyframes hash34fc5153_0_keyframe_1 {
    0% {
        transform: rotateY(-86deg);
    }
    100% {
        transform: rotateY(-180deg);
    }
}

@keyframes hash56249cc6_0_keyframe_0 {
    0% {
        transform: rotateY(-180deg);
    }
    50% {
        transform: rotateY(-86deg);
    }
    100% {
        transform: rotateY(0deg);
    }
}

@keyframes hash56249cc6_1_keyframe_0 {
    0% {
        transform: scale3d(1, 1, 1);
    }
    100% {
        transform: scale3d(1.1, 1.1, 1.1);
    }
}

@keyframes hash56249cc6_1_keyframe_1 {
    0% {
        transform: scale3d(1.1, 1.1, 1.1);
    }
    54.545% {
        transform: scale3d(0.95, 0.95, 0.95);
    }
    100% {
        transform: scale3d(1, 1, 1);
    }
}

@keyframes hash34fc5153_1_keyframe_0 {
    0% {
        transform: scale3d(1, 1, 1);
    }
    100% {
        transform: scale3d(1.1, 1.1, 1.1);
    }
}

@keyframes hash34fc5153_1_keyframe_1 {
    0% {
        transform: scale3d(1.1, 1.1, 1.1);
    }
    54.545% {
        transform: scale3d(0.95, 0.95, 0.95);
    }
    100% {
        transform: scale3d(1, 1, 1);
    }
}
</style>
