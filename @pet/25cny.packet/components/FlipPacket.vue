<script setup lang="ts">
import { ref, onMounted, computed } from 'vue-demi';
import Popup from '@pet/adapt.popup/index.vue';
import Header from './Header/index.vue';
import BtnGroup from './BtnGroup/index.vue';
import { mainComponentsMap } from './config';
import type { TitleContext, Button, PrizeDetail, BottomInfo } from './type';
import ArrRight from '../assets/arr-right.vue';
import AdaptTransition from '@pet/adapt.transition/index.vue';
import { isAtFourTab, isAtSearchTab } from '@pet/yau.core';
import type { AudioItem } from '@yoda/audio';
import type { PilotInfo } from '@pet/adapt.popup/types';
import { injectViewInfo } from '@pet/core.mobile/screenDetect';
import { transViewValue } from '@pet/core.mobile';
import FlipCard from './FlipCard.vue';
import FlipDouble from '@/components/popups/flip-popup/FlipDouble.vue';
import type { LuckRushSudokuView, RewardDoubledDetailView } from '@/services/open-api-docs/home/<USER>/schemas';
import { useLogger } from '@/init/logger';

export interface OpenPacketProps {
    show?: boolean;
    sponsorLogo?: string;
    sponsorText?: string;
    title: string;
    titleContext?: TitleContext;
    subTitle?: string;
    openSubTitle?: string;
    blessing?: string;
    openBlessing?: string;
    bottomBlessing?: string;
    subTitleDesc?: string;
    mainButton?: Button;
    subButton?: Button;
    bottomInfo?: BottomInfo;
    prizeDetail?: PrizeDetail;
    styleType?: 'red' | 'gold';
    openType?: 'none' | 'click' | 'count';
    flyToTarget?: string;
    innerScroll?: boolean;
    isProgressPacket?: boolean;
    playPopupShowSound?: AudioItem;
    playFlySound?: Promise<AudioItem>;
    pilotCtrl?: PilotInfo;
}

const { sendClick, sendShow } = useLogger();

const props = withDefaults(defineProps<OpenPacketProps & { backPop?: LuckRushSudokuView } & {
    popupType?: string;
    mainButtonInfo?: {
        linkText: string;
        task_id?: string;
    }
}>(), {
    styleType: 'red',
    innerScroll: false,
    isProgressPacket: false,
});
const emits = defineEmits<{
    (event: 'close', showCover?: boolean | undefined): void;
    (event: 'after-leave'): void;
    (event: 'main-click'): void;
    (event: 'sub-click'): void;
    (event: 'bottom-click'): void;
    (event: 'coupon-click'): void;
    (event: 'video-playing'): void;
    (event: 'video-ended'): void;
    (event: 'video-error'): void;
    (event: 'packet-open'): void;
    (event: 'open-click'): void;
}>();
const needCover = computed(() => {
    return props.openType && ['click', 'count'].includes(props.openType);
});

const needClickCover = computed(() => {
    return props.openType && ['click'].includes(props.openType);
});

const showCover = ref(needCover.value);
const showTitle = ref(true);
const canOpen = ref(true);

const openPacket = () => {
    showTitle.value = false;
    setTimeout(() => {
        showCover.value = false;
        emits('packet-open');
    }, 220);
};

const onCoverClick = () => {
    if (props.openType === 'click' && canOpen.value) {
        emits('open-click');
        canOpen.value = false;
        openPacket();
    }
};

const skinClass = computed(() => {
    return `open-packet-skin-${props.styleType}`;
});

const handleFly = async () => {
    (await props.playFlySound)?.play();
};

const openEnd = () => {
    showTitle.value = true;
};

const isAt4Tab = computed(() => isAtFourTab());
const isSearchTab = computed(() => isAtSearchTab());
const hasTab = computed(() => {
    return isAt4Tab.value || isSearchTab.value;
});
const { availableViewHeight } = injectViewInfo()!;
const scaleStyle = computed(() => {
    // 两套规则因为有tab的场景改了红包的居中方式，从包体居中改为整体居中
    if (availableViewHeight.value < transViewValue(hasTab.value ? 670 : 780)) {
        return hasTab.value ? 'scale-content-8' : 'scale-content-9';
    } else if (availableViewHeight.value < transViewValue(hasTab.value ? 700 : 799)) {
        return hasTab.value ? 'scale-content-95' : 'scale-content-96';
    }
    return '';
});

const isFlip = ref(false);

onMounted(() => {
    setTimeout(() => {
        props.playPopupShowSound?.play?.();
    }, 100);
    if (props.openType === 'count') {
        setTimeout(() => {
            openPacket();
        }, 980);
    } else if (props.openType === 'click') {
        setTimeout(() => {
            if (canOpen.value) {
                canOpen.value = false;
                openPacket();
            }
        }, 2980);
    }
});

const frontParams = {
    button_name: '我知道了',
    encourage_type: (props.prizeDetail?.[0] as any)?.unit === '金币' ? 'LLCN' : 'LLCH',
    popup_type: props.popupType,
    title: props.title,
}

const backParams = computed(() => ({
    button_name: props.mainButtonInfo?.linkText,
    encourage_type: (props.backPop?.llpeDetail?.[0] as any)?.displayOriginalLlrewdUnit === '金币' ? 'LLCN' : 'LLCH',
    popup_type: props.backPop?.popupType,
    title: props.title,
    task_id: props.mainButtonInfo?.task_id ?? '',
}));

const backData = computed(() => props.backPop?.llpeDetail?.[0] as RewardDoubledDetailView);

const handleFrontButton = () => {
    isFlip.value = true;
    sendClick('OP_ACTIVITY_REWARD_POP', frontParams);
}

const handleAfterEnter = () => {
    sendShow('OP_ACTIVITY_REWARD_POP', frontParams);
    setTimeout(() => {
        isFlip.value = true;
    }, 1000);
}

const canClose = ref(false);
const handleClose = () => {
    if (!isFlip.value) {
        isFlip.value = true;
        return;
    }
    if (!canClose.value) {
        return;
    }
    emits('close', needClickCover.value && showCover.value);
}
const handleAnimationEnd = (e: AnimationEvent) => {
    if (e.animationName.includes('hash34fc5153_1_keyframe_1')) {
        sendShow('OP_ACTIVITY_REWARD_POP', backParams.value);
        canClose.value = true;
    }
}

const handleMainBtn = () => {
    sendClick('OP_ACTIVITY_REWARD_POP', backParams.value);
    emits('main-click');
}

const showAni = ref(false);
watch(() => isFlip.value, (val) => {
    setTimeout(() => {
        showAni.value = val;
    }, 300);
}, { immediate: true })
</script>
<template>
    <Popup
        :show="show"
        :fly-to-target="flyToTarget"
        :class="[skinClass, scaleStyle, hasTab ? 'lower-close' : 'normal-close', hasTab && 'all-center']"
        :inner-scroll="innerScroll"
        :show-close="showTitle"
        ani-type="pop-packet-25cny"
        light-type="packet"
        :enter-duration="867"
        :can-close="canClose"
        @close="handleClose"
        @afterLeave="emits('after-leave')"
        @after-enter="handleAfterEnter"
        @before-fly-in="handleFly"
        :pilot-ctrl="pilotCtrl"
    >
        <template #addons>
            <div :class="['header', showTitle && 'header-animation']">
                <Header
                    :title="title"
                    :assist-info="titleContext"
                    :logo="sponsorLogo"
                    :brand-logo-degrade-text="sponsorText"
                />
            </div>
        </template>
        <FlipCard
            class="flip-wrap"
            :flip="isFlip"
            :need-scale="true"
            :reverse="false"
            @animationend="handleAnimationEnd"
        >
            <template #front>
                <div class="packet" @click="onCoverClick">
                    <div class="back-board"></div>
                    <div class="top">
                        <Transition name="content-animate">
                            <div v-if="!showCover" class="content">
                                <div class="title-content">
                                    <div v-if="openSubTitle || subTitle" class="sub-title u-fw-500">
                                        {{ openSubTitle || subTitle }}
                                    </div>
                                    <div v-if="subTitleDesc" class="sub-title-desc">{{ subTitleDesc }}</div>
                                </div>
                                <div v-if="openBlessing" class="open-blessing">{{ openBlessing }}</div>
                                <slot></slot>
                                <template v-if="prizeDetail?.length">
                                    <template v-for="(item, index) in prizeDetail">
                                        <component
                                            :is="mainComponentsMap[item.prizeType]"
                                            v-if="item && mainComponentsMap[item.prizeType]"
                                            :key="index"
                                            v-bind="item"
                                            @coupon-click="emits('coupon-click')"
                                            @video-playing="emits('video-playing')"
                                            @video-ended="emits('video-ended')"
                                            @video-error="emits('video-error')"
                                        ></component>
                                    </template>
                                </template>
                            </div>
                        </Transition>
                    </div>
                    <div class="bottom">
                        <AdaptTransition name="fade">
                            <div v-if="!showCover" class="wrapper">
                                <BtnGroup
                                    :main-button="{
                                        linkText: '我知道了',
                                    }"
                                    @mainBtnClick="handleFrontButton"
                                ></BtnGroup>
                                <div v-if="bottomInfo?.bottomDesc" class="btm-desc">
                                    {{ bottomInfo?.bottomDesc }}
                                    <template v-if="bottomInfo?.bottomButton?.linkText">
                                        <span class="link u-fw-500" @click="emits('bottom-click')">{{
                                            bottomInfo?.bottomButton?.linkText
                                        }}</span
                                        ><i class="arr">
                                            <ArrRight />
                                        </i>
                                    </template>
                                </div>
                            </div>
                        </AdaptTransition>
                    </div>
                </div>
            </template>
            <template v-if="backPop" #back>
                <div class="packet" @click="onCoverClick">
                    <div class="back-board"></div>
                    <div class="top">
                        <Transition name="content-animate">
                            <div v-if="!showCover" class="content">
                                <div class="title-content">
                                    <div v-if="backPop.subTitle" class="sub-title u-fw-500">
                                        {{ backPop.subTitle }}
                                    </div>
                                    <div v-if="subTitleDesc" class="sub-title-desc">{{ backPop.subTitleDesc }}</div>
                                </div>
                                <FlipDouble :data="backData" :show-ani="showAni" />
                            </div>
                        </Transition>
                    </div>
                    <div class="bottom">
                        <AdaptTransition name="fade">
                            <div v-if="!showCover" class="wrapper">
                                <BtnGroup :main-button="mainButtonInfo" @mainBtnClick="handleMainBtn" />
                                <div v-if="backPop.bottomButton?.linkSubText" class="btm-desc">
                                    {{ backPop.bottomButton?.linkSubText }}
                                    <template v-if="backPop.bottomButton?.linkText">
                                        <span class="link u-fw-500" @click="emits('bottom-click')">{{
                                            backPop.bottomButton?.linkText
                                        }}</span
                                        ><i class="arr">
                                            <ArrRight />
                                        </i>
                                    </template>
                                </div>
                            </div>
                        </AdaptTransition>
                    </div>
                </div>
            </template>
        </FlipCard>
        <template #footer>
            <slot name="footer"></slot>
        </template>
    </Popup>
</template>

<style>
:root {
    --cny-base-open-packet-cover-top-img: unset;
    --cny-base-open-packet-cover-top-sub-title-color: unset;
    --cny-base-open-packet-cover-top-blessing-color: unset;
    --cny-base-open-packet-top-background: unset;
    --cny-base-open-packet-inner-sub-title-color: unset;
    --cny-base-open-packet-inner-sub-title-desc-color: unset;
    --cny-base-open-packet-inner-blessing-color: unset;
    --cny-base-open-packet-btm-bg-img: unset;
    --cny-base-open-packet-btm-desc-color: unset;
    --cny-base-open-packet-back-board-img: unset;
    --cny-base-open-packet-single-btn-bg-img: unset;
    --cny-base-open-packet-right-btn-bg-img: unset;
    --cny-base-open-packet-mid-open-icon: unset;

    --cny-back-open-packet-title-margin-bottom: 13px;
    --cny-base-open-packet-margin-bottom: -80px;
    --cny-base-open-packet-padding-bottom: 80px;
}
</style>

<style lang="scss" scoped>
.normal-close {
    --adapt-popup-close-btn-top-y-value: 26px;
}

.lower-close {
    --adapt-popup-close-btn-top-y-value: 36px;
}
.scale-content-8 {
    :deep(.transform-wrapper) {
        transform: scale(0.8);
    }
}

.scale-content-9 {
    :deep(.transform-wrapper) {
        transform: scale(0.9);
    }
}

.scale-content-95 {
    :deep(.transform-wrapper) {
        transform: scale(0.95);
    }
}

.scale-content-96 {
    :deep(.transform-wrapper) {
        transform: scale(0.96);
    }
}
:deep(.popup-extension) {
    z-index: 2;
    pointer-events: none;
}

.all-center {
    :deep(.transform-wrapper) {
        .popup-content-main {
            display: flex;
            justify-content: center;
        }
        .popup-addons {
            position: relative;

            &.addons-top {
                position: relative;
                top: auto;
                left: auto;
                transform: none;
            }
        }

        .popup-footer {
            position: relative;
            left: auto;
            bottom: auto;
            transform: none;
        }
    }
}

.header {
    opacity: 0;
    transition: opacity 200ms linear;
}

.header-animation {
    opacity: 1;
}

.flip-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 300px;
    min-height: 339px;
    margin-top: 24px;
}
.packet {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 300px;
    min-height: 339px;

    .top {
        width: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        min-height: 300px;
        margin-bottom: var(--cny-base-open-packet-margin-bottom);

        .content {
            display: flex;
            width: 282px;
            border-radius: 30px;
            flex-direction: column;
            align-items: center;
            box-sizing: border-box;
            padding-top: 34px;
            padding-bottom: var(--cny-base-open-packet-padding-bottom);
            position: relative;
            background: var(--cny-base-open-packet-top-background);
            min-height: 300px;

            .title-content {
                text-align: center;
                margin-bottom: var(--cny-back-open-packet-title-margin-bottom);

                .sub-title {
                    width: 220px;
                    text-align: center;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 20px;
                    line-height: 26px;
                    color: var(--cny-base-open-packet-inner-sub-title-color);
                }

                .sub-title-desc {
                    width: 210px;
                    margin: auto;
                    text-align: center;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-size: 14px;
                    color: var(--cny-base-open-packet-inner-sub-title-desc-color);
                    opacity: 0.6;
                    margin-top: 2px;
                }
            }

            .open-blessing {
                width: 210px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin: -6px 0 20px;
                font-size: 14px;
                color: var(--cny-base-open-packet-inner-blessing-color);
                opacity: 0.6;
            }
        }

        .cover-top {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 300px;
            height: 276px;
            bottom: 30px;
            z-index: 1;
            background: no-repeat center/100%;
            background-image: var(--cny-base-open-packet-cover-top-img);

            .cover-sub-title {
                width: 240px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-top: 70px;
                font-size: 22px;
                color: var(--cny-base-open-packet-cover-top-sub-title-color);
            }

            .blessing {
                width: 220px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-top: 16px;
                font-size: 15px;
                color: var(--cny-base-open-packet-cover-top-blessing-color);
            }
        }
    }

    .bottom {
        position: relative;
        width: 100%;
        height: 171px;
        background: no-repeat center/100%;
        background-image: var(--cny-base-open-packet-btm-bg-img);

        .wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            position: relative;
            padding-bottom: 20px;

            .btm-desc {
                display: flex;
                align-items: center;
                justify-content: center;
                line-height: 18px;
                opacity: 0.8;
                font-size: 12px;
                color: var(--cny-base-open-packet-btm-desc-color);
                margin-top: -6px;

                .link {
                    display: inline-block;
                    line-height: 18px;
                }

                .arr {
                    margin-left: 2px;
                    display: flex;
                    align-items: center;
                    height: 18px;
                    font-size: 10px;
                }
            }
        }
    }

    .mid-icon {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 146px;
        height: 146px;
        bottom: 75px;
        z-index: 1;

        .wrapper {
            position: absolute;
            width: 146px;
            height: 146px;
            z-index: 1;
            animation: mid-icon-scale 0.633s 0.3s infinite;

            .mid-animate {
                position: absolute;
                width: 146px;
                height: 146px;
                background: no-repeat center/100%;
                background-image: var(--cny-base-open-packet-mid-open-icon);
                animation: mid-icon-shake 0.633s 0.3s cubic-bezier(0.33, 0, 0.67, 1) infinite;
            }
        }

        .circle-light {
            position: absolute;
            width: 80px;
            height: 80px;
            z-index: 1;
            background: no-repeat center/100%;
            background-image: url('./assets/circle-light.png');
            animation: light-scale 0.633s 0.3s cubic-bezier(0.33, 0.35, 0.67, 1) infinite;
        }
    }

    .back-board {
        position: absolute;
        left: 0;
        bottom: 52px;
        width: 300px;
        height: 300px;
        background: no-repeat center/100%;
        background-image: var(--cny-base-open-packet-back-board-img);
    }
}

.cover-animate {
    &-leave-active {
        animation: draw-top 0.167s linear;
    }
}

@keyframes draw-top {
    0% {
        opacity: 1;
        transform: translateY(0);
    }

    44% {
        opacity: 1;
        transform: translateY(3px);
    }

    100% {
        opacity: 0;
        transform: translateY(-252px);
    }
}

.content-animate {
    &-enter {
        transform: scaleY(0.75) translateY(19px);
    }

    &-enter-active {
        transform-origin: center bottom;
        animation: content-show 0.6s linear forwards;
    }

    &-enter-to {
        transform: scaleY(1) translateY(0);
    }
}

@keyframes content-show {
    0% {
        transform: scaleY(0.75) translateY(19px);
    }

    20% {
        transform: scaleY(0.75) translateY(19px);
    }

    46% {
        transform: scaleY(1) translateY(-4px);
    }

    73.4% {
        transform: scaleY(1) translateY(1.5px);
    }

    100% {
        transform: scaleY(1) translateY(0);
    }
}

@keyframes mid-icon-shake {
    0% {
        transform: rotate(0deg);
    }

    11.4% {
        transform: rotate(-14deg);
    }

    22.8% {
        transform: rotate(14deg);
    }

    34.4% {
        transform: rotate(-14deg);
    }

    45.6% {
        transform: rotate(14deg);
    }

    61.3% {
        transform: rotate(-14deg);
    }

    77% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(0deg);
    }
}

@keyframes mid-icon-scale {
    0% {
        transform: scale(1.01);
    }

    26.38% {
        transform: scale(1.069);
        animation-timing-function: cubic-bezier(0.17, 0, 0.58, 3.66);
    }

    52.76% {
        transform: scale(0.9868);
        animation-timing-function: cubic-bezier(0.42, -2.23, 0.83, 1);
    }

    100% {
        transform: scale(0.9868);
    }
}

@keyframes light-scale {
    0% {
        transform: scale(1.2474);
        opacity: 1;
    }

    36% {
        opacity: 1;
    }

    73% {
        transform: scale(2.4213);
        opacity: 0;
    }

    100% {
        transform: scale(2.4213);
        opacity: 0;
    }
}

.open-packet-skin-red {
    --cny-base-open-packet-cover-top-img: url('../assets/open-packet-cover-top.png');
    --cny-base-open-packet-cover-top-sub-title-color: #fef6ca;
    --cny-base-open-packet-cover-top-blessing-color: #fef6ca;
    --cny-base-open-packet-top-background: linear-gradient(180deg, #ffffff 30.24%, #fff9f3 57.09%, #ffe4dd 80.38%);
    --cny-base-open-packet-inner-sub-title-color: #550000;
    --cny-base-open-packet-inner-sub-title-desc-color: #550000;
    --cny-base-open-packet-inner-blessing-color: #550000;
    --cny-base-open-packet-btm-bg-img: url('../assets/open-packet-btm-bg.png');
    --cny-base-open-packet-btm-desc-color: #fef6ca;
    --cny-base-open-packet-back-board-img: url('../assets/open-packet-back-board.png');
    --cny-base-open-packet-single-btn-bg-img: url('../assets/open-packet-single-btn.png');
    --cny-base-open-packet-single-btn-font-color: #f7211d;
    --cny-base-open-packet-left-btn-font-color: #ffdcb4;
    --cny-base-open-packet-right-btn-bg-img: url('../assets/open-packet-right-btn.png');
    --cny-base-open-packet-right-btn-font-color: #ff0b24;
    --cny-base-open-packet-mid-open-icon: url('../assets/close-packet-icon-open.png');
}
.open-packet-skin-gold {
    --cny-base-open-packet-cover-top-img: url('../assets/golden-open-packet-cover-top.png');
    --cny-base-open-packet-cover-top-sub-title-color: #ba3608;
    --cny-base-open-packet-cover-top-blessing-color: #ba3608;
    --cny-base-open-packet-top-background: linear-gradient(180deg, #ffffff 30.24%, #fff9f3 57.09%, #ffe4dd 80.38%);
    --cny-base-open-packet-inner-sub-title-color: #550000;
    --cny-base-open-packet-inner-sub-title-desc-color: #550000;
    --cny-base-open-packet-inner-blessing-color: #550000;
    --cny-base-open-packet-btm-bg-img: url('../assets/golden-open-packet-btm-bg.png');
    --cny-base-open-packet-btm-desc-color: #ba3608;
    --cny-base-open-packet-back-board-img: url('../assets/golden-open-packet-back-board.png');
    --cny-base-open-packet-single-btn-bg-img: url('../assets/golden-open-packet-single-btn.png');
    --cny-base-open-packet-single-btn-font-color: #fff5e6;
    --cny-base-open-packet-left-btn-font-color: #bd420d;
    --cny-base-open-packet-right-btn-bg-img: url('../assets/golden-open-packet-right-btn.png');
    --cny-base-open-packet-right-btn-font-color: #fff5e6;
    --cny-base-open-packet-mid-open-icon: url('../assets/golden-close-packet-icon-open.png');
}
</style>
