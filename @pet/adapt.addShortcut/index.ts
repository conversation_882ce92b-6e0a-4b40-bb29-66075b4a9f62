import { usePopupModel } from '@/models/popup.model';
import { loadUrlByOpenBrowser, addShortcut as addShortCutInDesktopBridge } from './bridge/bridgeList';
import { getCookie } from './cookie/index';
// eslint-disable-next-line import/no-cycle
import sheetPopup from './popup/sheet.vue';

export function isInIOS(userAgent = navigator.userAgent) {
    return /iPhone|iPad|iPod/i.test(userAgent);
}
//积木页
export const explainLink =
    'https://ppg.viviv.com/doodle/xDioDAOw.html?uni_src=other_secondary_page&hyId=jimu&bizId=jimu_xDioDAOw&layoutType=4&noBackNavi=true';

export interface shortCutType {
    url: string; //必填 端内 h5 活动页 URL
    icon: string; //必填 活动 icon
    name: string; //必填 活动标题
    id?: string; //可选 自定义 id, 通过此字段可以避免重复创建快捷方式， 以及后续考虑通过 id 统计活动参与度
    hidePopup?: boolean; //可选  代表是否隐藏默认弹窗 默认false
    styleConfig: {
        bgImg: string; // 必填  IOS中间页背景图
        bgColor: string; // 必填 IOS中间色背景色
        btText?: string; // 可选 按钮文案 默认‘点击进入活动页’
    };
}
export enum kpnTypes {
    NEBULA = 'NEBULA',
    KUAISHOU = 'KUAISHOU',
}
//  端外H5页面链接
const H5_URL =
    process.env.NODE_ENV === 'production'
        ? 'https://ug.kuaishou.com/ug/activity/fission/shortcut'
        : 'https://ug-activity.staging.kuaishou.com/ug/activity/fission/shortcut';

const result = {
    status: '',
    url: '',
};
// ios添加快捷方式
const addShortcutByIos = async (object: shortCutType, encodeUrl: string, localKpn: string) => {
    // 拼接ios中间页的url
    const openUrl =
        H5_URL +
        `?url=${encodeUrl}&icon=${object.icon}&name=${object.name}&id=${
            object.id ?? object.url
        }&kpn=${localKpn}&bgImg=${object.styleConfig.bgImg}&bgColor=${encodeURIComponent(
            object.styleConfig.bgColor,
        )}&btText=${object.styleConfig.btText ?? '点击进入活动页'}`;
    // 通过浏览器打开中间页
    const res = await loadUrlByOpenBrowser({ url: openUrl });
    // 根据返回结果处理status
    if (res.result === 1) {
        result.status = 'success';
    } else {
        // 调桥失败
        result.status = 'fail';
    }
};

// 安卓添加快捷方式
const addShortcutByAndrio = async (object: shortCutType, dpLink: string) => {
    await addShortCutInDesktopBridge({
        uri: dpLink,
        icon: object.icon,
        shortcutId: object.id ?? object.url,
        name: object.name,
    })
        .then((res: any) => {
            if (res.result === 1) {
                result.status = 'success';
            } else {
                result.status = 'undefined';
            }
        })
        .catch((err) => {
            // 桥报错
            if (err.code === 1001) {
                result.status = 'repeat';
            } else {
                result.status = 'fail';
            }
            console.log('catch addShortCutInDesktopBridge', err);
        });
};
export const addShortCut = async (object: shortCutType) => {
    const { openPopup } = usePopupModel();
    // 是否隐藏安卓通用弹窗
    const hidePopup = object.hidePopup ?? false;

    // 拼接dpLink
    const localKpn = getCookie('kpn') as kpnTypes;
    const encodeUrl = encodeURIComponent(object.url);
    let dpLink = object.url;
    if (localKpn === kpnTypes.KUAISHOU) {
        dpLink = `kwai://webview?url=${encodeUrl}`;
    } else {
        dpLink = `ksnebula://webview?url=${encodeUrl}`;
    }

    if (isInIOS()) {
        // ios调用该方法
        await addShortcutByIos(object, encodeUrl, localKpn);
    } else {
        // 安卓调用该方法
        await addShortcutByAndrio(object, dpLink);
        // 安卓必弹
        if (!hidePopup) {
            const task = openPopup({
                component: sheetPopup as any,
                data: {},
                options: {
                    name: 'sheetPopup',
                    priority: 99999,
                    queueTags: ['channel1'],
                },
            });
            task.end.then((data: any) => {
                console.log('!!!popup1111', data);
                if (data.event === 'confirm') {
                    console.log('!!!popup', data);
                }
            });
        }
        //  积木url
        result.url = explainLink;
    }
    return result;
};
