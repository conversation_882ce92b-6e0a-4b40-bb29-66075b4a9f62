<script lang="ts">
export default {
    name: 'AdaptBackToTop',
};
</script>

<script lang="ts" setup>
import type { VNode } from 'vue';
import { ref } from 'vue-demi';
import { scrollTo, useDomListener, throttle } from '@pet/yau.core';
import { transViewValue } from '@pet/core.mobile';

export type BackToTopProps = {
    /**
     * 滑动多少距离后显示返回按钮
     */
    offsetDistanceShow?: number;
    /**
     * 滚动时间
     */
    scrollDuration?: number;
    /**
     * 滚动距离
     */
    offset?: number;
    /**
     * 滚动至目标
     */
    target?: string;
};

export type BackToTopEmits = {
    /**
     * 滚动开始
     */
    (event: 'start'): void;
    /**
     * 滚动结束
     */
    (event: 'end'): void;
};

export type BackToTopSlots = {
    default?: () => VNode[] | undefined;
};

const props = withDefaults(defineProps<BackToTopProps>(), {
    offsetDistanceShow: 3000,
    scrollDuration: 500,
    offset: 0,
    target: '',
});

const emit = defineEmits<BackToTopEmits>();

const showButton = ref(false);
const scrollIsOver = ref(false);
const onScroll = () => {
    const scrollTop = window.scrollY || document.documentElement.scrollTop;
    showButton.value = scrollTop > transViewValue(props.offsetDistanceShow) && !scrollIsOver.value;
    scrollIsOver.value = false;
};

useDomListener(() => window, 'scroll', throttle(onScroll, 100), {
    passive: true,
});

function scrollEnd() {
    scrollIsOver.value = true;
    emit('end');
}

function goTop() {
    const root = (document.scrollingElement as HTMLElement) ?? document.documentElement;
    let offset = 0;
    if (props.offset > 0) {
        offset = transViewValue(props.offset);
    } else if (props.target !== '') {
        const el = document.getElementById(props.target);
        if (!el) {
            console.error('back-to-top: can not get HTMLElement');
            return;
        }
        offset = Math.round(el.getBoundingClientRect().y);
    } else {
        offset = -root.scrollTop;
    }
    emit('start');
    scrollTo(root, {
        direction: 'y',
        duration: props.scrollDuration,
        offset,
        onEnd: scrollEnd,
    });
}
</script>

<template>
    <Transition name="fade">
        <div v-if="showButton" role="button" class="back-button" @click="goTop">
            <slot />
        </div>
    </Transition>
</template>

<style>
:root {
    /* 返回顶部的背景 */
    --adapt-back-to-top-background: url('./assets/back-icon.png');
    /* 返回顶部的宽度 */
    --adapt-back-to-top-width: 44px;
    /* 返回顶部的高度 */
    --adapt-back-to-top-height: 44px;
}
</style>

<style lang="scss" scoped>
.back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    right: 50%;
    margin-right: -193px;
    bottom: 100px;
    z-index: 100;
    width: var(--adapt-back-to-top-width);
    height: var(--adapt-back-to-top-height);
    opacity: 1;
    background: var(--adapt-back-to-top-background);
    background-size: 100%;
    border-radius: 50%;
}
.fade-enter-active,
.fade-leave-active {
    transition:
        opacity 200ms ease,
        transform 200ms ease;
}

.fade-enter-from,
.fade-enter,
.fade-leave-to {
    opacity: 0;
    transform: translate(0, 50%);
}
</style>
