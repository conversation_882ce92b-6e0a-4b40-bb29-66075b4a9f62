<script lang="ts" setup>
import { useTeamPushModel } from '@/models/teamPushModel';

const { selected, selectedChange } = useTeamPushModel();
</script>
<template>
    <div class="team-panel__push-allow" @click="selectedChange">
        <div class="team-panel__push-select">
            <span v-if="selected" class="team-panel__push-selected" />
            <span v-else class="team-panel__push-not-selected" />
        </div>
        <div class="team-panel__push-text">同时订阅来自队友的打卡推送提醒</div>
    </div>
</template>
<style lang="scss" scoped>
.team-panel__push-allow {
    width: 100%;
    height: 18px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 15px;
}
.team-panel__push-text {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 13px;
    line-height: 18px;
    letter-spacing: 0px;
    vertical-align: middle;
    color: #c98888;
}
.team-panel__push-select {
    display: flex;
    align-items: center;
    position: relative;
    left: -2px;
    width: 18.5px;
    height: 18px;
}
.team-panel__push-not-selected {
    width: 100%;
    height: 100%;
    @include bg('../assets/push-not-selected.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.team-panel__push-selected {
    width: 100%;
    height: 100%;
    @include bg('../assets/push-selected.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
</style>
