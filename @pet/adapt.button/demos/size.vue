<pet-info lang="json">
{ "title": "不同尺寸按钮", "description": "按钮尺寸以高度为规范划分，支持高度 22 30 38 46 60 66 73", "priority": 8 }
</pet-info>
<script lang="ts" setup>
import Button from '../index.vue';
</script>

<template>
    <div>
        <div class="title">按钮尺寸以高度为规范划分，支持高度 22 38 46 60 66 73</div>
        <div class="title">规范高度22尺寸按钮</div>
        <Button class="item-btn" :height="22">邀请</Button>
        <Button class="item-btn" :height="22">去邀请</Button>
        <Button class="item-btn" :height="22">立即邀请</Button>
        <div class="title">规范高度30尺寸按钮</div>
        <Button class="item-btn" :height="30">邀请</Button>
        <Button class="item-btn" :height="30">去邀请</Button>
        <Button class="item-btn" :height="30">立即邀请</Button>
        <div class="title">规范高度38尺寸按钮</div>
        <Button class="item-btn" :height="38">邀请</Button>
        <Button class="item-btn" :height="38">去邀请</Button>
        <Button class="item-btn" :height="38">立即邀请</Button>
        <Button class="item-btn" :height="38">立即去邀请</Button>
        <div class="title">规范高度46尺寸按钮</div>
        <Button class="item-btn" :height="46">邀请</Button>
        <Button class="item-btn" :height="46">去邀请</Button>
        <Button class="item-btn" :height="46">立即邀请</Button>
        <Button class="item-btn" :height="46">立即去邀请</Button>
        <div class="title">规范高度60尺寸按钮</div>
        <Button class="item-btn" :height="60">邀请</Button>
        <Button class="item-btn" :height="60">去邀请</Button>
        <Button class="item-btn" :height="60">立即邀请</Button>
        <Button class="item-btn" :height="60">立即去邀请</Button>
        <div class="title">规范高度66尺寸按钮</div>
        <Button class="item-btn" :height="66">邀请</Button>
        <Button class="item-btn" :height="66">去邀请</Button>
        <Button class="item-btn" :height="66">立即邀请</Button>
        <Button class="item-btn" :height="66">立即去邀请</Button>
        <div class="title">规范高度38尺寸按钮</div>
        <Button class="item-btn" :height="72">邀请</Button>
        <Button class="item-btn" :height="72">去邀请</Button>
        <Button class="item-btn" :height="72">立即邀请</Button>
        <Button class="item-btn" :height="72">立即去邀请</Button>
    </div>
</template>

<style lang="scss" scoped>
div {
    font-size: 14px;
}
.title {
    font-size: 20px;
    margin-top: 20px;
}
.item-btn {
    margin: 10px 4px;
}
</style>
