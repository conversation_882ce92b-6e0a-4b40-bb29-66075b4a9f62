<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';

const LlwardGridAni = defineAsyncComponent(() => import('./gridCoins/EffectIndex.vue'));
const CoinPilot = defineAsyncComponent(() => import('./CoinPilot.vue'));
const { effectShowStatus } = useDowngradeLevel();

const downgrade = computed(() => {
    return !effectShowStatus.value.L1;
});
defineProps<{
    show: boolean;
    name: string;
}>();
const emits = defineEmits<{
    (e: 'ended'): void;
}>();

const coinCount = 6;
const endedIndex = ref<number>(0);
const handleEnd = async () => {
    endedIndex.value++;
    if (endedIndex.value === coinCount) {
        emits('ended');
    }
};
</script>
<template>
    <LlwardGridAni v-if="!downgrade" class="llward-grid-ani" @ended="emits('ended')"></LlwardGridAni>
    <template v-else>
        <CoinPilot v-for="i in coinCount" :key="i" :delay="i * 100" @ended="handleEnd" />
    </template>
</template>

<style lang="scss" scoped></style>
