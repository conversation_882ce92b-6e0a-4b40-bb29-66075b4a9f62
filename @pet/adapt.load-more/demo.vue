<pet-info lang="json">
{ "title": "加载更多", "description": "加载更多示例" }
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';

import LoadMore from './index.vue';
const dyTmpList = [
    ' 然集地育国委如再了新见分相片根率。然马空着整好完易种向强育响于设行。',
    ' 指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。',
    ' 将据重打金解土大安者界及压族。联物长效决观存因二多布节好。亲消社且说代又到养个新切量专型。基合约听书问科你经直教少速国。养而联理火受广有证海华政最由细看。',
    ' 解住族理与上问关音机主然过色物次。就清布将而整学难管非程参地持高么步住。流认关边美为性县育力很数研低质高习。解住族理与上问关音机主然过色物次。就清布将而整学难管非程参地持高么步住。流认关边美为性县育力很数研低质高习。解住族理与上问关音机主然过色物次。就清布将而整学难管非程参地持高么步住。流认关边美为性县育力很数研低质高习。',
    ' 详解bug的时候发现他并不是一个bug，不想解bug的时候发现bug真正存在',
    ' 你知道的事情就一定知道，你不知道的事情就一定不知道。有些事情发生的时候好，有些事情发生的时候并不好，人生总是需要一点心灵鸡汤',
    ' 小明从不聪明，但它有个哥哥很聪明',
    ' 然集地育国委如再了新见分相片根率。然马空着整好完易种向强育响于设行。',
    ' 指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。指度近知严头能低结他置现越许达阶是花。叫且比拉层说她常边农收进着委给书看。科圆县科支天里在一响通元万清强展外。矿速器别往转信维按成住为务。',
    ' 将据重打金解土大安者界及压族。联物长效决观存因二多布节好。亲消社且说代又到养个新切量专型。基合约听书问科你经直教少速国。养而联理火受广有证海华政最由细看。',
];

const noMore = ref(false);
const list = ref(dyTmpList);
const count = ref(0);
function loadData() {
    window.setTimeout(() => {
        let tmp = dyTmpList;
        if (count.value > 4) {
            tmp = [];
            noMore.value = true;
        }
        list.value = list.value.concat(tmp);
        count.value += 1;
    }, 500);
}
</script>

<template>
    <div class="wrapper">
        <div class="list">
            <div
                v-for="(item, index) in list"
                :key="index"
                :style="{ border: '1px solid #ccc', margin: '10px', padding: '10px' }"
            >
                <span :style="{ color: 'red' }">{{ index }}</span>
                {{ item }}
            </div>
            <LoadMore root-margin="0px 0px 50px 0px" :no-more="noMore" @to-bottom="loadData">
                <template #info>
                    <p v-if="noMore">加载完毕</p>
                    <p v-else>加载中</p>
                </template>
            </LoadMore>
        </div>
    </div>
</template>

<style>
html,
body {
    height: 100%;
}
</style>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
    --infinityLoading-color: red;
    font-size: 16px;
}
.list {
    flex: 1 1 0%;
    overflow: auto;
}
</style>
