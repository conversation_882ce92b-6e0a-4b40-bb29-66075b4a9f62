<script setup lang="ts">
import { computed } from 'vue-demi';

import CardValue from './components/value.vue';
import { getBtnText } from './config/utils';
import type { CouponDisplayType, UseStatus } from './types';

interface Props {
    /**
     * 卡券类型
     * @values 同`coupon-ui`
     */
    type?: CouponDisplayType;
    /**
     * 主要值
     */
    valueMain?: string;
    /**
     * 值描述
     */
    valueDesc?: string;
    /**
     * 图
     */
    valueImage?: string;
    /**
     * 按钮文案
     */
    btnText?: string;
    /**
     * 状态
     * @values 同`coupon-ui`
     */
    status?: UseStatus;
    /**
     * 标签
     */
    tag?: string;
    /**
     * 按钮禁用
     */
    btnDisabled?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
    type: 'default',
    status: 'not_use',
    jump: true,
});

const emit = defineEmits<{
    /**
     * 卡券点击
     */
    (event: 'coupon-click'): void;
}>();

const statusText = computed(() => props.btnText ?? getBtnText(props.status));
const couponDisabled = computed(() => Boolean(['out_of_date', 'has_used'].includes(props.status) || props.btnDisabled));
const couponClass = computed(() => [{ 'is-relative': Boolean(props.tag) }, { 'is-disabled': couponDisabled.value }]);

function handleCouponClick() {
    emit('coupon-click');
}
</script>

<template>
    <div class="coupon-cube" :class="couponClass" @click="handleCouponClick">
        <span v-if="tag" class="coupon-tag">
            {{ tag }}
        </span>
        <div class="coupon-value-main">
            <CardValue :type="type" :main="valueMain" :desc="valueDesc" :image="valueImage"> </CardValue>
        </div>
        <div class="coupon-status">
            {{ statusText }}
        </div>
    </div>
</template>

<style lang="scss" scoped>
@use 'sass:map';

$coupon-cube-size: (
    'width': 120px,
    'height': 117px,
);
$coupon-cube-background: url(./assets/bg_cube.png);
$coupon-value-color: #fe3666;
$coupon-tag-background: #ffe3e9;
.coupon-cube {
    display: inline-flex;
    flex-direction: column;
    justify-content: space-between;
    width: map.get($coupon-cube-size, 'width');
    height: map.get($coupon-cube-size, 'height');
    background-image: $coupon-cube-background;
    background-size: 100% 100%;
    text-align: center;
    color: $coupon-value-color;
    font-size: 12px;
    line-height: 16px;
    &.is-relative {
        position: relative;
    }
    &.is-disabled {
        opacity: 0.7;
    }

    :deep(.coupon-value-number-large) {
        font-size: 28px;
    }
}
.coupon-status {
    padding-bottom: 12px;
    font-weight: 700;
}
.coupon-value-main {
    width: 100%;
    height: 78px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 6px;
}
.coupon-tag {
    position: absolute;
    left: 0;
    top: 0;
    background: $coupon-tag-background;
    padding: 2px 4px;
    font-size: 9px;
    line-height: 12px;
    border-radius: 8px 0 4px 0;
}
</style>
