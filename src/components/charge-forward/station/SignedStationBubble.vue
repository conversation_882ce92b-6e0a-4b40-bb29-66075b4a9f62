<script lang="ts" setup>
import defaultAvatar from '../assets/signed/avatar.png';

const props = withDefaults(
    defineProps<{
        avatar?: string;
        location: string;
    }>(),
    {
        avatar: defaultAvatar,
    },
);
</script>

<template>
    <div class="content-group">
        <div class="highlight-rectangle"></div>
        <img class="image-frame" src="../assets/signed/avatar-bg.png" alt="头像" />
        <img class="image-frame avatar" :src="avatar" alt="头像" />
        <img class="additional-group" src="../assets/signed/signedIcon.png" alt="状态" />
        <div class="address-section">
            <div class="record-item">{{ location }}</div>
        </div>
        <div class="additional-record-item">今日已打卡</div>
    </div>
</template>

<style scoped>
.content-group {
    box-sizing: border-box;
    height: 50px;
    width: 122px;
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    margin: auto;
    z-index: 1;
}

.highlight-rectangle {
    box-sizing: border-box;
    position: absolute;
    top: 0px;
    left: 0px;
    width: 122px;
    height: 50px;
    border-width: 1px;
    border-style: solid;
    border-color: #ffffff;
    border-top-left-radius: 12.22px;
    border-top-right-radius: 12.22px;
    border-bottom-right-radius: 12.22px;
    border-bottom-left-radius: 12.22px;
    background: linear-gradient(181.76deg, #e5ffbf 1.53%, #e1ffb7 54.6%, #acff3b 127.67%);
}

.image-frame {
    box-sizing: border-box;
    position: absolute;
    top: 7px;
    left: 8px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
}
.avatar {
    width: 30px;
    height: 30px;
    top: 10px;
    left: 11px;
}

.additional-group {
    box-sizing: border-box;
    position: absolute;
    bottom: 7px;
    left: 30px;
    width: 16px;
    height: 16px;
}

.address-section {
    box-sizing: border-box;
    position: absolute;
    top: 7px;
    right: 10px;
    width: 60px;
    height: 16px;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    /* gap: 2px; */
}

.record-item {
    box-sizing: border-box;
    width: 100%;
    vertical-align: top;
    font-size: 13px;
    line-height: 16px;
    font-family: PingFang SC;
    font-weight: 600;
    color: #000000;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.additional-record-item {
    box-sizing: border-box;
    position: absolute;
    bottom: 7px;
    right: 10px;
    text-align: center;
    font-size: 12px;
    line-height: 16.8px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #8a8a8a;
}
</style>
