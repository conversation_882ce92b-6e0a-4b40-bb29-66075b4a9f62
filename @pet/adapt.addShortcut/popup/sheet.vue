<script lang="ts">
export default {
    name: 'SheetPopup',
};
</script>
<script lang="ts" setup>
import Sheet from '@pet/adapt.sheet/index.vue';
import { loadUrlOnNewPageBridge } from '../bridge/bridgeList';
// eslint-disable-next-line import/no-cycle
import { explainLink } from '../index';

const openPage = () => {
    loadUrlOnNewPageBridge({ url: explainLink, type: 'back', leftTopBtnType: 'back' }).catch(() => {
        window.location.href = explainLink;
    });
};
const emits = defineEmits<{
    (e: 'close'): void;
    (e: 'end'): void;
}>();
const onCloseClick = () => {
    emits('close');
    emits('end');
};
</script>
<template>
    <Sheet :cartoon="false" @mask-close="onCloseClick">
        <div class="close" @click="onCloseClick" />
        <div class="containt">
            <div class="title u-fw-500">操作成功</div>
            <div class="desc">若添加失败，请前往手机设置，为快手打开“创建桌面快捷方式”的权限</div>
            <div class="link u-fw-500" @click="openPage">
                如何开启权限
                <span class="icon" />
            </div>
            <div class="btn">
                <button class="right normal u-fw-500" @click="onCloseClick">知道了</button>
            </div>
        </div>
    </Sheet>
</template>

<style lang="scss" scoped>
::v-deep .sheet-inner {
    background: #ffffff;
}
.close {
    position: absolute;
    height: 24px;
    width: 24px;
    top: 20px;
    right: 31px;
    background-image: url('./assets/close.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}
.containt {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.title {
    font-size: 20px;
    // font-weight: 500;
    line-height: 28px;
    color: #222222;
    margin-top: 32px;
}
.desc {
    width: 350px;
    height: 46px;
    font-size: 15px;
    color: #666666;
    font-weight: 400;
    line-height: 23px;
    margin-top: 12px;
    text-align: center;
}
.link {
    height: 23px;
    font-size: 15px;
    // font-weight: 500;
    line-height: 23px;
    color: #385080;
    margin-top: 24px;
    display: flex;
    align-items: center;
    .icon {
        height: 12px;
        width: 12px;
        background-image: url('./assets/ios-icon-shortcut.png');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
}
.btn {
    width: 414px;
    display: flex;
    justify-content: center;
    padding: 24px 32px 16px;
    .right {
        border: none;
        color: #ffffff;
        background-color: #fe3666;
        margin-left: 16px;
    }
    .normal {
        border-radius: 24px;
        width: 350px;
        display: flex;
        height: 48px;
        padding: 12.5px 24px;
        justify-content: center;
        align-items: center;
        font-size: 15px;
        // font-weight: 500;
        line-height: 23px;
    }
}
</style>
