<pet-info lang="json">
{ "title": "插槽用法", "description": "跑马灯弹幕", "priority": 1 }
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';

import { carousel, carouselItem } from '../index';

const data = ref(
    Array.from({ length: 10 }).map((x, i) => ({
        i,
        name: `<span class='index'>序号${i}</span>标题插槽`,
    })),
);
</script>

<template>
    <div class="demo">
        <div class="title">插槽控制第一行内容、第二行内容</div>
        <div class="container">
            <carousel :list="data">
                <template #default="{ item }">
                    <carouselItem>
                        <template #title><div v-html="item.name"></div> </template>
                        <template #desc><div class="desc">这是一个描述位置插槽</div></template>
                    </carouselItem>
                </template>
            </carousel>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.demo {
    .title {
        font-size: 16px;
    }
    :deep(.index) {
        margin-right: 4px;
        font-size: 10px;
        font-weight: 500;
        color: red;
    }
    .desc {
        color: #fffeb9;
    }
}

.container {
    margin-top: 100px;
}
</style>
