<pet-info lang="json">
{
    "title": "最大值",
    "description": "超出最大值展示"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const numberValueData = ref(80);

function numberChange() {
    const num = 5;
    numberValueData.value += num * Math.random();
}
</script>

<template>
    <div class="demo">
        <h5>原始数值{{ numberValueData }}</h5>
        <div class="container">
            <h6>:max="99"</h6>
            <NumberScroll :number-value="numberValueData" :max="99" />
        </div>
        <button @click="numberChange">增加数值</button>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 48px 0 24px;
    font-size: 12px;
    text-align: center;
}
h5 {
    position: absolute;
}
</style>
