import { createUseModel } from '@gundam/model';
import { getUrlSearchParams } from '@pet/yau.core';

export const useSocialGuide0714Model = createUseModel(() => {
    const isSocial = getUrlSearchParams().entry_src === 'ks_2025sum_070' && !getUrlSearchParams().dayIndex;
    const isFromSocialGuide = computed(() => isSocial);
    const inSocialProcess = ref(isSocial);
    // 0714迭代社交导流-如果是通过社交导流进入，第一次请求时需要规避掉派发弹窗
    const isInitSocialGuide = ref(isFromSocialGuide.value);
    // 0714社交导流-是否自动拉起组件面板
    const canAutoOpenTeamPanel = ref(false);

    return {
        inSocialProcess,
        isInitSocialGuide,
        isFromSocialGuide,
        canAutoOpenTeamPanel,
    };
});
