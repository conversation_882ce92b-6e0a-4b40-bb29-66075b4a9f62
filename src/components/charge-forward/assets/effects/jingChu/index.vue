<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import Picture from "@pet/quantum.PictureImg";
import TVPlayer from '@pet/summer.tvplayer/index.vue';

import { useAnimationPause } from '@/models/animationVisibility';

import jingChuPng from './assets/fallback.png';
import fallback from './assets/fallback.png?preset=modern';
import jingChuMov from './assets/jingChu.mov?url';
import jingChuMp4 from './assets/jingChu.mp4?url';
import jingChuWebm from './assets/jingChu.webm?url';

const { effectShowStatus } = useDowngradeLevel();
const { isAnimationPause } = useAnimationPause()
const emit = defineEmits<{
    (e: 'ended'): void;
}>();

const urls = {
    mp4: jingChuMp4,
    mov: jing<PERSON>hu<PERSON><PERSON>,
    webm: jingChuWebm,
}
</script>
<template>
    <TVPlayer v-if="effectShowStatus.L1" id="8248" class="tv-wrapper" :urls="urls" :autoplay="true" :loop="true" :placeholder="jingChuPng" :play="!isAnimationPause" @ended="emit('ended')"/>
    <Picture v-else class="tv-wrapper" :src="fallback" />
</template>
<style lang="scss" scoped>
.tv-wrapper {
    position: relative;
    width: 101.5px;
    height: 150.5px;
    left: -22px;
    top: -20px;
}
</style>
