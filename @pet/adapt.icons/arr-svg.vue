<template>
    <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-arr">
        <path
            d="M4 2L7 5.00005L4.00009 8"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
        />
    </svg>
</template>

<style>
:root {
    /* 箭头默认颜色 */
    --adapt-icon-arrow-color: #fe3666;
}
</style>

<style lang="scss" scoped>
.icon-arr {
    color: var(--adapt-icon-arrow-color);
}
</style>
