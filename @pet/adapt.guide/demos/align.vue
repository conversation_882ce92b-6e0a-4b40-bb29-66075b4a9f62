<pet-info lang="json">
{ "title": "更改对齐位置", "description": "整体动画位于组件中间" }
</pet-info>
<script lang="ts" setup>
import Guide from '../index.vue';
</script>

<template>
    <div class="frame-box">
        <div class="frame-text">组件位置 0 * 0</div>
        <Guide class="guide-align-position" />
    </div>
    <div class="cell-title">自定义修改手势大小，位置偏移</div>
    <div class="frame-box">
        <div class="frame-text">组件位置 50px * 100px</div>
        <Guide class="custome-set" />
    </div>
</template>

<style lang="css" scoped>
.frame-box {
    position: relative;
    width: 200px;
    height: 200px;
    background: rgba(0, 0, 0, 0.5);
    padding: 45px 0 0 10px;
    box-sizing: border-box;
    color: #fff;
    font-size: 12px;
}

.frame-text {
    position: absolute;
    top: 30px;
    left: 10px;
}

.guide-align-position {
    --adapt-guide-hand-margin: 0;
    --adapt-guide-hand-box-top: 0px;
    --adapt-guide-hand-box-left: 0px;
}

.cell-title {
    font-size: 16px;
    margin-top: 30px;
}
.custome-set {
    --adapt-guide-hand-box-top: 50px;
    --adapt-guide-hand-box-left: 100px;
}
</style>

<style lang="scss">
.custome-set {
    .hand-main {
        --adapt-guide-hand-width: 41px;
        --adapt-guide-hand-height: 45px;
    }
    .shine {
        --adapt-guide-shine-width: 24px;
        --adapt-guide-shine-position: 80px;
    }
    .shine-in {
        --adapt-guide-shine-in-width: 15px;
        --adapt-guide-shine-in-position: 85px;
    }
    .hand-main {
        --adapt-guide-hand-top: 83px;
        --adapt-guide-hand-left: 96px;
    }
}
</style>
