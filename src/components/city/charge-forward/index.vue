<script setup lang="ts">
import { injectViewInfo } from '@pet/core.mobile/screenDetect';
import Picture from '@pet/quantum.PictureImg';

import HaloArrow from '@/components/city/charge-forward/halo-arrow/index.vue';
import { MapKeyType, StationMapEnum } from '@/components/city/charge-forward/manager/constant';
import SlotWidgetView from '@/components/city/slot/SlotWidgetView.vue';
import { SoundType, useAudioModel } from '@/models/audioModel';
import { useBeginnerGuideModel } from '@/models/city/beginnerGuideModel';
import { useConfigModel } from '@/models/city/configModel';
import { useForwardRushBtnModel } from '@/models/city/forwardRushBtnModel';
import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';
import { useCityHomeModel } from '@/models/city/homeModel';
import { getLocalMapConfig, inHeadless } from '@/utils/ssg';

import haloArrowSkin from './assets/bg/haloArrow.png?preset=modern';
import fallbackIcon from './assets/bg/middle-icon.png';
import LlwardGroup from './llward/LlwardGroup.vue';
import { ChargeForwardManager } from './manager/manager';
import UserIp from './user-ip/index.vue';
import GridReact from '../../charge-forward/gridReact/index.vue';
import GridSkin from '../../charge-forward/skin/GridSkin.vue';

const {
    manager,
    container,
    llwardGrids,
    currentGrid,
    middleIconStyle,
    haloArrowPicOffsetStyle,
    gridSkins,
    mapConfig,
    currentStep,
    stationConfig,
    backgroundStyle,
    gllwardGridLayout,
    gridSkinConfig,
    popoverContainerStyle,
    gCurrentStationKey,
} = useForwardRushGridModel();
const { viewRatio } = injectViewInfo()!;

const { signedStep, llwardAniController } = useForwardRushBtnModel();
const { showGirdIcon, showIP, showHalo } = useBeginnerGuideModel();
const { themeConfig } = useConfigModel();
const { playSound } = useAudioModel();
const canLoad = ref(false);
const { localPrefix, homeData, cityIcon } = useCityHomeModel();

// 地图的key值，根据城市名映射
const mapKey = computed(() => {
    const current =
        gCurrentStationKey.value || getLocalMapConfig(`${localPrefix}forwardRushGridBackGroundStationKey`) || '';
    if (current && current in StationMapEnum) {
        const key = StationMapEnum[current as keyof typeof StationMapEnum];
        return key;
    }
    return MapKeyType.City;
});

onMounted(() => {
    setTimeout(() => {
        canLoad.value = true;
    }, 300);
});

watch(
    [container, currentStep, stationConfig, gllwardGridLayout, mapConfig, gridSkinConfig, viewRatio],
    ([con, currentStep, station, llwards, mapConfig, gridSkinConfig]) => {
        if (con) {
            const { width, height } = con.getBoundingClientRect();
            manager.value = new ChargeForwardManager({
                currentStep,
                signedStep: signedStep.value,
                mapConfig,
                gridllwardConfig: llwards,
                stationConfig: station,
                gridSkinConfig,
                viewSize: {
                    width,
                    height,
                },
                mapKey: mapKey.value,
                localPrefix,
            });
        }
    },
);

watch(
    [homeData, manager],
    ([home, manager]) => {
        if (home && manager) {
            manager.onMoveEnd(() => {
                playSound(SoundType.IP_DOWN);
            });
            // setLocalMapConfig(`${localPrefix}forwardRushGridBackGroundMapKey`, `${mapKey.value ?? MapKeyType.Default}`);
        }
    },
    {
        deep: true,
        immediate: true,
    },
);

/**
 * 用户IP展示规则：
 * 第一天&&已签到&&弹框未打开
 * 第一天&&未签到
 * 非第一天&&未签到
 */
const showIp = computed(() => currentStep.value >= 0 && showIP.value);
</script>
<template>
    <div
        v-if="!inHeadless()"
        ref="container"
        class="charge-forward-container"
        :class="`theme-${mapKey ?? MapKeyType.Default}`"
        :style="{
            ...backgroundStyle,
            'background-color': themeConfig?.backgroundColor ?? '#ffffff',
        }"
    >
        <img v-if="cityIcon" class="middle-icon" :src="cityIcon ?? fallbackIcon" :style="{ ...middleIconStyle }" />
        <div id="game-popover" class="charge-forward-popover" :style="popoverContainerStyle"></div>
        <!-- 展示规则：当天打卡成功后不展示 -->
        <UserIp v-if="showIp" />
        <div v-show="showGirdIcon" class="grid-skin">
            <Picture
                class="halo-arrow"
                :src="haloArrowSkin"
                :style="{
                    ...haloArrowPicOffsetStyle,
                }"
            ></Picture>
        </div>
        <HaloArrow v-if="showHalo && !showGirdIcon" />
        <LlwardGroup v-if="canLoad" :llward="llwardGrids" />
        <template v-for="skin in gridSkins">
            <GridSkin
                v-if="skin"
                :key="skin.step"
                :name="gridSkinConfig?.gridSkinSponsor ?? ''"
                :url="gridSkinConfig?.gridSkinUrl ?? ''"
                :position="skin"
            />
        </template>
        <GridReact
            v-if="currentGrid"
            :position="currentGrid.position"
            :shape="currentGrid.shape"
            :step="currentGrid.step"
        ></GridReact>
        <Teleport to="#app" :disabled="!llwardAniController.show.value">
            <component
                :is="llwardAniController.comp.value"
                :show="llwardAniController.show.value"
                :name="llwardAniController.name.value"
                @ended="llwardAniController.endAni"
            />
        </Teleport>
    </div>
    <div v-else id="ssg-map" class="ssg-map"></div>
</template>
<style scoped lang="scss">
.charge-forward-container {
    width: 100%;
    height: 100%;
    background-size: 100% auto;
    background-repeat: no-repeat;
    overflow: hidden;
    z-index: 91;
}
.charge-forward-popover {
    // background-color: aqua;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 91;
}

.middle-icon {
    position: absolute;
    width: 118px;
    height: 118px;
    top: -59px;
    left: -59px;
    z-index: 49;
}

.grid-skin {
    position: absolute;
    height: 100%;
    width: 414px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    .halo-arrow {
        position: relative;
        width: 348px;
        height: 141px;
        top: 2.5px;
        left: 1px;
    }
}

.ssg-map {
    position: absolute;
    left: 0;
    top: 0;
    background-position: right 0 top var(--charge-forward-background-offset-size);
    background-color: var(--charge-forward-background-ssg-bg-color);
    width: 100%;
    height: 100%;
    background-size: 100% auto;
    background-repeat: repeat-y;
    overflow: hidden;
    z-index: 91;
}

.theme-default {
    @include bg('./assets/bg/cityMap.png');
}
.theme-bd-chongQing {
    @include bg('./assets/bg/bd-chongQing.jpg');
}
.theme-city {
    @include bg('./assets/bg/cityMap.png');
}
.theme-bd-shenYang {
    @include bg('./assets/bg/bd-shenYang.jpg');
}
</style>
