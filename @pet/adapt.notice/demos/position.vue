<pet-info lang="json">
{ "title": "通知位置", "description": "" }
</pet-info>
<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import { ref, nextTick } from 'vue-demi';

import NoticeCard from '../index.vue';

const show = ref(false);
const createNotice = ref(false);

function noticeLeave() {
    createNotice.value = false;
}

const eventNotice = {
    avatar: '',
    figure: 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '摇一摇PK赢红包',
    desc: '和附近朋友一起摇一摇赢红包',
    buttonText: '去赢钱',
};

const userNotice = {
    avatar: 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    figure: '',
    title: '快手小助手',
    desc: '为你助力成功快去看看吧',
    buttonText: '去看看',
};

const noticeInfo = ref(userNotice);

let i = 0;

function handleClick() {
    i++;
    noticeInfo.value = i % 2 === 1 ? userNotice : eventNotice;
    createNotice.value = true;
    nextTick(() => {
        show.value = true;
    });
}
</script>

<template>
    <div>
        <NoticeCard
            v-if="createNotice"
            v-model:show="show"
            class="my-notice"
            position="bottom"
            ani-type="drawer-bottom"
            :slide-remove="false"
            :avatar="noticeInfo.avatar"
            :figure="noticeInfo.figure"
            :title="noticeInfo.title"
            :desc="noticeInfo.desc"
            :button-text="noticeInfo.buttonText"
            @after-leave="noticeLeave"
        >
        </NoticeCard>
        <Button class="button" @click="handleClick">发送通知</Button>
    </div>
</template>

<style lang="stylus" scoped>
@import '@pet/adapt.reset/reset.css';
/* 这里的距离请不需要加上安全区的距离 */
.my-notice
    --adapt-notice-card-edge-gap: 40px
</style>
