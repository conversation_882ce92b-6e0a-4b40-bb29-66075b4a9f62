<script setup lang="ts"></script>

<template>
    <svg class="icon-close" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28ZM16.8281 9.75691C17.2186 9.36639 17.8517 9.36639 18.2423 9.75691C18.6328 10.1474 18.6328 10.7806 18.2423 11.1711L15.4137 13.9997L18.2421 16.828C18.6326 17.2186 18.6326 17.8517 18.2421 18.2423C17.8516 18.6328 17.2184 18.6328 16.8279 18.2423L13.9995 15.4139L11.1712 18.2422C10.7807 18.6327 10.1475 18.6327 9.75698 18.2422C9.36646 17.8517 9.36646 17.2185 9.75698 16.828L12.5853 13.9997L9.75683 11.1712C9.36631 10.7807 9.36631 10.1475 9.75683 9.75698C10.1474 9.36646 10.7805 9.36646 11.171 9.75698L13.9995 12.5854L16.8281 9.75691Z"
            fill="currentColor"
        />
    </svg>
</template>

<style lang="scss" scoped>
.icon-close {
    color: var(--adapt-packet-close-btn-color);
    width: 29px;
    height: 29px;
    padding: 1px;
}
</style>
