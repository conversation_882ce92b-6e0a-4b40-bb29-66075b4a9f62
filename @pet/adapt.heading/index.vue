<script lang="ts">
export default {
    name: 'AdaptHeading',
};
</script>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue-demi';
import { paint } from '@vision/runtime';

interface Props {
    /**
     * 主标题
     */
    title?: string | string[];
    /**
     * 副（子）标题
     */
    subTitle?: string;
    /**
     * 主标题切图
     */
    titleImg?: string;
    /**
     * 副（子）题切图
     */
    subTitleImg?: string;
    /**
     * 是否展示默认lottie
     */
    showLottie?: boolean;
    /**
     * 是否展示背光
     */
    showBgShine?: boolean;
    /**
     * 是否展示入场动效
     */
    showEnterActive?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
    title: '',
    subTitle: '',
    showLottie: false,
    showBgShine: false,
    showEnterActive: false,
});

const slots = defineSlots<{
    lottie?: ((props: {}) => any) | undefined;
    title?: ((props: {}) => any) | undefined;
    subTitle?: ((props: {}) => any) | undefined;
}>();

// 主副标题任一展示图片则都不展示标题文案
const showTitlePic = computed(() => !!(props.subTitleImg || props.titleImg));
// 是否有子标题
const hasSubTitle = computed(() => !!(props.subTitleImg || props.subTitle || slots.subTitle));
// 将主标题初始化为数组形式
const titleArray = computed(() => {
    if (!!Array.isArray(props.title)) {
        if (props.title.length === 1) {
            return ['', ...props.title];
        } else {
            return props.title;
        }
    } else {
        return ['', props.title];
    }
});
const getSubTitleBg = computed(() => {
    return props.subTitleImg ? { backgroundImage: `url(${props.subTitleImg})` } : {};
});
const lottie = ref<HTMLElement | null>(null);

watch(lottie, async (val) => {
    if (!val) {
        return;
    }
    const effectData = import('./head-light-lottie/config').then((mod) => mod.default);
    const effect = await effectData;
    const effectInstance = paint(val, effect, {
        options: {
            ratio: 2,
            // 为了更好控制播放状态，不使用内置的自动播放
            autoplay: false,
            // 同上
            loop: true,
        },
    });
    effectInstance.play();
});
</script>

<template>
    <div class="wrapper">
        <Transition :name="props.showEnterActive ? 'title' : ''" appear>
            <div class="title-area">
                <!-- 默认 title 背景 lottie -->
                <div
                    v-if="props.showLottie"
                    ref="lottie"
                    :class="['head-lottie', hasSubTitle && 'sub-title-special-lottie']"
                ></div>
                <!-- lottie 的 slot -->
                <div v-if="$slots.lottie" :class="['head-lottie', hasSubTitle && 'sub-title-special-lottie']">
                    <slot name="lottie" />
                </div>
                <!-- 主标题 文案 -->
                <div
                    v-if="!showTitlePic && props.title"
                    :class="[
                        'title',
                        hasSubTitle && 'top',
                        titleArray[0]?.length ? 'two-line-title' : 'one-line-title',
                    ]"
                >
                    <div v-if="titleArray[0]" class="title-item">{{ titleArray[0] }}</div>
                    <div v-if="titleArray[1]" class="title-item second-line-title">
                        {{ titleArray[1] }}
                    </div>
                    <div
                        v-if="props.showBgShine"
                        class="bg-shine"
                        :style="{ top: titleArray[0] ? '76%' : '55%' }"
                    ></div>
                </div>
                <!-- 主标题切图 （如果title使用切图 则切图优先级高于字符串文案） -->
                <div
                    v-if="showTitlePic && props.titleImg"
                    :class="['title-img', hasSubTitle && 'top']"
                    :style="{ backgroundImage: `url(${props.titleImg})` }"
                ></div>
                <!-- title 的 slot -->
                <div v-if="$slots.title" :class="['title', $slots.subTitle && 'top']">
                    <slot name="title" />
                </div>
                <!-- 副（子）标题文案 -->
                <div v-if="!showTitlePic && props.subTitle" class="sub-title">
                    {{ props.subTitle }}
                </div>
                <!-- 副（子）标题切图 -->
                <div v-if="showTitlePic && props.subTitleImg" class="sub-title-img" :style="getSubTitleBg"></div>
                <!-- 副（子）标题 slot -->
                <div v-if="$slots.subTitle" class="sub-title">
                    <slot name="subTitle" />
                </div>
            </div>
        </Transition>
    </div>
</template>
<style>
:root {
    /** 主标题宽 */
    --adapt-heading-title-width: 340px;
    /** 主标题高 */
    --adapt-heading-title-height: 76px;
    /** 主标题行高 */
    --adapt-heading-title-line-height: 36px;
    /** 一行主标题的上 margin */
    --adapt-heading-one-line-title-margin-top: 0px;
    /** 两行主标题的上 margin */
    --adapt-heading-two-line-title-margin-top: 0px;
    /* 两行标题之间的margin */
    --adapt-heading-two-line-title-between-margin: 4px;
    /** 主标题字体颜色 */
    --adapt-heading-title-font-color: linear-gradient(235.64deg, #fffaf0 36.75%, #fffbd5 69.96%);
    /** 主标题字体大小 */
    --adapt-heading-title-font-size: 30px;
    /** 主标题字重 */
    --adapt-heading-title-font-weight: 400;
    /** 主标题字体风格 */
    --adapt-heading-title-font-style: skewX(0deg);
    /** 主标题字间距 */
    --adapt-heading-title-letter-spacing: 2px;
    /** 副（子）标题宽 */
    --adapt-heading-sub-title-width: 323px;
    /** 副（子）标题高 */
    --adapt-heading-sub-title-height: 18px;
    /** 副（子）标题字体颜色 */
    --adapt-heading-sub-title-font-color: rgba(255, 242, 219, 0.8);
    /** 副（子）标题字体大小 */
    --adapt-heading-sub-title-font-size: 14px;
    /** 副（子）标题字重 */
    --adapt-heading-sub-title-font-weight: 400;
    /** 副标题与主标题的距离 */
    --adapt-heading-title-margin-bottom: 10px;
    /** 副（子）标题字体风格 */
    --adapt-heading-sub-title-font-style: skewX(0);
    /** 背景lottie的下外边距边界 */
    --adapt-heading-bg-lottie-bottom: -44px;
    /** 背景lottie的下外边距边界 */
    --adapt-heading-bg-lottie-special-bottom: -16px;
    /** 背景lottie在文档中的定位方式 */
    --adapt-heading-bg-lottie-position: absolute;
    /** 背景lottie的高 */
    --adapt-heading-bg-height: 142px;
    /** 背景lottie的宽 */
    --adapt-heading-bg-width: 414px;
    /** 背光的高 */
    --adapt-heading-bg-shine-height: 12px;
    /** 背光的透明度 */
    --adapt-heading-bg-shine-opacity: 0.8;
    /** 背光的层级 */
    --adapt-heading-bg-shine-z-index: -1;
    /** 背光的颜色 */
    --adapt-heading-bg-shine-background: linear-gradient(
        90deg,
        rgba(255, 220, 159, 0.4) 0%,
        rgba(255, 220, 159, 0.8) 28.03%,
        rgba(255, 220, 159, 0.8) 70.59%,
        rgba(255, 220, 159, 0.4) 100%
    );
    /** 背光的模糊度 */
    --adapt-heading-bg-shine-filter-blur: 8px;
    --adapt-heading-title-background-size: 100%;
}
</style>
<style lang="scss" scoped>
.wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    /* stylelint-disable scale-unlimited/declaration-strict-value */
    background: no-repeat center/100%;
    /* stylelint-enable scale-unlimited/declaration-strict-value */
    width: 100%;

    .title-area {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        flex-direction: column;

        .head-lottie {
            position: var(--adapt-heading-bg-lottie-position);
            bottom: var(--adapt-heading-bg-lottie-bottom);
            width: var(--adapt-heading-bg-width);
            height: var(--adapt-heading-bg-height);
            overflow: hidden;
        }
        .sub-title-special-lottie {
            bottom: var(--adapt-heading-bg-lottie-special-bottom);
        }

        .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-direction: column-reverse;
            line-height: var(--adapt-heading-title-line-height);
            font-size: var(--adapt-heading-title-font-size);
            font-family: var(--adapt-heading-title-font-family);
            transform: var(--adapt-heading-title-font-style);
            text-align: center;
            letter-spacing: var(--adapt-heading-title-letter-spacing);
            overflow: visible;
            margin-top: var(--adapt-heading-one-line-title-margin-top);
        }
        .one-line-title {
            .title-item {
                /* 设计兜底策略，正常不应该出现截字策略 */
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .two-line-title {
            .title-item {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        /* stylelint-disable-next-line no-descending-specificity */
        .title-item {
            position: relative;
            max-width: var(--adapt-heading-title-width);
            background: var(--adapt-heading-title-font-color);
            background-clip: text;
            -webkit-background-clip: text;
            /* stylelint-disable scale-unlimited/declaration-strict-value */
            -webkit-text-fill-color: transparent;
            /* stylelint-enable scale-unlimited/declaration-strict-value */
            /* 解决clip的溢出 */
            /* 去掉标题可能出现的细线 */
            clip-path: inset(0.7px);
            background-size: var(--adapt-heading-title-background-size);
            background-repeat: no-repeat;
        }
        .bg-shine {
            content: '';
            position: absolute;
            left: 50%;
            width: 100%;
            height: var(--adapt-heading-bg-shine-height);
            background: var(--adapt-heading-bg-shine-background);
            transform: translate(-50%, -50%);
            filter: blur(var(--adapt-heading-bg-shine-filter-blur));
            opacity: var(--adapt-heading-bg-shine-opacity);
            overflow: visible;
            z-index: var(--adapt-heading-bg-shine-z-index);
        }
        /* stylelint-disable-next-line no-duplicate-selectors */
        .two-line-title {
            flex-direction: column;
            margin-top: var(--adapt-heading-two-line-title-margin-top);
        }
        .second-line-title {
            margin-top: var(--adapt-heading-two-line-title-between-margin);
        }
        .sub-title {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: var(--adapt-heading-sub-title-width);
            font-size: var(--adapt-heading-sub-title-font-size);
            font-family: var(--adapt-heading-sub-title-font-family);
            transform: var(--adapt-heading-sub-title-font-style);
            font-weight: var(--adapt-heading-sub-title-font-weight);
            text-align: center;
            color: var(--adapt-heading-sub-title-font-color);
        }

        .title-img {
            width: var(--adapt-heading-title-width);
            height: var(--adapt-heading-title-height);
            /* stylelint-disable scale-unlimited/declaration-strict-value */
            background: no-repeat center/100%;
            /* stylelint-enable scale-unlimited/declaration-strict-value */
        }
        .sub-title-img {
            width: var(--adapt-heading-sub-title-width);
            height: var(--adapt-heading-sub-title-height);
            /* stylelint-disable scale-unlimited/declaration-strict-value */
            background: no-repeat center/100%;
            /* stylelint-enable scale-unlimited/declaration-strict-value */
        }
        .top {
            margin-bottom: var(--adapt-heading-title-margin-bottom);
        }
    }
}

.title-enter-active {
    transform-origin: 207px 71px;
    animation: header-show 0.4s 0s linear;
}

@keyframes header-show {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 0.45;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}
</style>
