import type { CreatePopupTaskType } from '@pet/adapt.task-queue/task-modules/popupTask';

import GameStaticPopup from '@/components/popups/static-popup/index.vue';
import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

import { type PopupBtnClick, type PopupDataType, type PopupTaskConfig } from './types';
import { filterNullValues } from '../common';

export const GameModalTypes = ['LS_COMMON_STATIC_POPUP_DIVERSION'];

const getTitle = (title: string | string[]) => {
    if (Array.isArray(title)) {
        return title;
    }
    if (title?.includes('\n')) {
        return title.split('\n');
    }
    return title;
};

export function gameModalTransform(
    popupData: any,
    createPopupTask: CreatePopupTaskType,
    config?: PopupTaskConfig,
    onBtnClick?: PopupBtnClick,
) {
    const type: 'dialog' | 'layer' = 'layer';
    const data: any = filterNullValues({
        popupType: popupData?.popupType ?? '',
        type,
        sponsorLogo: popupData?.sponsorLogo,
        sponsorText: popupData?.sponsorText,
        title: getTitle(popupData?.title ?? ''),
        subTitle: popupData?.subTitle ?? '',
        desc: '',
        message: '',
        icon: popupData?.icon,
        mainButton: popupData?.mainButton?.linkText,
        btnClick: onBtnClick,
        // cloneFlyToTarget: 'wallet',
        clonePilotProps: {
            duration: 667,
            bezier: [0, -147, -98.25, 4.365],
            timeBezier: [0.33, 0.0, 0.67, 1.0],
            style: {
                '--adapt-bezier-fly-fade-start': '1',
                '--adapt-bezier-fly-fade-end': '1',
                '--adapt-bezier-fly-scale-end': '0.1',
            },
        },
    });

    // 创建弹窗任务
    const task = createPopupTask({
        component: GameStaticPopup,
        data,
        options: config,
    });

    return task;
}
