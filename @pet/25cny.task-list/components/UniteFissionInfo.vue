<script lang="ts" setup>
defineProps<{ rwdList: { desc: string; rwdNum: string; rwdUnit: string }[] }>();
</script>

<template>
    <div class="fission-container">
        <div v-for="(item, index) in rwdList" class="item">
            <div v-if="item.rwdUnit.length < 3" class="normal">
                <div class="rwd">
                    <span class="rwd-num">{{ item.rwdNum }}</span>
                    <span class="rwd-unit">{{ item.rwdUnit }}</span>
                </div>
                <div class="desc">{{ item.desc }}</div>
            </div>
            <div v-else class="special">
                <div class="special-rwd-num">{{ item.rwdNum }}</div>
                <div class="special-rwd-unit">{{ item.rwdUnit }}</div>
                <div class="desc">{{ item.desc }}</div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.fission-container {
    display: flex;
    justify-content: center;
    margin-left: auto;
}
.item {
    width: 98px;
    height: 80px;
    background: rgba(255, 235, 235, 0.5);
    margin-left: 9px;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:first-child {
        margin-left: 0;
    }
}
.desc {
    color: rgba(85, 0, 0, 0.5);
    font-size: 12px;
    margin-top: 6px;
    text-align: center;
}
.rwd {
    color: rgba(255, 32, 32, 1);
    font-family: KuaiYuanHuiTi;
    height: 30px;
    text-align: center;
    &-unit {
        font-size: 13px;
        transform: skew(-5deg);
    }
    &-num {
        font-size: 21px;
        display: inline-block;
        transform: skew(-5deg);
    }
}
.normal {
    padding-top: 14px;
}
.special {
    display: flex;
    flex-direction: column;
    padding-top: 6px;
    &-rwd-num {
        color: rgba(255, 32, 32, 1);
        font-family: KuaiYuanHuiTi;
        font-size: 21px;
        transform: skew(-5deg);
        text-align: center;
        line-height: 30px;
    }
    &-rwd-unit {
        color: #ff2020;
        text-align: center;
        font-size: 11px;
        font-weight: 600;
        line-height: 14px;
        margin-top: -3px;
    }
    .desc {
        margin-top: 4px;
    }
}
</style>
