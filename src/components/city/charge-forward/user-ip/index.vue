<script lang="ts" setup>
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';

import { SoundType, useAudioModel } from '@/models/audioModel';
import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';

import Downgrade from './downgrade.vue';
import defaultAvatar from '../../../charge-forward/assets/ip/default-avatar.png';
import { IpStatusAni } from '../../../charge-forward/user-ip/types';
import IPEffect from '../assets/effects/ipAnim/EffectIndex.vue';
import { RenderZIndex } from '../manager/constant';

const { moving, gUserInfo, ipStyle, ipOnStation, ipOnBirthStation, isIpLowerThanMidIcon } = useForwardRushGridModel();
const { playSound } = useAudioModel();
const { effectShowStatus } = useDowngradeLevel();

const downgrade = computed(() => {
    // return true;
    return !effectShowStatus.value.L1;
});
const IPZIndex = computed(() => {
    return isIpLowerThanMidIcon.value ? RenderZIndex.LowerIP : RenderZIndex.HigherIP;
});
const birthEnd = ref(false);

const action = ref(IpStatusAni.birth);

const avatarUrl = computed(() => {
    return !!gUserInfo.value?.headUrl ? gUserInfo.value.headUrl : defaultAvatar;
});

const onStageEnded = (stage: IpStatusAni) => {
    console.log('stage 🎈🎈onStageEnded', stage);
    if (stage === IpStatusAni.birth || stage === IpStatusAni.afterJump) {
        birthEnd.value = true;
        action.value =
            ipOnStation.value || ipOnBirthStation.value ? IpStatusAni.stationWaiting : IpStatusAni.normalWaiting;
    }
};

watch(
    () => moving.value,
    (val) => {
        if (val) {
            action.value = IpStatusAni.beforeJump;
            playSound(SoundType.IP_READY);
        }
    },
    {
        immediate: true,
    },
);
</script>
<template>
    <div id="ip" class="ip ip-container" :class="{ downgrade }" :style="{ ...ipStyle, zIndex: IPZIndex }">
        <div v-if="!downgrade">
            <IPEffect class="ip-effect" :action="action" :avatar-url="avatarUrl" @stageEnded="onStageEnded" />
        </div>
        <Downgrade v-else class="ip-downgrade" :birthed="birthEnd" />
    </div>
</template>
<style lang="scss" scoped>
.ip-container {
    // width: 67px;
    // height: 76px;
    width: 50px;
    height: 57px;
    position: absolute;
    // 点击事件透传
    pointer-events: none;
    top: -84px;
    left: -54px;
    &.showOnSide {
        top: -45px;
        left: -24px;
    }
    &.downgrade {
        position: absolute;
        top: -20px;
        left: -25px;
        &.showOnSide {
            top: 45px;
            left: 10px;
        }
    }
    .ip-effect {
        width: 111px;
        height: 176px;
    }
}
</style>
