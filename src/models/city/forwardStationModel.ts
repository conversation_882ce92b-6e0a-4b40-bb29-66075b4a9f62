import { createUseModel } from '@gundam/model';
import { whenever } from '@vueuse/core';

import type { CityChessStationView } from '@/services/open-api-docs/home/<USER>/schemas';

import { useCityHomeModel } from './homeModel';

export const useForwardStationModel = createUseModel(() => {
    const { homeData } = useCityHomeModel();

    const stationInfo = ref<CityChessStationView | null>(null);

    const gCurrentStation = ref<CityChessStationView | null>(null); // 今天的

    const resetStation = () => {
        gCurrentStation.value = null;
        stationInfo.value = null;
    };

    whenever(
        homeData,
        (val) => {
            resetStation();
            gCurrentStation.value = val?.chessboard?.station ?? null;
        },
        {
            immediate: true,
            deep: true,
        },
    );

    return {
        gCurrentStation,
    };
});
