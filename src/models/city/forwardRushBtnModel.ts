import { createModel, useModel } from '@gundam/model';
import type { Task } from '@pet/adapt.queue-api/main/TaskQueue';
import { nativeCloud } from '@pet/yau.cloud-native';
import { isNotNil, sleep } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';
import { whenever } from '@vueuse/core';

import { useLlwardAnimation, type LlwardAniName } from '@/models/city/llwardAnimation';
import { useNavigate } from '@/models/utils/useNavigate';
import {
    type CityChessMoveResultView,
    type LuckRushSudokuView,
    PopupType,
} from '@/services/open-api-docs/home/<USER>/schemas';

import { ForwardRushBtnStatus, useConfigModel, type IMainButtonConfig } from './configModel';
import { useForwardRushGridModel } from './forwardRushGridModel';
import { useSnapShotModel } from './snapShotModel';
import { forwardRushApiModel } from '../api/city/forwardRushApiModel';
import { useCityHomeApiModel } from '../api/city/homeApiModel';
import { TaskType, usePopupModel, QUEUE_TAGS_TYPE } from '../popup.model';

export const forwardRushBtnModel = createModel(({ getModelInstance }) => {
    // 是否允许点击向前冲
    const enableForwardRush = ref(false);
    const forwardRushTip = ref<string>('');
    const showGridPopup = ref(false);
    const { blockingRefreshHandler, refreshHome, data: homeData } = useCityHomeApiModel();
    const { kconfConfig } = useConfigModel();
    // 站点奖励动画
    const llwardAniController = useLlwardAnimation();
    const { goForward } = useModel(forwardRushApiModel);
    const { signed, currentStep, expectTotalStep, accountModel } = useSnapShotModel();
    const { manager, showStationBubble, afterSelect, showBubbleAndAniAfterSelect, firstSigned } =
        useForwardRushGridModel();
    const { openPopup, openSummerPopup, addEventTask } = usePopupModel();
    const { gotoHomePage } = useNavigate();
    const { log } = useCaptureDebugLog('cityForwardRushBtnModel');

    /** 主按钮的kconf配置 */
    const mainButtonConfig = computed<IMainButtonConfig>(() => kconfConfig.value?.mainButtonConfig ?? {});

    /** 主按钮状态 */
    const mainButtonStatus = computed(() => {
        if (signed.value) {
            return ForwardRushBtnStatus.SIGNED;
        }
        return ForwardRushBtnStatus.PROCESSING;
    });

    /** 主按钮和气泡信息 */
    const buttonAndBubbleInfo = computed(() => {
        const data = mainButtonConfig.value[mainButtonStatus.value] ?? {};
        return {
            /** 主按钮文案 */
            buttonText: data.buttonText ?? '',
            /** 主按钮副文案 */
            buttonSubText: data.buttonSubText ?? '',
            /** 气泡第一行文案 */
            bubbleText: data.bubbleText ?? '',
            /** 气泡第二行文案 */
            bubbleSubText: data.bubbleSubText ?? '',
            /** 气泡展示时长 */
            duration: data.duration ?? 3000,
        };
    });

    /* 挑战任务弹窗，用于任务完成后关闭 */
    const taskPopup = ref<Task | null>();
    const llwardGridName = computed(() => {
        return manager.value?.renderMap.ip.llwardGridName ?? null;
    });
    const palyGridFlyAni = async () => {
        // 执行金币飞入动画;
        if (llwardGridName.value) {
            llwardAniController.startAni(llwardGridName.value as LlwardAniName);
            await sleep(1000);
        }
        return;
    };
    // 站点的step
    const signedStep = computed(() => {
        const steps = [expectTotalStep.value];
        return steps;
    });
    // 倒计时结束自动跳转主会场(卷轴)
    const autoGoToHomePage = () => {
        nativeCloud.show();
        gotoHomePage();
        setTimeout(() => {
            nativeCloud.hide();
        }, 2000);
    };
    async function handleForwardRushProgress(data: CityChessMoveResultView) {
        /** 更新进度 */
        if (isNotNil(data?.progress?.currentStep) && isNotNil(data?.progress?.expectTotalStep)) {
            currentStep.value = data?.progress.currentStep;
            expectTotalStep.value = data?.progress.expectTotalStep;
            signed.value = data?.progress?.signed ?? false;
            accountModel.value = data?.accountModel;
        }
        return;
    }

    // 挑战任务下线花字
    const openChallengeNullBanner = async () => {
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerText.vue'),
            data: {
                text: '太幸运了',
                subText: '本次挑战直接成功',
                type: 'challenge',
            },
        });
        await task.end;
    };

    /**
     * 处理点击按钮触发向前冲的逻辑
     * @returns
     */

    // eslint-disable-next-line sonarjs/cognitive-complexity
    async function handleForwardRush(task?: string) {
        if (!enableForwardRush.value) {
            return;
        }
        log(`[city forward rush]开始向前冲`);
        blockingRefreshHandler(true);
        // 向前冲加锁
        enableForwardRush.value = false;
        const { success, data, needRefresh } = await goForward();

        if (!success) {
            // 释放主接口刷新
            blockingRefreshHandler(false);
            // 向前冲解锁
            enableForwardRush.value = true;
            // 后端返回异常
            needRefresh && refreshHome();
            log(`[city forward rush]move接口请求异常, ${JSON.stringify(data ?? {})}`);
            return;
        }
        // 向前冲弹窗不为空
        if (data?.luckRushSudokuView && data?.luckRushSudokuView?.length > 0) {
            // 当前向前冲结果弹窗
            const currentPopup = data?.luckRushSudokuView[0] as LuckRushSudokuView;
            const popupType = currentPopup?.popupType;
            // 最终格子奖励：LS_ATTRACTION_TICKETS 景点门票，LS_CITY_PHYSICAL_LLREWD  旅游资格，LS_LOCAL_LIFE_COUPON 本地生活优惠券，LS_CITY_PAGE_BLESS  祝福语，LS_CITY_PROFILE_PENDANT 头像挂件
            const finalRewardPopupType = [
                PopupType.LS_ATTRACTION_TICKETS,
                PopupType.LS_CITY_PHYSICAL_LLREWD,
                PopupType.LS_LOCAL_LIFE_COUPON,
                PopupType.LS_CITY_PAGE_BLESS,
                PopupType.LS_CITY_PROFILE_PENDANT,
            ];
            // 释放主接口刷新
            blockingRefreshHandler(false);
            if (currentPopup?.hidePopup) {
                // 金币奖励
                if (popupType === PopupType.LS_LLCN_LLREWD) {
                    await manager.value?.moveTo(data?.progress?.currentStep ?? 0);
                    await palyGridFlyAni();
                    enableForwardRush.value = true;
                    // 更新进度
                    await handleForwardRushProgress(data);
                    return;
                }
            }

            // 向前冲结果弹窗
            if (finalRewardPopupType.includes(popupType)) {
                const isFailedPopup = popupType === PopupType.LS_CITY_PAGE_BLESS;
                await manager.value?.moveTo(data?.progress?.currentStep ?? 0);
                // 最终格子奖励 打开最终奖励弹窗
                const finalForwardPop = openSummerPopup(currentPopup, {
                    taskType: isFailedPopup ? TaskType?.PASSIVE_POPUP : TaskType.ACTIVE_POPUP,
                });
                finalForwardPop?.end.finally(async () => {
                    console.log('最终格子奖励');
                    await handleForwardRushProgress(data);
                    // 最终大奖飞入动效
                    //await palyGridFlyAni();
                    refreshHome();
                    enableForwardRush.value = true;
                });
                return;
            }
            if (popupType === PopupType.LS_LLCH_LLREWD || popupType === PopupType.LLRP_CITY_CHESS_LLCH) {
                await manager.value?.moveTo(data?.progress?.currentStep ?? 0);
                // 红包奖励 打开红包奖励弹窗
                const normalRewardPop = openSummerPopup(currentPopup, {
                    taskType: TaskType.ACTIVE_POPUP,
                });
                normalRewardPop?.end.finally(async () => {
                    console.log('红包奖励');
                    await palyGridFlyAni();
                    await handleForwardRushProgress(data);
                    refreshHome();
                    enableForwardRush.value = true;
                });
                return;
            }
            // 挑战格子
            if (popupType === PopupType.GRID_COMMON_TASK_LLCN) {
                // 打开挑战格子弹窗
                taskPopup.value = openSummerPopup(currentPopup, {
                    taskType: TaskType.GRID_TASK_POPUP, // TIPS: 阻塞性弹窗
                });
                taskPopup.value?.end.finally(() => {
                    taskPopup.value = null;
                    enableForwardRush.value = true;
                });
                return;
            }
            enableForwardRush.value = true;
        } else {
            log(`[city forward rush]move无弹窗返回, ${JSON.stringify(data ?? {})}`);
            if (data) {
                if (data?.progress?.gridTaskDegrade) {
                    // 展示任务下线提醒
                    addEventTask({
                        event: openChallengeNullBanner,
                        options: {
                            priority: -1,
                            name: 'taskNullBanner',
                            log: '挑战任务下线后，展示助力成功花字',
                            queueTags: [QUEUE_TAGS_TYPE.POPUP, QUEUE_TAGS_TYPE.SHEET, QUEUE_TAGS_TYPE.TASK_SHEET_POPUP],
                        },
                    });
                }
                await manager.value?.moveTo(data?.progress?.currentStep ?? 0);
                // 任务格子且非降级的情况，播放金币飞入动效
                if (task === 'gridTask' && !data?.progress?.gridTaskDegrade) {
                    await palyGridFlyAni();
                }
                await handleForwardRushProgress(data);
            }
            enableForwardRush.value = true;
        }
    }

    /**
     * 点击向前冲按钮
     */

    const handleForwardRushBtnClick = async () => {
        if (!enableForwardRush.value) {
            console.log('向前冲按钮被锁定，无法点击');
            return;
        }
        // 一圈跳完状态
        if (mainButtonStatus.value === ForwardRushBtnStatus.SIGNED) {
            // 回主会场
            gotoHomePage();
            return;
        } else {
            // 标准向前冲状态
            // 当前最后一个格子没有完成
            if (!signed.value) {
                await handleForwardRush();
                return;
            }
            return;
        }
    };
    // 关闭挑战格子__任务弹窗
    const closeGridTask = async () => {
        const task = taskPopup.value;
        if (!task) {
            log('[GridTask] 任务弹窗 不存在');
            return false;
        }
        task.triggerDestroy();
        await task.end;
    };
    // 首次step为0时，ip从初始位置自动向前挑一步
    const autoForwardRush = async () => {
        // if (currentStep.value === 0) {
        addEventTask({
            event: handleForwardRush,
            options: {
                priority: -1,
                name: 'autoForwardRush',
                log: '用户首次进入页面初始位置，自动跳一步',
                queueTags: [QUEUE_TAGS_TYPE.POPUP, QUEUE_TAGS_TYPE.SHEET, QUEUE_TAGS_TYPE.TASK_SHEET_POPUP],
            },
        });
        // } else {
        //     log(
        //         `[city forward autoForwardRush]自动跳一步失败， 当前步数：${currentStep.value} 按钮可点击：${enableForwardRush.value}`,
        //     );
        // }
    };
    // 记录主接口是否第一次已经返回
    let hasHomeData = false;
    whenever(
        () => homeData.value,
        () => {
            if (!hasHomeData && homeData.value) {
                // 当主接口返回时，可点击
                enableForwardRush.value = true;
                hasHomeData = true; // 设置标志位，确保回调函数只执行一次
            }
        },
    );

    return {
        enableForwardRush,
        forwardRushTip,
        mainButtonStatus,
        buttonAndBubbleInfo,
        llwardAniController,
        showStationBubble,
        afterSelect,
        showBubbleAndAniAfterSelect,
        showGridPopup,
        handleForwardRushBtnClick,
        handleForwardRush,
        closeGridTask,
        signedStep,
        autoGoToHomePage,
        autoForwardRush,
    };
});

export const useForwardRushBtnModel = () => useModel(forwardRushBtnModel);
