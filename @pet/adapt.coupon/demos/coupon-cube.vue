<pet-info lang="json">
{ "title": "方形卡券", "description": "纵向的方形卡券", "priority": 6 }
</pet-info>
<script setup lang="ts">
import CouponCube from '../cube.vue';
import type { PlatformCouponData } from '../types';

const displayTypeNumber: PlatformCouponData = {
    displayType: 'number',
    branding: false,
    name: '日常测试',
    icon: '',
    desc: '全平台可用（除少数商品外）',
    forwardUrl:
        'https://app.kwaixiaodian.com/page/kwaishop-pub-couponuse/v20211111?couponTargetName=平台通用券&layoutType=4&enableWK=1&__launch_options__=%7B%22enableLoading%22%3Atrue%2C%22enableProcess%22%3Afalse%7D&breakupTime=0&webview_bgcolor=%23f80031&breakupTime=0&hyId=couponus&entry_src=cnycoupongezi&source=cnycoupongezi&couponId=6201481043&couponTargetType=3&doorPrice=1000&reducePrice=100&couponStartTime=1660201848196&couponEndTime=1660205448196&couponSourceType=1',
    forwardText: '立即使用',
    status: 'unused',
    value: 999,
    valueText: '1',
    valueDesc: '满10元可用',
    expiredTime: 1660205448196,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

const displayTypeDiscount: PlatformCouponData = {
    displayType: 'discount',
    branding: false,
    name: '优惠券',
    icon: 'https://kcdn.staging.kuaishou.com/kos/nlav100106/encourage-1658904825620-pvIssv.jpg',
    desc: '仅限新客使用',
    forwardUrl: 'https://www.baidu.com/zzzzzzzzzz',
    forwardText: '立即使用',
    status: 'used',
    value: 5.0,
    valueText: '5.0',
    valueDesc: '无门槛',
    expiredTime: 1661928815415,
    prizeId: 0,
    prizeType: '',
    shopCouponId: '',
    productItemId: 0,
};

function handleClick(info: PlatformCouponData) {
    console.log('coupon-click');
    console.info(info);
}
</script>

<template>
    <div>
        <CouponCube
            :info="displayTypeNumber"
            tag="尊享礼包"
            class="coupon-item"
            @coupon-click="handleClick"
        ></CouponCube>
        <CouponCube :info="displayTypeDiscount" tag="尊享礼包" class="coupon-item"></CouponCube>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.coupon-item {
    margin: 0 4px;
}
</style>
