<script lang="ts">
export default {
    name: 'AdaptGuide',
};
</script>
<script lang="ts" setup>
import { computed, ref } from 'vue-demi';

interface GuideHandProps {
    /**
     * 手势方向
     */
    reverse?: boolean;
    /**
     * 重复次数，不传无限循环
     */
    repeat?: number;
    /**
     * 播放动效
     */
    play?: boolean;
}

const props = withDefaults(defineProps<GuideHandProps>(), {
    reverse: false,
    play: true,
    alignPoint: 'edge',
});

const animationRepeat = computed(() => {
    const repeat = props.repeat ?? -1;
    return repeat >= 0
        ? {
              'animation-iteration-count': repeat,
          }
        : '';
});

const emit = defineEmits<{
    /**
     * 手动画结束
     */
    (event: 'end'): void;
}>();

const isImagesLoaded = ref(false);
const loadedImages = ref(0);

const handleImageLoad = () => {
    loadedImages.value++;
    if (loadedImages.value === 2) {
        isImagesLoaded.value = true;
    }
};

const handStatus = computed(() => [props.reverse && 'reverse', props.play && isImagesLoaded.value && 'is-play']);
</script>

<template>
    <div class="hand is-edge" :class="[handStatus]">
        <img
            src="./assets/shine.png"
            class="shine"
            :style="animationRepeat"
            alt=""
            @load="handleImageLoad"
            @error="handleImageLoad"
        />
        <img
            src="./assets/hand.png"
            class="hand-main"
            :style="animationRepeat"
            alt=""
            @load="handleImageLoad"
            @animationend="emit('end')"
            @error="handleImageLoad"
        />
    </div>
</template>
<style>
:root {
    /* 动效模块大小 */
    --adapt-guide-hand-box-width: 182px;
    /* 动效模块间距大小 */
    --adapt-guide-hand-margin: -91px;
    /* 动效模块上移位置 */
    --adapt-guide-hand-box-top: 0px;
    /* 动效模块左移位置 */
    --adapt-guide-hand-box-left: 0px;
}
</style>

<style lang="scss" scoped>
.hand {
    position: absolute;
    top: var(--adapt-guide-hand-box-top);
    left: var(--adapt-guide-hand-box-left);
    width: var(--adapt-guide-hand-box-width);
    height: var(--adapt-guide-hand-box-width);
    &.is-edge {
        margin-left: var(--adapt-guide-hand-margin);
        margin-top: var(--adapt-guide-hand-margin);
    }
}

$original-hand-box-size: 182px;
$original-hand-main-top: 96px;
$original-hand-main-left: 104px;
$original-shine-top: 65px;
$original-shine-left: 62px;

.hand-main {
    position: absolute;
    top: calc(var(--adapt-guide-hand-box-width) / 2 + $original-hand-main-top - $original-hand-box-size / 2);
    left: calc(var(--adapt-guide-hand-box-width) / 2 + $original-hand-main-left - $original-hand-box-size / 2);
    height: 57px;
    width: 49px;
    transform-origin: 50% 50%;
}

.shine {
    position: absolute;
    top: calc(var(--adapt-guide-hand-box-width) / 2 + $original-shine-top - $original-hand-box-size / 2);
    left: calc(var(--adapt-guide-hand-box-width) / 2 + $original-shine-left - $original-hand-box-size / 2);
    height: 50px;
    width: 52px;
    transform-origin: 50% 50%;
}

.is-play {
    .hand-main {
        animation: click 700ms 0s steps(14) infinite alternate;
        // animation: click 700ms 0s cubic-bezier(0.333, 0, 0.667, 1) infinite alternate;
    }
    .shine {
        // animation:
        //     shine-opacity 700ms linear infinite,
        //     shine-scale 700ms linear infinite;

        animation:
            shine-opacity 700ms steps(14) infinite,
            shine-scale 700ms steps(14) infinite;
    }
}
.reverse {
    transform: scaleX(-1);
}

@keyframes click {
    0% {
        transform: translateY(-3.25px) translateX(-10.75px) rotate(3deg);
    }
    45% {
        transform: translateY(-9.124px) translateX(-17.625px) rotate(-1deg);
    }
    100% {
        transform: translateY(-3.25px) translateX(-10.75px) rotate(3deg);
    }
}

@keyframes shine-opacity {
    0% {
        opacity: 0;
    }
    19.03% {
        // delay: 133ms
        opacity: 0;
    }
    23.75% {
        // delay: 166ms
        opacity: 1;
        // animation-timing-function: cubic-bezier(0.17, 0, 0.83, 1);
    }
    81.12% {
        // delay: 567ms
        opacity: 1;
    }
    100% {
        // delay: 700ms
        opacity: 0;
        // animation-timing-function: cubic-bezier(0.17, 0.17, 0.83, 0.83);
    }
}

@keyframes shine-scale {
    0% {
        transform: scale(0.2);
    }
    23.86% {
        // delay: 167ms
        transform: scale(0.2);
    }
    57.14% {
        // delay: 400ms
        transform: scale(1.1);
        // animation-timing-function: cubic-bezier(0.18, 0, 0.48, 1);
    }
    100.00% {
        // delay: 700ms
        transform: scale(0.8);
        // animation-timing-function: cubic-bezier(0.17, 0, 0.51, 1);
    }
}
</style>
