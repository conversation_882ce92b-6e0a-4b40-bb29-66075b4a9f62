<script lang="ts" setup>
import { useTimeoutFn } from '@vueuse/core';
import { ref } from 'vue-demi';

import TaskList from '@pet/25cny.task-list/components/TaskList.vue';
import { randomTaskData } from '@pet/25cny.task-list/demo-utils/randomTaskData';
import type { UseTaskOptions } from '@pet/work.task-list-core/useTask';
import { createExecutor } from '@pet/work.task-list-core/utils/framework/DefaultImpl/TriggerFlowModules/ExecuteModule/createExecutor';
import type {
    ResponseTask,
    TaskActionShare,
    TaskInfo,
} from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';

const myExternalTaskList = ref<ResponseTask[]>([]);
const taskLoading = ref(false);

function fetchTaskList() {
    taskLoading.value = true;
    console.log('Another fetch triggered');
    return new Promise((resolve) => {
        useTimeoutFn(() => {
            const randomNum = Math.floor(Math.random() * 3);
            const task = randomTaskData[randomNum]!;
            myExternalTaskList.value = [task];
            resolve('');
        }, 1000);
    });
}

const taskListConfig: UseTaskOptions = {
    subBizId: 13,
    executor: createExecutor({
        emit: async (event, taskInfo) => {
            console.log('emit');
            console.log(event, taskInfo);
            return;
        },
        share: async (event, taskInfo) => {
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
        shareByAvatar: async (event, taskInfo) => {
            // 关系链任务点击头像，可能是直接拉微信，可能是拉分享面板
            return new Promise<void>((resolve) => {
                const action = taskInfo.action as TaskActionShare;
                // 这里去调用分享
            });
        },
    }),
    taskList: myExternalTaskList,
    fetchTaskList,
    isLoading: taskLoading,
};
</script>

<template>
    <div>
        <button @click="fetchTaskList">重新随机一个</button>
        <TaskList :config="taskListConfig" />
    </div>
</template>

<style scoped></style>
