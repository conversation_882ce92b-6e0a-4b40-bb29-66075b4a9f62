import { useGuideState } from '../index';
import type { GuideDirectiveOptions, GuideType } from '../type';
import { compareDates, isNotNil } from '@pet/yau.core';
import { useServerTimeNow } from '@pet/yau.yoda';
import { whenever } from '@vueuse/core';
import { defineAsyncComponent, onUnmounted, ref, type Ref } from 'vue';

type GuideOptions = Omit<GuideDirectiveOptions, 'component'>;

export interface GuideProps {
    cssQuery: string;
    cssSelectorIndex?: number;
    reverse?: boolean;
    timeRange?: {
        startTime?: string;
        endTime?: string;
    };
    guideDirectiveOptions: GuideOptions;
    guideType: GuideType;
}

interface UseCommonGuideProps {
    enable: Ref<boolean>;
    guideProps: GuideProps[];
}

const useCommonGuide = (props: UseCommonGuideProps) => {
    const { now } = useServerTimeNow();
    const { addGuideInfo, removeGuideInfo } = useGuideState();
    whenever(props.enable, () => {
        props.guideProps.forEach((guideProps) => {
            const { cssQuery, cssSelectorIndex, guideDirectiveOptions, guideType, reverse, timeRange } = guideProps;
            if (timeRange) {
                const startTime = timeRange?.startTime;
                const endTime = timeRange?.endTime;
                if (isNotNil(startTime) && compareDates(startTime, now.value)) {
                    return;
                }
                if (isNotNil(endTime) && !compareDates(endTime, now.value)) {
                    return;
                }
            }
            const targetElement = ref<HTMLElement | undefined>();
            const targetElements = document.querySelectorAll(cssQuery);
            targetElement.value = targetElements[cssSelectorIndex ?? 0] as HTMLElement | undefined;

            if (!targetElement.value) {
                return;
            }
            addGuideInfo({
                ...guideDirectiveOptions,
                component: defineAsyncComponent(() => import('@pet/adapt.guide/index.vue')),
                props: { reverse },
                type: guideType ?? 2,
                el: targetElement.value,
            });
        });
    });

    onUnmounted(() => {
        props.guideProps.forEach((guideProps) => {
            removeGuideInfo(guideProps.guideDirectiveOptions.id);
        });
    });
};

export default useCommonGuide;
