<script lang="ts">
export default {
    name: 'AdaptIconWidget',
};
</script>
<script lang="ts" setup>
import { computed } from 'vue-demi';
import Back from '@pet/adapt.back/index.vue';
import YodaScan from '@pet/adapt.scan/index.vue';
import type { ScanConfig, OpenTarget, ScanResult } from '@pet/adapt.scan/index.vue';
import Scan from './icons/scan.vue';
import BackIcon from './icons/back.vue';
import BgSvg from './components/bgsvg.vue';
import type { WidgetType } from './types';

type TextType = Exclude<WidgetType, 'scan-icon' | 'back' | 'text'>;

interface Props {
    /**
     * 挂件类型
     */
    type?: WidgetType;
    /**
     * 挂件文案
     */
    text?: string;
    /**
     * 返回前执行
     */
    beforeBack?: (() => void) | (() => Promise<void>);
    /**
     * 自定义返回
     */
    customBack?: (() => void) | (() => Promise<void>);
    /**
     * scanScene
     */
    scanScene?: string;
    /**
     * 扫码配置
     */
    scanConfig?: ScanConfig;
    /**
     * 扫描打开方式
     */
    scanTarget?: OpenTarget;
    /**
     * 扫描出错时的兜底文案
     */
    scanErrMsg?: string;
}

const map: Record<TextType, string> = {
    share: '分享',
    rule: '规则',
    wallet: '钱包',
    more: '更多',
    scan: '扫码',
};

const emit = defineEmits<{
    (e: 'click', params?: string): void;
    (e: 'scanResult', val?: ScanResult): void;
}>();

const props = withDefaults(defineProps<Props>(), {
    type: 'back',
    text: '',
});

const renderText = computed(() => map[props.type as TextType] ?? props.text);
const widgetClass = computed(() => `widget-type-${props.type}`);

async function clickHandler() {
    emit('click', props.type);
}

function handleScanResult(res: ScanResult) {
    emit('scanResult', res);
}
</script>

<template>
    <span v-if="type === 'scan' || type === 'scan-icon'" :class="widgetClass" @click.stop="clickHandler()">
        <YodaScan
            class="icon-widget u-fw-500"
            :config="scanConfig"
            :target="scanTarget"
            :err-msg="scanErrMsg"
            @result="handleScanResult"
        >
            <div v-if="type === 'scan'" class="icon-widget-text">
                {{ renderText }}
                <slot />
            </div>
            <Scan v-else class="icon-widget-icon" />
            <BgSvg class="back-background" />
        </YodaScan>
    </span>
    <span v-else class="icon-widget u-fw-500" :class="widgetClass" @click.stop="clickHandler()">
        <Back v-if="type === 'back'" class="icon-widget-icon" :before-back="beforeBack" :custom-back="customBack">
            <BackIcon class="icon-widget-icon" />
        </Back>
        <div v-else class="icon-widget-text">
            {{ renderText }}
            <slot />
        </div>
        <BgSvg class="back-background" />
    </span>
</template>
<style>
:root {
    /* 文字或者图标的颜色 */
    --adapt-icon-widget-color: #fff;
    /* 挂件尺寸 */
    --adapt-icon-widget-size: 36px;
    /* 挂件背景尺寸 */
    --adapt-icon-widget-icon-size: 34px;
    /* 文字字号 */
    --adapt-icon-widget-font-size: 10px;
    /* 挂件背景色 */
    --adapt-icon-widget-backround: rgba(0, 0, 0, 0.2);
}
</style>
<style lang="scss" scoped>
$icon-size: 24px;

.icon-widget {
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--adapt-icon-widget-size);
    height: var(--adapt-icon-widget-size);
    position: relative;
    font-size: var(--adapt-icon-widget-font-size);
    text-align: center;
    color: var(--adapt-icon-widget-color);
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    // font-weight: 500;
    .icon-widget-icon {
        width: $icon-size;
        height: $icon-size;
        position: relative;
        z-index: 1;
        // 滑动的时候解决可能的icon>抖动问题
        transform: translateZ(0);
    }
    .back-background {
        height: var(--adapt-icon-widget-icon-size);
        width: var(--adapt-icon-widget-icon-size);
        color: var(--adapt-icon-widget-backround);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    .icon-widget-text {
        /* flexbox内层级，不用positioned声明防止一些副作用了 */
        z-index: 1;
    }
}
</style>
