import { createUseModel } from '@gundam/model';
import useCaptureDebugLog from '@pet/yau.logger';

import type {
    CityPageGuidePopupVO,
    CityPageDailyWelcomePopupVO,
} from '@/services/open-api-docs/home/<USER>/schemas';

import { usePopupModel, TaskType, QUEUE_TAGS_TYPE } from '../popup.model';

const { log } = useCaptureDebugLog('beginnerGuide');

/** BeginnerGuideModel 新手引导 */
export const useBeginnerGuideModel = createUseModel(() => {
    const { openPopup } = usePopupModel();
    const showGirdIcon = ref(true);
    const showHalo = ref(false);
    const showIP = ref(true);
    const showEndGirdIcon = ref(true);

    /** 横幅 */
    const openBeginnerGuide = (popup?: CityPageGuidePopupVO) => {
        log('LSQ> 新手引导，启动！: PopUpVO', popup);
        if (!popup) {
            return;
        }
        const { title, subTitle } = popup;
        const task = openPopup({
            component: () => import('@/components/city/popups/beginner-guide/BannerPopup.vue'),
            data: {
                rightText: `${title as string}\n${subTitle as string}`,
            },
            options: {
                name: TaskType.PASSIVE_POPUP,
                queueTags: [QUEUE_TAGS_TYPE.POPUP],
            },
        });
        return task.end;
    };

    const openBeginnerTextGuide = (popup?: { title: string }) => {
        if (!popup) {
            return;
        }
        const { title } = popup;
        const task = openPopup({
            component: () => import('@/components/popups/banner-popup/BannerText.vue'),
            data: {
                text: title || '',
            },
            options: {
                queueTags: [QUEUE_TAGS_TYPE.POPUP],
            },
        });
        return task.end;
    };

    /** 首次访问 */
    const firstTimeAccessGuide = async (popup?: CityPageGuidePopupVO) => {
        log('播放首次访问引导动效');
        // 隐藏格子icon
        showGirdIcon.value = false;
        showEndGirdIcon.value = false;
        // 隐藏头像
        showIP.value = false;
        await openBeginnerGuide(popup);
        // 播放光圈动效
        showHalo.value = true;
        // 展示头像
        showIP.value = true;
        // 光圈播放1000ms后，开始播终极大奖动效
        setTimeout(() => {
            showEndGirdIcon.value = true;
        }, 1000);
        // 光圈播放1800ms后，展示箭头静态图并播奖励格子动效
        setTimeout(() => {
            showGirdIcon.value = true;
            showHalo.value = false;
        }, 1800);
    };
    /** 非首次访问 */
    const notFirstTimeAccessGuide = (popup: CityPageDailyWelcomePopupVO) => {
        // 隐藏格子icon
        showGirdIcon.value = false;
        showEndGirdIcon.value = false;
        // 隐藏头像
        showIP.value = false;
        openBeginnerTextGuide({
            title: popup.title || '',
        });
        // 播放光圈动效
        showHalo.value = true;
        // 展示头像
        showIP.value = true;
        // 光圈播放1000ms后，开始播终极大奖动效
        setTimeout(() => {
            showEndGirdIcon.value = true;
        }, 1000);
        // 光圈播放结束后，开始播奖励格子动效
        setTimeout(() => {
            showGirdIcon.value = true;
            showHalo.value = false;
        }, 1800);
    };

    return {
        firstTimeAccessGuide,
        notFirstTimeAccessGuide,
        showGirdIcon,
        showEndGirdIcon,
        showHalo,
        showIP,
    };
});
