<script lang="ts" setup>
import { ref } from 'vue-demi';

interface TransStyle {
    transformOrigin?: string;
    opacity?: string;
    transform?: string;
    transition: string;
}

interface Props {
    /**
     * 飞向的dom id
     */
    flyToTarget?: string;
    /**
     * y轴贝塞尔曲线参数（起飞：从上到下）
     */
    launchBezier?: string;
    /**
     * y轴贝塞尔曲线参数（落地：从下到上）
     */
    landingBezier?: string;
    /**
     * x轴贝塞尔曲线参数
     */
    moveBezier?: string;
    /**
     * 缩放贝塞尔曲线参数
     */
    scaleBezier?: string;
    /**
     * 动效持续时间
     */
    duration?: number;
    /**
     * 使用自身动画（开启后，需要在调用处css写对应animation variable和@keyframes）
     */
    useSelfAnimation?: boolean;
}

const xController = ref<HTMLElement | null>(null);
const yController = ref<HTMLElement | null>(null);
const selfController = ref<HTMLElement | null>(null);

const props = withDefaults(defineProps<Props>(), {
    flyToTarget: '',
    launchBezier: '0.1, 0.28, 0.2, 1.7',
    landingBezier: '0.5, -1, 0.51, 0',
    moveBezier: '.67, 0, .67, 1',
    scaleBezier: '0, 0, 1, 1',
    duration: 667,
    useSelfAnimation: false,
});

function getDistance(self: HTMLElement, target: HTMLElement) {
    if (!(self != null && target != null)) {
        return { x: 0, y: 0, scale: 1 };
    }
    const targetInfo = target.getBoundingClientRect();
    const rootInfo = self.getBoundingClientRect();
    const x = targetInfo.x - rootInfo.x + (targetInfo.width - rootInfo.width) / 2;
    const y = rootInfo.y - targetInfo.y + (rootInfo.height - targetInfo.height) / 2;
    const scale = targetInfo.width / rootInfo.width;
    return {
        x,
        y,
        scale,
    };
}

function getDelta() {
    const self = xController.value;
    const target = document.getElementById(props.flyToTarget);
    if (!self || !target) {
        return undefined;
    }
    return () => getDistance(self, target);
}

function setStyle(el: HTMLElement, style: TransStyle) {
    if (el != null) {
        const dom = el;
        dom.style.transformOrigin = style.transformOrigin ?? 'center';
        dom.style.opacity = style.opacity ?? '1';
        dom.style.transform = style.transform ?? 'none';
        dom.style.transition = style.transition;
    }
}

function setX() {
    const el = xController.value;
    const delta = getDelta();
    if (el && delta) {
        setStyle(el, {
            transform: `translate(${delta().x}px, 0)`,
            transition: `transform ${props.duration}ms cubic-bezier(${props.moveBezier})`,
            opacity: '1',
        });
    }
}

function setY() {
    const el = yController.value;
    const delta = getDelta();
    if (el && delta) {
        const bz = delta().y < 0 ? props.landingBezier : props.launchBezier;
        setStyle(el, {
            transform: `translate(0, ${0 - delta().y}px)`,
            transition: `transform ${props.duration}ms cubic-bezier(${bz})`,
        });
    }
}

function addSelfClass(el: HTMLElement) {
    if (el != null) {
        const dom = el;
        dom.classList.add('pilot-self-animation-container');
    }
}

function setSelf() {
    const el = selfController.value;
    const delta = getDelta();
    if (el && delta) {
        if (props.useSelfAnimation) {
            addSelfClass(el);
        } else {
            setStyle(el, {
                transform: `scale(0)`,
                transition: `transform ${props.duration}ms cubic-bezier(${props.scaleBezier}), opacity ${
                    props.duration / 3
                }ms ease-in ${(props.duration * 2) / 3}ms`,
                opacity: '0',
            });
        }
    }
}

function boot() {
    setX();
    setY();
    setSelf();
}
defineExpose({
    boot,
});
</script>

<template>
    <div v-if="!flyToTarget">
        <slot />
    </div>
    <div v-else ref="xController" class="pilot-x-container">
        <div ref="yController" class="pilot-y-container">
            <div ref="selfController" class="pilot-self-container">
                <slot />
            </div>
        </div>
    </div>
</template>
