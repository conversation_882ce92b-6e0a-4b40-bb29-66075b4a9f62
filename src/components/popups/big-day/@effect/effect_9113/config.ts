/**
 * lottie-web resource data
 * generate@ 1 for @vision/runtime@^0.7.0
 * preview@ https://vision.corp.kuaishou.com/effect/9113
 *
 * wrapper size expected: { width: 414px; height: 896px; }
 */
import '@vision/runtime/adapter/lottie-web';

import entryData from './assets/data';

export default Object.seal({
    type: 'lottie-web' as const,

    meta: {
        version: 1,
        id: '9113',
        effectVersion: 0,
    },

    layout: {
        adaptive: 414,
    },

    options: {
        /**
         * 默认循环播放
         */
        loop: false,
        /**
         * 默认自动播放
         */
        autoplay: true,
        ratio: 2,
    },

    entryData,

    extraData: {
        /**
         * 如果使用entrySrc直接引入lottie.json，或者资源目录中途手动改变了，请手动设置base指定资源请求的base目录
         */
        // base: '',

        /**
         * 用来渲染的模式 "canvas" | "html" | "svg"
         */
        renderType: 'canvas',

        /**
         * 拉伸展示设置，默认使用 'xMidYMid slice'。详细见
         * https://developer.mozilla.org/zh-CN/docs/Web/SVG/Attribute/preserveAspectRatio
         */
        // preserveAspectRatio: 'xMidYMid slice',

        /**
         * 图层和资源的替换信息
         */
        refInfo: [
            {
                type: 'asset',
                key: 'textImage',
                id: 'image_8',
            },
        ],
    },
});
