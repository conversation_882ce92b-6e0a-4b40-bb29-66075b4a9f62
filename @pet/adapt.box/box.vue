<script lang="ts">
import { computed, defineComponent, type PropType, h, provide, readonly } from 'vue-demi';
import { px2rem } from '@pet/core.mobile';
import type { DirectionType, AlignType, JustifyType, IGapInfo } from './types';
import { BoxInfoProvider } from './types';

const directionClasses = {
    row: 'row',
    'row-rev': 'row-reverse',
    col: 'column',
    'col-rev': 'column-reverse',
};

const alignClasses = {
    start: 'align-start',
    end: 'align-end',
    center: 'align-center',
    baseline: 'baseline',
    stretch: 'stretch',
};

const justifyClasses = {
    start: 'justify-start',
    end: 'justify-end',
    center: 'justify-center',
    between: 'between',
    around: 'around',
    evenly: 'evenly',
};

const COMPONENT_NAME = 'g-box-flex';
const COMPONENT_NAME_CLASSIC = 'g-box-block';

export default defineComponent({
    name: 'BaseBox',
    props: {
        /**
         * 渲染的html tag
         */
        tag: {
            type: String as PropType<keyof HTMLElementTagNameMap>,
            default: 'div',
            require: false,
        },
        /**
         * 渲染的flex是否换行
         */
        wrap: {
            type: Boolean,
            default: false,
            require: false,
        },
        /**
         * flex方向
         */
        direction: {
            type: String as PropType<DirectionType>,
            default: 'row',
            require: false,
        },
        /**
         * align-items对齐方式
         */
        align: {
            type: String as PropType<AlignType>,
            default: 'stretch',
            require: false,
        },
        /**
         * justify-content对齐方式
         */
        justify: {
            type: String as PropType<JustifyType>,
            default: 'start',
            require: false,
        },
        /**
         * 换行时主轴的列数
         */
        items: {
            type: Number,
            default: 0,
            require: false,
        },
        /**
         * 横轴间隙
         */
        gapRow: {
            type: Number,
            default: 0,
            require: false,
        },
        /**
         * 纵轴间隙
         */
        gapCol: {
            type: Number,
            default: 0,
            require: false,
        },
        /**
         * 是否为inline形式
         */
        inline: {
            type: Boolean,
            default: false,
            require: false,
        },
        /**
         * 显示为inline-block
         */
        classic: {
            type: Boolean,
            default: false,
            require: false,
        },
    },
    setup(props, { slots }) {
        const boxClasses = computed(() => {
            const display = props.classic ? COMPONENT_NAME_CLASSIC : COMPONENT_NAME;
            const colsCtrl = Boolean(props.items) && `${display}-col-${props.items}`;
            const isWrap = props.wrap && `${COMPONENT_NAME}-wrap`;
            const isInline = props.inline && 'is-inline';
            const dir = `${COMPONENT_NAME}-${directionClasses[props.direction]}`;
            const axisX = `${COMPONENT_NAME}-${alignClasses[props.align]}`;
            const axisY = `${COMPONENT_NAME}-${justifyClasses[props.justify]}`;
            const commonClass = [display, colsCtrl, isInline];
            const flexClasses = [isWrap, dir, axisX, axisY];
            return props.classic ? commonClass : [...commonClass, ...flexClasses];
        });

        const boxStyles = computed(() => {
            const gapRowType = props.direction.includes('row') ? 'margin-top' : 'margin-left';
            const gapColType = props.direction.includes('row') ? 'margin-left' : 'margin-top';
            return {
                [gapRowType]: Boolean(props.gapRow) ? px2rem(0 - props.gapRow) : undefined,
                [gapColType]: Boolean(props.gapCol) ? px2rem(0 - props.gapCol) : undefined,
            };
        });

        const boxInfo = computed<IGapInfo>(() => {
            return {
                direction: props.direction,
                classic: props.classic,
                row: props.gapRow,
                col: props.gapCol,
            };
        });

        provide(BoxInfoProvider, readonly(boxInfo));

        return () =>
            h(
                props.tag,
                {
                    class: boxClasses.value,
                    style: boxStyles.value,
                },
                slots.default?.(),
            );
    },
});
</script>

<style lang="scss" scoped>
.g-box-block {
    display: block;
    &.is-inline {
        display: inline-block;
    }
}

$align: (
    align-start: flex-start,
    align-end: flex-end,
    align-center: center,
    baseline: baseline,
    stretch: stretch,
);

$justify: (
    justify-start: flex-start,
    justify-end: flex-end,
    justify-center: center,
    between: space-between,
    around: space-around,
    evenly: space-evenly,
);

.g-box-flex {
    display: flex;
    &.is-inline {
        display: inline-flex;
    }

    &-wrap {
        flex-wrap: wrap;
    }

    @each $direction in row-reverse, column, column-reverse {
        &-#{$direction} {
            flex-direction: $direction;
        }
    }

    @each $name, $property in $align {
        &-#{$name} {
            align-items: $property;
        }
    }

    @each $name, $property in $justify {
        &-#{$name} {
            justify-content: $property;
        }
    }
}

@for $i from 1 through 8 {
    .g-box-flex-col-#{$i} {
        :deep(> .g-box-item-flex) {
            flex-basis: calc(100% / $i);
            flex-grow: 0;
        }
    }

    .g-box-block-col-#{$i} {
        :deep(> .g-box-item-inline-block) {
            width: calc(100% / $i);
        }
    }
}
</style>
