<script lang="ts" setup>
defineOptions({
    name: 'AdaptLayCartoon',
});
</script>

<template>
    <div class="lay-cartoon">
        <slot />
    </div>
</template>
<style>
:root {
    /* 趴图 */
    --adapt-lay-cartoon: url(./assets/cartoon.png);
    /* 趴图背景图 */
    --adapt-lay-cartoon-background: none;
    /* 趴图高度 */
    --adapt-lay-cartoon-height: 140px;
    /* 手爪子等这类元素距离面板的负的距离 */
    --adapt-lay-cartoon-margin-bottom: 10.5px;
    /* 背景的位置 */
    --adapt-lay-cartoon-background-position: top center;
}
</style>
<style lang="scss" scoped>
.lay-cartoon {
    height: var(--adapt-lay-cartoon-height);
    background-repeat: no-repeat;
    background-size: auto 100%;
    background-position: var(--adapt-lay-cartoon-background-position);
    background-image: var(--adapt-lay-cartoon), var(--adapt-lay-cartoon-background);
    margin-bottom: calc(-1 * #{var(--adapt-lay-cartoon-margin-bottom)});
}
</style>
