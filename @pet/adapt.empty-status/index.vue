<script lang="ts">
export default {
    name: 'AdaptEmptyStatus',
};
</script>
<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import type { ButtonHeight, ButtonType } from '@pet/adapt.button/types';
import { computed } from 'vue-demi';
import Icon from './icons.vue';
import type { IconName } from './types';

interface StatusProps {
    /**
     *  Icon名称
     *  @values 'network' | 'protect' | 'band' | 'empty' |
     * 'anomaly' | 'notStart' | 'end' | 'download' | 'noBody' |
     * 'noGift' | 'noMessage' | 'activityHot' | 'user'
     */
    icon?: IconName;
    /**
     *  是否包含按钮
     */
    button?: boolean;
    /**
     * 按钮高度
     */
    buttonHeight?: ButtonHeight;
    /**
     *  按钮文案
     */
    buttonText?: string;
    /**
     * 按钮类型
     */
    buttonType?: ButtonType;
    /**
     * 是否在模块内
     */
    inComponent?: boolean;
}

const props = withDefaults(defineProps<StatusProps>(), {
    icon: 'network',
    button: false,
    buttonHeight: 38,
    buttonText: '刷新',
    bottonType: 'plain',
    inComponent: true,
});

const emit = defineEmits<{
    /**
     * 按钮点击
     */
    (event: 'btn-click'): void;
}>();

const slots = defineSlots<{
    subMessage?: ((props: {}) => any) | undefined;
    icon?: ((props: {}) => any) | undefined;
    default?: ((props: {}) => any) | undefined;
}>();
const withSubMessage = computed(() => slots.subMessage && 'with-sub-message');
const iconPosition = computed(() => (props.inComponent ? 'is-in-component' : 'is-in-page'));
</script>

<template>
    <div class="status-wrap">
        <div class="status-main" :class="iconPosition">
            <div v-if="slots.icon" class="icon-wrapper status-icon">
                <!-- @slot 图标插槽 -->
                <slot name="icon" />
            </div>
            <Icon v-else class="status-icon" :name="icon" />
            <div v-if="slots.default" class="status-message" :class="withSubMessage">
                <!-- @slot 默认插槽 -->
                <slot />
            </div>
            <div v-if="slots.subMessage" class="status-sub-message">
                <!-- @slot 辅助信息 -->
                <slot name="subMessage" />
            </div>
            <div v-if="button" class="status-ctrl">
                <Button :type="buttonType" :height="buttonHeight" @click.stop="emit('btn-click')" class="custom-class">
                    {{ buttonText }}
                </Button>
            </div>
        </div>
    </div>
</template>
<style>
:root {
    /* 图标默认颜色 */
    --adapt-empty-status-icon-color: currentColor;
    /* 图标透明度 */
    --adapt-empty-status-icon-opacity: 0.6;
    /* 图标大小 */
    --adapt-empty-status-icon-width: 88px;
    /* 图标兜底文案文字大小 */
    --adapt-empty-status-normal-font-size: 16px;
    /* 图标兜底文案行高 */
    --adapt-empty-status-normal-line-height: 22px;
    /* 图标主标题文字大小 */
    --adapt-empty-status-main-font-size: 18px;
    /* 图标主标题行高 */
    --adapt-empty-status-main-line-height: 25px;
    /* 图标副标题文字大小 */
    --adapt-empty-status-sub-font-size: 15px;
    /* 图标副标题行高 */
    --adapt-empty-status-sub-line-height: 21px;
    /* 图标副标题字重 */
    --adapt-empty-status-sub-font-weight: 700;
    /* 图标副标题透明度 */
    --adapt-empty-status-sub-opcity: 0.6;
    /* icon与文案间距 */
    --adapt-empty-status-icon-message-gap: 16px;
    /* 图标描述文案与按钮间距 */
    --adapt-empty-status-message-btn-gap: 20px;
    /* 图标描述主文案与副标题间距 */
    --adapt-empty-status-message-sub-gap: 8px;
    /* 图标描述文案最大宽度 */
    --adapt-empty-status-message-max-width: 318px;
}
</style>

<style lang="scss" scoped>
.status-wrap {
    --adapt-button-plain-border-color: var(--adapt-empty-status-icon-color);
    --adapt-button-plain-font-color: var(--adapt-empty-status-icon-color);
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;

    .status-main {
        font-size: var(--adapt-empty-status-main-font-size);
        text-align: center;
    }
    .status-ctrl {
        display: flex;
        justify-content: center;
        margin-top: var(--adapt-empty-status-message-btn-gap);

        .custom-class {
        --adapt-button-primary-background-color: transparent;
        --adapt-button-primary-font-color: black;
        border: 1px solid black;
        border-radius: 22px;
        }

    .is-in-page {
        --adapt-empty-status-icon-width: 96px;
    }
}
.status-icon {
    color: var(--adapt-empty-status-icon-color);
    opacity: var(--adapt-empty-status-icon-opacity);
    vertical-align: top;
    width: var(--adapt-empty-status-icon-width);
    height: var(--adapt-empty-status-icon-width);
}
.status-message {
    text-align: center;
    margin-top: var(--adapt-empty-status-icon-message-gap);
    font-size: var(--adapt-empty-status-normal-font-size);
    line-height: var(--adapt-empty-status-normal-line-height);
    max-width: var(--adapt-empty-status-message-max-width);
    color: var(--adapt-empty-status-icon-color);
    &.with-sub-message {
        font-size: var(--adapt-empty-status-main-font-size);
        font-weight: var(--adapt-empty-status-sub-font-weight);
        line-height: var(--adapt-empty-status-main-line-height);
    }
}
.status-sub-message {
    color: var(--adapt-empty-status-icon-color);
    max-width: var(--adapt-empty-status-message-max-width);
    opacity: var(--adapt-empty-status-sub-opcity);
    font-size: var(--adapt-empty-status-sub-font-size);
    line-height: var(--adapt-empty-status-sub-line-height);
    margin-top: var(--adapt-empty-status-message-sub-gap);
}

.icon-wrapper {
    margin: auto;
    :deep(svg),
    :deep(img) {
        width: 100%;
        height: 100%;
    }
}
}
</style>
