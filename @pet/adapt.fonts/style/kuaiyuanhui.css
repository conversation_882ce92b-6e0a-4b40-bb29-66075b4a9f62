@font-face {
    font-family: kuaiyuanhui;
    src: url('../assets/KuaiYuanHuiTi-Regular_min.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    ascent-override: 100%;
}

@font-face {
    font-family: Kuai<PERSON>uanHuiTi-Full;
    src: url('../assets/KuaiYuanHuiTi-Full.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    ascent-override: 100%;
}

@font-face {
    font-family: KuaiYuanHuiTi;
    src: url('../assets/KuaiYuanHuiTi-Regular_min.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    /* 修正基线 webview87支持 其他不管了 */
    ascent-override: 100%;
}
