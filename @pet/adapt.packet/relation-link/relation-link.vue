<script setup lang="ts">
import { computed } from 'vue-demi';
import RelationItem from './relation-item.vue';
import { debounce } from '@pet/yau.core/lodash';
import type { UserInfo, NormalInfo, InviteInfo, MoreInfo, GroupInfo } from '../types';

const props = defineProps<{
    info: {
        desc: string;
        totalCount?: number;
        userList: UserInfo[];
    };
}>();

const emits = defineEmits<{
    (e: 'group-click'): void;
    (e: 'invite-avatar-click'): void;
}>();

const groupClick = debounce(() => {
    emits('group-click');
}, 200);

// 设计给的规则超复杂啊 https://www.figma.com/design/o3HEGwJiet8GxgLkW7Dg5e/%E5%A2%9E%E9%95%BF%E8%AE%BE%E8%AE%A1%E5%85%A8%E5%B1%80%E8%A7%84%E8%8C%83-%E5%AF%B9%E5%A4%96?node-id=2422-42437&t=m5OxrEpEeHKgEpCh-0
// eslint-disable-next-line sonarjs/cognitive-complexity
const linkList = computed(() => {
    const userLength = props.info.userList.length;
    // 兼容旧的逻辑，如果没有totalCount，则默认展示4个
    if (props.info.totalCount === undefined) {
        return props.info.userList.slice(0, 4).map(
            (item) =>
                ({
                    type: 'normal',
                    userInfo: item,
                }) satisfies NormalInfo,
        );
        // 小于4个时，直接展示
    } else if (props.info?.totalCount <= 4) {
        return Array.from({ length: props.info.totalCount }).map((item, index) => {
            if (props.info.userList[index]) {
                return {
                    type: 'normal',
                    userInfo: props.info.userList[index]!,
                } satisfies NormalInfo;
            } else {
                return {
                    type: 'invite',
                } satisfies InviteInfo;
            }
        });
    } else {
        const data = [];
        const infoList = [...props.info.userList];
        const lackNum = props.info?.totalCount - userLength;
        // 四个展示位置分别处理
        // 位置4处理
        if (lackNum <= 3) {
            data.unshift({
                type: 'invite',
            } satisfies InviteInfo);
        } else {
            data.unshift({
                type: 'more',
                count: lackNum - (userLength === 0 ? 3 : 2),
            } satisfies MoreInfo);
        }
        // 位置3处理
        if (lackNum === 1) {
            data.unshift({
                type: 'normal',
                userInfo: infoList.pop()!,
            } satisfies NormalInfo);
        } else {
            data.unshift({
                type: 'invite',
            } satisfies InviteInfo);
        }
        // 位置2处理
        if (lackNum <= 2) {
            data.unshift({
                type: 'normal',
                userInfo: infoList.pop()!,
            } satisfies NormalInfo);
        } else {
            data.unshift({
                type: 'invite',
            } satisfies InviteInfo);
        }
        // 位置1处理
        if (infoList.length > 1) {
            data.unshift({
                type: 'group',
                userList: infoList,
            } satisfies GroupInfo);
        } else if (infoList.length === 1) {
            data.unshift({
                type: 'normal',
                userInfo: infoList[0]!,
            } satisfies NormalInfo);
        } else {
            data.unshift({
                type: 'invite',
            } satisfies InviteInfo);
        }

        return data;
    }
});
</script>

<template>
    <div class="relation-link">
        <div class="desc">
            <span class="line"></span>
            <span class="text">{{ info.desc }}</span>
            <span class="line right"></span>
        </div>
        <div class="user-list">
            <RelationItem
                v-for="(item, index) in linkList"
                :key="index"
                :class="['item', index !== 0 ? 'gap-8' : '']"
                :info="item"
                @group-click="groupClick"
                @invite-avatar-click="emits('invite-avatar-click')"
            />
        </div>
    </div>
</template>

<style></style>

<style lang="scss" scoped>
@import '../common.scss';
.relation-link {
    position: relative;
    .desc {
        display: flex;
        justify-content: center;
        align-items: center;
        .line {
            display: inline-block;
            width: 22px;
            height: 1px;
            border-radius: 1px;
            background: var(--adapt-packet-relation-left-icon-bg);
        }

        .right {
            background: var(--adapt-packet-relation-right-icon-bg);
        }

        .text {
            margin: 0 5px;
            display: inline-block;
            font-weight: var(--adapt-packet-inner-relation-desc-font-weight);
            font-size: var(--adapt-packet-inner-relation-desc-font-size);
            color: var(--adapt-packet-inner-relation-desc-color);
        }
    }

    .user-list {
        margin-top: var(--adapt-packet-inner-relation-avatar-margin-top);
        display: flex;
        justify-content: center;
    }

    .gap-8 {
        margin-left: 8px;
    }
}
</style>
