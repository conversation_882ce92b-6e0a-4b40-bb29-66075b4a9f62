<script setup lang="ts">
/* generate by gen_demo.js */
import ActivityhotIcon from '../activityhot-svg.vue';
import AnomalyIcon from '../anomaly-svg.vue';
import ArrLineIcon from '../arr-line-svg.vue';
import ArrSolidIcon from '../arr-solid-svg.vue';
import ArrIcon from '../arr-svg.vue';
import BandIcon from '../band-svg.vue';
import BoldTipIcon from '../bold-tip-svg.vue';
import CircleAddIcon from '../circle-add-svg.vue';
import CircleRefreshIcon from '../circle-refresh-svg.vue';
import ClickIcon from '../click-svg.vue';
import CloseBigLineIcon from '../close-big-line-svg.vue';
import CloseBigSimpleIcon from '../close-big-simple-svg.vue';
import CloseBigSolidIcon from '../close-big-solid-svg.vue';
import CloseSmallLineIcon from '../close-small-line-svg.vue';
import CloseSmallSimpleIcon from '../close-small-simple-svg.vue';
import CloseSmallSolidIcon from '../close-small-solid-svg.vue';
import CloseIcon from '../close-svg.vue';
import CustomerserviceIcon from '../customerService-svg.vue';
import DownloadIcon from '../download-svg.vue';
import EmptyIcon from '../empty-svg.vue';
import EndIcon from '../end-svg.vue';
import GuideIcon from '../guide-svg.vue';
import HelpIcon from '../help-svg.vue';
import LocationIcon from '../location-svg.vue';
import LockIcon from '../lock-svg.vue';
import MusicIcon from '../music-svg.vue';
import NetworkIcon from '../network-svg.vue';
import NobodyIcon from '../nobody-svg.vue';
import NogiftIcon from '../nogift-svg.vue';
import NomessageIcon from '../nomessage-svg.vue';
import NotstartIcon from '../notstart-svg.vue';
import ProtectIcon from '../protect-svg.vue';
import RecordIcon from '../record-svg.vue';
import RefreshIcon from '../refresh-svg.vue';
import RuleBoldIcon from '../rule-bold-svg.vue';
import ScanIcon from '../scan-svg.vue';
import ServiceIcon from '../service-svg.vue';
import ShakeIcon from '../shake-svg.vue';
import ShareIcon from '../share-svg.vue';
import SoundOffIcon from '../sound-off-svg.vue';
import SoundOnIcon from '../sound-on-svg.vue';
import StarIcon from '../star-svg.vue';
import TrendsIcon from '../trends-svg.vue';
import UserIcon from '../user-svg.vue';
import WalletIcon from '../wallet-svg.vue';
</script>
<template>
    <div>
        <div class="item">
            <ActivityhotIcon class="icon" />
            <h4>activityhot-svg.vue</h4>
        </div>
        <div class="item">
            <AnomalyIcon class="icon" />
            <h4>anomaly-svg.vue</h4>
        </div>
        <div class="item">
            <ArrLineIcon class="icon" />
            <h4>arr-line-svg.vue</h4>
        </div>
        <div class="item">
            <ArrSolidIcon class="icon" />
            <h4>arr-solid-svg.vue</h4>
        </div>
        <div class="item">
            <ArrIcon class="icon" />
            <h4>arr-svg.vue</h4>
        </div>
        <div class="item">
            <BandIcon class="icon" />
            <h4>band-svg.vue</h4>
        </div>
        <div class="item">
            <BoldTipIcon class="icon" />
            <h4>bold-tip-svg.vue</h4>
        </div>
        <div class="item">
            <CircleAddIcon class="icon" />
            <h4>circle-add-svg.vue</h4>
        </div>
        <div class="item">
            <CircleRefreshIcon class="icon" />
            <h4>circle-refresh-svg.vue</h4>
        </div>
        <div class="item">
            <ClickIcon class="icon" />
            <h4>click-svg.vue</h4>
        </div>
        <div class="item">
            <CloseBigLineIcon class="icon" />
            <h4>close-big-line-svg.vue</h4>
        </div>
        <div class="item">
            <CloseBigSimpleIcon class="icon" />
            <h4>close-big-simple-svg.vue</h4>
        </div>
        <div class="item">
            <CloseBigSolidIcon class="icon" />
            <h4>close-big-solid-svg.vue</h4>
        </div>
        <div class="item">
            <CloseSmallLineIcon class="icon" />
            <h4>close-small-line-svg.vue</h4>
        </div>
        <div class="item">
            <CloseSmallSimpleIcon class="icon" />
            <h4>close-small-simple-svg.vue</h4>
        </div>
        <div class="item">
            <CloseSmallSolidIcon class="icon" />
            <h4>close-small-solid-svg.vue</h4>
        </div>
        <div class="item">
            <CloseIcon class="icon" />
            <h4>close-svg.vue</h4>
        </div>
        <div class="item">
            <CustomerserviceIcon class="icon" />
            <h4>customerService-svg.vue</h4>
        </div>
        <div class="item">
            <DownloadIcon class="icon" />
            <h4>download-svg.vue</h4>
        </div>
        <div class="item">
            <EmptyIcon class="icon" />
            <h4>empty-svg.vue</h4>
        </div>
        <div class="item">
            <EndIcon class="icon" />
            <h4>end-svg.vue</h4>
        </div>
        <div class="item">
            <GuideIcon class="icon" />
            <h4>guide-svg.vue</h4>
        </div>
        <div class="item">
            <HelpIcon class="icon" />
            <h4>help-svg.vue</h4>
        </div>
        <div class="item">
            <LocationIcon class="icon" />
            <h4>location-svg.vue</h4>
        </div>
        <div class="item">
            <LockIcon class="icon" />
            <h4>lock-svg.vue</h4>
        </div>
        <div class="item">
            <MusicIcon class="icon" />
            <h4>music-svg.vue</h4>
        </div>
        <div class="item">
            <NetworkIcon class="icon" />
            <h4>network-svg.vue</h4>
        </div>
        <div class="item">
            <NobodyIcon class="icon" />
            <h4>nobody-svg.vue</h4>
        </div>
        <div class="item">
            <NogiftIcon class="icon" />
            <h4>nogift-svg.vue</h4>
        </div>
        <div class="item">
            <NomessageIcon class="icon" />
            <h4>nomessage-svg.vue</h4>
        </div>
        <div class="item">
            <NotstartIcon class="icon" />
            <h4>notstart-svg.vue</h4>
        </div>
        <div class="item">
            <ProtectIcon class="icon" />
            <h4>protect-svg.vue</h4>
        </div>
        <div class="item">
            <RecordIcon class="icon" />
            <h4>record-svg.vue</h4>
        </div>
        <div class="item">
            <RefreshIcon class="icon" />
            <h4>refresh-svg.vue</h4>
        </div>
        <div class="item">
            <RuleBoldIcon class="icon" />
            <h4>rule-bold-svg.vue</h4>
        </div>
        <div class="item">
            <ScanIcon class="icon" />
            <h4>scan-svg.vue</h4>
        </div>
        <div class="item">
            <ServiceIcon class="icon" />
            <h4>service-svg.vue</h4>
        </div>
        <div class="item">
            <ShakeIcon class="icon" />
            <h4>shake-svg.vue</h4>
        </div>
        <div class="item">
            <ShareIcon class="icon" />
            <h4>share-svg.vue</h4>
        </div>
        <div class="item">
            <SoundOffIcon class="icon" />
            <h4>sound-off-svg.vue</h4>
        </div>
        <div class="item">
            <SoundOnIcon class="icon" />
            <h4>sound-on-svg.vue</h4>
        </div>
        <div class="item">
            <StarIcon class="icon" />
            <h4>star-svg.vue</h4>
        </div>
        <div class="item">
            <TrendsIcon class="icon" />
            <h4>trends-svg.vue</h4>
        </div>
        <div class="item">
            <UserIcon class="icon" />
            <h4>user-svg.vue</h4>
        </div>
        <div class="item">
            <WalletIcon class="icon" />
            <h4>wallet-svg.vue</h4>
        </div>
    </div>
</template>
<style src="../assets/css/demo.scss" lang="scss" scoped></style>
