<script lang="ts" setup>
import { computed, ref, useSlots } from 'vue-demi';

import Card from '@pet/adapt.card/index.vue';
import type { CardSize } from '@pet/adapt.card/types';
import TaskConnectionAvatar from '@pet/25cny.task-list/components/TaskConnectionAvatar.vue';
import type { CardProps, TaskListOptions } from '@pet/25cny.task-list/components/types';
import type { TaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';
import { interceptStrTo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/utils/utils';

const MAX_USER_COUNT = 5;

const props = withDefaults(
    defineProps<{
        taskInfo: TaskInfo;
        avatar: string;
        userList: TaskInfo['assistUserInfo'];
        size?: CardSize;
        uiPassThroughConfig?: Exclude<TaskListOptions['uiPassThroughConfig'], undefined>['relationChainTask'];
        needCompleteAmount?: number;
        defaultIcon?: string;
    }>(),
    { needCompleteAmount: MAX_USER_COUNT },
);

const displayUserList = computed(() => {
    const data = [];
    const amount = Math.min(props.needCompleteAmount, MAX_USER_COUNT);
    for (let i = 0; i < amount; i++) {
        const info = props.userList?.[i] || { userId: -1, userName: '', headImg: '' };
        data.push({
            avatar: info.headImg,
            username: interceptStrTo(info.userName, 5),
            userId: info.userId,
        });
    }
    return data;
});

const emits = defineEmits<{
    (e: 'clickAvatar', uid: number): void;
}>();

function getCardProps(computedProps: CardProps) {
    const baseProps = props.uiPassThroughConfig?.taskCard?.base ?? {};
    const overrideProps = props.uiPassThroughConfig?.taskCard?.override ?? {};
    return {
        ...baseProps,
        ...computedProps,
        ...overrideProps,
    };
}

const uiPassThroughConfigAvatar = computed(() => props.uiPassThroughConfig?.avatar);
const slots = useSlots();
</script>
<template>
    <div class="connection-task-card">
        <Card
            class="task-list-item"
            v-bind="
                getCardProps({
                    avatar,
                    title: taskInfo.title,
                    desc: taskInfo.description,
                    size,
                    fallbackAvatar: defaultIcon,
                })
            "
            :cdn-level="'P1'"
        >
            <template #ctrl>
                <slot name="ctrl"></slot>
            </template>
        </Card>
        <div class="connection-info-card">
            <slot v-if="slots.extraModule" :task-info="taskInfo" name="extraModule"></slot>
            <div v-else class="user-list">
                <TaskConnectionAvatar
                    v-for="user in displayUserList ? displayUserList : []"
                    :key="user.userId"
                    :task-info="taskInfo"
                    :user-info="user"
                    :ui-pass-through-config="uiPassThroughConfigAvatar"
                    @clickAvatar="emits('clickAvatar', $event)"
                />
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.task-list-item {
    --adapt-card-title-color: rgba(85, 0, 0, 1);
    --adapt-card-desc-color: rgba(85, 0, 0, 0.4);
    --adapt-card-main-ctrl-gap: 22px;
    --adapt-card-figure-main-gap: 10px;
    --adapt-card-figure-size: 44px;
    --adapt-card-padding: 15px 12px;
    --adapt-card-background: var(--task-list-card-background);
    --adapt-card-radius: 20px 20px 0 0;
}
.task-list-item {
    --adapt-card-figure-size: 44px;
    --adapt-card-padding: 15px 12px 10px 12px;
}
.connection-task-card .task-list-item.g-card {
    border-radius: 16px 16px 0 0;
}

.connection-info-card {
    box-sizing: border-box;
    background-color: var(--task-list-card-background);
    border-radius: 0 0 15px 16px;
    display: flex;
    flex-wrap: wrap;
    padding: 0 18px 12px 66px;
    .separator {
        width: 100%;
        height: 1px;
        margin-bottom: 16px;
    }
    .user-list {
        width: 100%;
        display: flex;
        align-items: center;
    }
}
</style>
