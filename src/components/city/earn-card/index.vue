<script lang="ts" setup>
import { useModel } from '@gundam/model';
import { useOpenPage } from '@pet/yau.yoda';

import { useConfigModel } from '@/models/city/configModel';
import { useForwardRushBtnModel } from '@/models/city/forwardRushBtnModel';
import { useCityHomeModel } from '@/models/city/homeModel';
import { useSnapShotModel } from '@/models/city/snapShotModel';

const openPage = useOpenPage();
const { account, countDown } = useCityHomeModel();
const { kconfUEConfig } = useConfigModel();
const { autoGoToHomePage } = useForwardRushBtnModel();
const { accountModel } = useSnapShotModel();

// Countdown timer logic
const hours = ref('00');
const minutes = ref('00');
const seconds = ref('00');

const remainingTime = ref<number | undefined>();

// Timer reference to clear on unmount
const timerRef = ref<number | null>(null);

// Calculate time remaining based on countDown value
const updateCountdown = () => {
    if (typeof remainingTime.value !== 'number') {
        return;
    }
    // Convert to hours, minutes, seconds
    const hoursValue = Math.floor(remainingTime.value / (1000 * 60 * 60));
    const minutesValue = Math.floor((remainingTime.value % (1000 * 60 * 60)) / (1000 * 60));
    const secondsValue = Math.floor((remainingTime.value % (1000 * 60)) / 1000);
    // Format with leading zeros
    hours.value = hoursValue.toString().padStart(2, '0');
    minutes.value = minutesValue.toString().padStart(2, '0');
    seconds.value = secondsValue.toString().padStart(2, '0');
    remainingTime.value -= 1000;
    // Decrement the remaining time by 1 second for the next update
};

const clearTimer = () => {
    if (timerRef.value) {
        clearInterval(timerRef.value);
        timerRef.value = null;
    }
};

const startTimer = () => {
    clearTimer();
    remainingTime.value = countDown.value;
    // Update every second
    timerRef.value = window.setInterval(() => {
        // Stop timer when countdown reaches zero
        if (typeof remainingTime.value === 'number' && remainingTime.value <= 0) {
            // todo 结束倒计时返回主会场
            clearTimer();
            autoGoToHomePage();
        }
        updateCountdown();
    }, 1000);
};

const jumpTo = (url: string) => {
    url && openPage(url, { forceOpenInNewWebview: true, keepQuery: false });
};

// Watch for changes in countDown and restart timer when it changes
watch(countDown, (newVal) => {
    if (newVal && typeof newVal === 'number') {
        startTimer();
    }
});

onMounted(() => {
    startTimer();
});

// Clean up timer on unmount
onUnmounted(() => {
    clearTimer();
});
</script>

<template>
    <div class="earn-card-container">
        <div class="earn-card">
            <div class="earn-card-content">
                <div class="earn-card-left">
                    <div class="earn-card-header">
                        <img src="./assets/readpack.png" alt="Cash" />
                        <div class="earn-card-label">我的现金：</div>
                    </div>
                    <div id="earn-card-amount" class="earn-card-amount">
                        <span class="earn-card-value">{{ accountModel?.total || account.total }}</span>
                        <span class="earn-card-unit">{{ accountModel?.unit || account.unit }}</span>
                    </div>
                </div>
                <div
                    v-click-log="{
                        action: 'OP_ACTIVITY_CASH_BUTTON',
                    }"
                    class="earn-card-right"
                    @click="jumpTo(kconfUEConfig.walletUrl)"
                >
                    <img class="right-button-img" src="./assets/btn.png" alt="Btn" />
                </div>
            </div>

            <div class="earn-card-countdown">
                <span class="countdown-value">{{ hours }}</span
                ><span class="separator">:</span><span class="countdown-value">{{ minutes }}</span
                ><span class="separator">:</span><span class="countdown-value">{{ seconds }}</span>
                <span class="countdown-label">后赚现金结束</span>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.earn-card-container {
    position: relative;
    width: 292px;
    margin-top: 12px;
    z-index: 99;
    display: flex;
    justify-content: center;
}
.earn-card {
    width: 292px;
    border-radius: 22px;
    background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.96) 0%,
        rgba(255, 255, 255, 0.86) 43.93%,
        rgba(255, 255, 255, 0.48) 89.92%,
        rgba(255, 255, 255, 0.1) 125.51%
    );

    .earn-card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px 4px 20px;

        .earn-card-left {
            .earn-card-header {
                display: flex;
                align-items: center;
                img {
                    width: 12px;
                    height: 12px;
                    margin-right: 3px;
                }
                .earn-card-label {
                    color: #9c9c9c;
                    font-family: PingFang SC;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                }
            }

            .earn-card-amount {
                display: flex;
                align-items: flex-end;
                padding-top: 2px;
                color: #fe3666;
                .earn-card-value {
                    font-family: MiSans;
                    font-size: 24px;
                    font-style: normal;
                    font-weight: 630;
                    line-height: 1;
                }
                .earn-card-unit {
                    padding-left: 4px;
                    font-family: PingFang SC;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 600;
                }
            }
        }

        .earn-card-right {
            .right-button-img {
                width: 56px;
                height: 28px;
            }
        }
    }

    .earn-card-countdown {
        border-radius: 0px 0px 22px 22px;
        line-height: 32px;
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(2px);
        text-align: center;
        color: #fe3666;

        .countdown-value {
            display: inline-block;
            width: 18px;
            line-height: 18px;
            margin: 0 1px;
            flex-shrink: 0;
            border-radius: 4px;
            background: rgba(254, 54, 102, 0.1);
            color: #fe3666;
            font-family: MiSans;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        .separator {
            font-family: MiSans;
            font-weight: 600;
            font-size: 12px;
            padding: 0 2px;
        }

        .countdown-label {
            padding-left: 5px;
            color: #9c9c9c;
            font-family: PingFang SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }
    }
}
</style>
