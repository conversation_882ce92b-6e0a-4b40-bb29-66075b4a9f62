import mockjs from 'mockjs';

const mockPageGuidePopup = {
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    popupType: 'CITY_PAGE_GUIDE_POPUP',
    title: '保定欢迎你',
    subTitle: '每步都赚钱 逛1圈抽大奖！',
};
const mockDailyGuidePopup = {
    sponsorText: '快手',
    popupType: 'CITY_PAGE_DAILY_GUIDE_POPUP',
    title: '保定欢迎您！',
};

const mockLLCHTaskSuccessPopup = {
    sponsorLogo:
        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
    sponsorText: '快手',
    popupType: 'CITY_LLCH_LLAWD_GRID_TASK',
    title: '任务成功完成',
    subTitle: '开红包啦',
    userId: 2184733160,
    activityId: 'summer25Main_t2',
    mainButton: {
        linkType: 'CLOSE',
        linkText: '开心收下',
        linkSubText: null,
        linkUrl: null,
        icon: null,
    },
    subButton: null,
    taskId: 16858,
    sortScore: *********,
    hashKeys: ['28_*********'],
    desc: null,
    icon: null,
    llrewdId: 45232,
    titleContext: null,
    blessing: '红包一开，笑口常开',
    llpeDetail: [
        {
            llpeType: 'LLCN',
            amount: 88,
            displayAmount: '88',
            displayUnit: '金币',
            openSubTitle: '快手送你金币',
            bottomDesc: null,
        },
    ],
    userIdentity: null,
    bottomInfo: {
        bottomDesc: '已存入「我的钱包」',
        bottomButton: {
            linkType: 'KWAI_LINK',
            linkText: '去查看',
            linkSubText: null,
            linkUrl:
                'kwai://kds/react?bundleId=Cny2025RetentionKrn&componentName=wallet&biz=13627&themeStyle=1&entry_src=entry_src',
            icon: null,
        },
    },
};

export default mockjs.mock({
    result: 1,
    message: '@string',
    data: {
        titleInfo: {
            logo: '',
            title: '环游城市 赚现金',
            subTitle: '环游一圈抽环球影城门票',
        },
        chessboard: {
            progress: {
                currentStep: 0,
                expectTotalStep: 12,
                signed: false,
                currentGridTaskDetail: {},
                gridTaskDegrade: false,
                currentTime: 1668000000000,
                lastStation: false,
            },
            userInfo: {
                userName: 'mockUser',
                headUrl:
                    'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
            },
            station: {
                stationInfo: {
                    uniqueKey: 'baoding',
                    stationName: '保定',
                    stationIcon:
                        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citylight/baoding_light.png?x-kcdn-pid=112543',
                    stationBlackIcon:
                        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/citydark/baoding_dark_new.png?x-kcdn-pid=112543',
                    chessStationIcon:
                        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/baoding_chess.png?x-kcdn-pid=112543',
                    llrewdIcon:
                        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                    stationThemeKey: 'city',
                    signed: false,
                    stationDayIndex: 4,
                    stationTotalStep: 7,
                    stationBubbleIcon:
                        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/blackbox.png?x-kcdn-pid=112543',
                    normalBubbleText: '最高{8888元}',
                    stationBubbleText: '今日打卡/n最高{8888元}',
                    tomorrowBubbleText: '明日打卡/n最高{8888元}',
                    bubbleShowSeconds: 5000,
                    gridSkinUrl: null,
                    gridSkinSponsor: null,
                    gridSkinLocation: [3],
                    stationIconRewardText: '8888',
                    stationIconRewardTextColor: '#00D164',
                    newLlrewdIcon:
                        'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/reward/cashBlack.png?x-kcdn-pid=112543',
                },
                llrewdGridLayout: {
                    CITY_LLCH_GRID: [
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/redPacket.png',
                            gridLocation: 4,
                            displayAmount: 0.12,
                            showAmount: false,
                        },
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/redPacket.png',
                            gridLocation: 5,
                            displayAmount: 0.12,
                            showAmount: true,
                        },
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/redPacket.png',
                            gridLocation: 6,
                            displayAmount: 0.12,
                            showAmount: true,
                        },
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/redPacket.png',
                            gridLocation: 7,
                            displayAmount: 0.12,
                            showAmount: true,
                        },
                    ],
                    CITY_LLCN_GRID: [
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/zhi-jie-huo-de_2x.572d2f7af91510fe.png',
                            gridLocation: 1,
                            displayAmount: 99,
                            showAmount: true,
                        },
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/zhi-jie-huo-de_2x.572d2f7af91510fe.png',
                            gridLocation: 2,
                            displayAmount: 99,
                            showAmount: true,
                        },
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/zhi-jie-huo-de_2x.572d2f7af91510fe.png',
                            gridLocation: 3,
                            displayAmount: 99,
                            showAmount: true,
                        },
                    ],
                    CITY_END_GRID: [
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/2090056226_2x.fdc6c624509672b5.png',
                            gridLocation: 12,
                            showAmount: false,
                        },
                    ],
                    CITY_TASK_GRID: [
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/2036091224_2x.1ee7a5222957f417.png',
                            gridLocation: 11,
                            displayAmount: 99,
                            showAmount: true,
                        },
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/2036091224_2x.1ee7a5222957f417.png',
                            gridLocation: 10,
                            displayAmount: 1688,
                            showAmount: false,
                        },
                        {
                            gridIcon:
                                'https://p2-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/cityPage/2036091224_2x.1ee7a5222957f417.png',
                            gridLocation: 9,
                            showAmount: false,
                            displayAmount: 1688,
                        },
                    ],
                },
            },
            gridTaskPopUp: null,
        },
        accountModel: {
            total: '6.286',
            unit: '元',
        },
        countDown: 5 * 60 * 60 * 1000,
        processingListView: {
            title: '冲到终点必得{25.4元！}',
            processingViews: [
                {
                    text: null,
                    llrewdView: '1.68',
                    icon: 'https://ali.a.yximgs.com/kos/nlav12119/drxDKtGn_2025-07-11-15-09-21.png',
                    step: 1,
                    opened: true,
                },
                {
                    text: null,
                    llrewdView: '1.68',
                    icon: 'https://tx.a.yximgs.com/kos/nlav12119/JQCgnfIm_2025-07-11-15-08-21.png',
                    step: 3,
                    opened: false,
                },
                {
                    text: null,
                    llrewdView: '??',
                    icon: 'https://tx.a.yximgs.com/kos/nlav12119/JQCgnfIm_2025-07-11-15-08-21.png',
                    step: 4,
                    opened: false,
                },
                {
                    text: null,
                    llrewdView: '6.66',
                    icon: 'https://tx.a.yximgs.com/kos/nlav12119/JQCgnfIm_2025-07-11-15-08-21.png',
                    step: 8,
                    opened: false,
                },
                {
                    text: null,
                    llrewdView: '',
                    icon: 'https://ali.a.yximgs.com/kos/nlav12119/reXOiZmr_2025-07-11-15-09-10.png',
                    step: 12,
                    opened: false,
                },
            ],
        },
        cityIcon:
            'https://p4-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sign-in/chess/chongqing_chess.png?x-kcdn-pid=112543',
        popList: [
            // mockDailyGuidePopup,
            // mockPageGuidePopup,
            // mockLLCHTaskSuccessPopup,
        ],
        homeUEConstantsConfig: {
            gridGiftPopupLogo:
                'https://p5-plat.wsukwai.com/kcdn/cdn-kcdn112543/wishTravel/sponsor/kuaishouLogo.png?x-kcdn-pid=112543',
            gridGiftPopupIcon: 'https://ali.a.yximgs.com/kos/nlav12119/hygQfAPN_2025-07-31-14-18-53.png',
        },
        homeFEConstantsConfig: {
            // 页面设置
            frameConfig: {
                themeMap: {
                    // 兜底主题，应对key没对应上的情况
                    city: {
                        // 主题色，状态栏颜色
                        themeColor: '#ff7700',
                        // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                        headerBackMaskBackground:
                            'linear-gradient(179.33deg, #FF7700 9.36%, #FFB545 52.35%, rgba(255, 221, 148, 0) 99.42%)',
                        // 底部背景遮罩颜色
                        footerBackMaskBackground:
                            'linear-gradient(180deg, rgba(255, 208, 180, 0) 0.78%, rgba(255, 220, 191, 0.9) 42.63%, #FFEFD1 100%)',
                        backgroundColor: '#ffefd1',
                    },
                    // 兜底主题，应对key没对应上的情况
                    default: {
                        // 主题色，状态栏颜色
                        themeColor: '#ff7700',
                        // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                        headerBackMaskBackground:
                            'linear-gradient(179.33deg, #FF7700 9.36%, #FFB545 52.35%, rgba(255, 221, 148, 0) 99.42%)',
                        // 底部背景遮罩颜色
                        footerBackMaskBackground:
                            'linear-gradient(180deg, rgba(255, 208, 180, 0) 0.78%, rgba(255, 220, 191, 0.9) 42.63%, #FFEFD1 100%)',
                        backgroundColor: '#ffefd1',
                    },
                },
            },
            inpushConfig: {
                // 兜底轮询间隔
                interval: 60000,
            },
            // 主按钮文案
            mainButtonConfig: {
                // 已签到
                PROCESSING: {
                    buttonText: '向前冲',
                },
                // 免签
                SIGNED: {
                    buttonText: '回主会场',
                },
            },
            gridGiftPopupText: {
                title: '抽奖可获得奖品',
                mainButton: '我知道了',
            },
        },
        cityTourismAccount: {
            nickName: '悠游吉林·吉林文旅',
            avatar: 'https://tx.a.yximgs.com/kos/nlav12119/TTlwBPpF_2025-07-24-11-47-13.png',
            followed: true,
            userId: 123456,
        },
        cityNewsUrl:
            'kwai://krn?bundleId=NewsInformation&componentName=CallForSubmissions&themeStyle=1&bgColor=%23FFFFFF&fromActivity=SummerGrowth&cityName=%E9%87%8D%E5%BA%86',
        cityName: '北京',
    },
    timestamp: '@integer(1, 100)',
    hostname: '@string',
    error_msg: '@string',
});

// 注释掉下面这行代码，则会禁用当前接口的 mock
// export const disable = true;
