import { transformMsToString } from '@pet/yau.core/time';

import type {
    CouponDisplayType,
    PlatformCouponData,
    PlatformCouponDisplayType,
    PlatformCouponStatus,
    UseStatus,
} from './types';

const formatTimestamp = (time: number) => transformMsToString(time, 'yyyy.MM.dd HH:mm');

const displayTypeMap = new Map<PlatformCouponDisplayType, CouponDisplayType>([
    ['image', 'default'],
    ['number', 'rebate'],
    ['discount', 'discount'],
    ['text', 'benefit'],
]);

const useStatusMap = new Map<PlatformCouponStatus, UseStatus>([
    ['unused', 'not_use'],
    ['used', 'has_used'],
    ['expired', 'out_of_date'],
    ['invalid', 'out_of_date'],
    ['view', 'not_use'],
    ['reviewing', 'review'],
    ['reviewFailure', 'not_use'],
    ['received', 'received'],
    ['unStarted', 'not_started'],
]);

function formatCash(value: number, digits?: number) {
    return digits !== undefined ? `${(value / 100).toFixed(digits)}` : `${value / 100}`;
}

const transformSubText = (data: PlatformCouponData) => {
    // 未到开始使用时间
    if (data.status === 'unStarted') {
        return typeof data.validTime === 'number' ? `开始使用时间 ${formatTimestamp(data.validTime)}` : '-- --';
    }
    return Boolean(data.expiredTime) ? `有效期至 ${formatTimestamp(data.expiredTime)}` : '-- --';
};

export function transformCoupon(data: PlatformCouponData) {
    function transMainDesc(data: PlatformCouponData) {
        switch (data.displayType) {
            case 'number':
                return formatCash(data?.value ?? 0);
            case 'discount':
                return `${data?.value ?? 0}`;
            case 'text':
                return data.valueText ?? '';
            default:
                return '';
        }
    }

    return {
        type: displayTypeMap.get(data.displayType) ?? 'default',
        mainDesc: transMainDesc(data),
        subDesc: data?.valueDesc,
        img: data?.image,
        title: data?.name ?? '-- --',
        icon: data?.icon,
        desc: data?.desc,
        value: data?.value !== undefined && data.displayType === 'special' ? formatCash(data.value, 2) : undefined,
        ogValue:
            data?.originalValue !== undefined && data.displayType === 'special'
                ? formatCash(data.originalValue, 2)
                : undefined,
        sub: transformSubText(data),
        status: useStatusMap.get(data.status) ?? 'not_use',
        btnText: data?.forwardText,
        forwardUrl: data?.forwardUrl,
    };
}
