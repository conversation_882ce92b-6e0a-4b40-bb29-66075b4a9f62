<script lang="ts" setup>
import { computed } from 'vue-demi';

import IconCheck from './assets/check-svg.vue';

defineOptions({
    name: 'AdaptCheckbox',
});

interface Props {
    /**
     * v-model值
     */
    modelValue?: boolean;
    /**
     * 禁用
     */
    disabled?: boolean;
    /**
     * 类型
     * @values 'line' | 'flat'
     */
    type?: 'line' | 'flat';
    /**
     * HTML id
     */
    id?: string;
    /**
     * HTML name
     */
    name?: string;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    disabled: false,
    type: 'line',
});

const emit = defineEmits<{
    /**
     * 更新v-model值
     * @arg { boolean } value
     */
    (event: 'update:modelValue', value: boolean): void;
}>();

const activeClass = computed(() => props.modelValue && 'is-active');
const disabledClass = computed(() => (props.disabled ? 'is-disabled' : 'is-able'));
const typeClass = computed(() => `type-${props.type}`);

const computedValue = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
</script>

<template>
    <label class="checkbox-label">
        <span
            role="checkbox"
            class="checkbox"
            :class="[activeClass, disabledClass, typeClass]"
            :aria-checked="`${modelValue}`"
            :aria-disabled="`${disabled}`"
        >
            <IconCheck v-if="modelValue" class="checkbox-icon" />
            <input
                :id="id"
                v-model="computedValue"
                :name="name"
                class="native-checkbox"
                type="checkbox"
                :disabled="disabled"
            />
        </span>
        <span v-if="$slots.default" class="checkbox-text">
            <slot />
        </span>
    </label>
</template>

<style lang="scss">
:root {
    /* checkbox 高度 */
    --adapt-checkbox-height: 14px;
    /* checkbox 未选中颜色 */
    --adapt-checkbox-color: #9c9c9c;
    /* checkbox 选中颜色 */
    --adapt-checkbox-active-color: #fe3666;
    /* checkbox 选中反色 */
    --adapt-checkbox-active-reverse-color: #fff;
    /* checkbox 禁止色 */
    --adapt-checkbox-disable-color: #eaeaea;
    /* checkbox 文案字号 */
    --adapt-checkbox-text-font-size: 13px;
    /* checkbox 文案颜色 */
    --adapt-checkbox-text-color: rgba(16, 1, 16, 0.5);
    /* checkbox 文案链接颜色 */
    --adapt-checkbox-text-link-color: #385080;
    /* checkbox 文案左间距 */
    --adapt-checkbox-text-margin-left: 4px;
}
</style>

<style lang="scss" scoped>
$base-value: 14px;
@function to-em($value) {
    $remValue: calc($value / $base-value) + em;
    @return $remValue;
}

$checkbox-size: to-em($base-value);
$checkbox-icon-width: to-em(8px);
$checkbox-icon-height: to-em(5px);

.checkbox-label {
    display: inline-flex;
    align-items: center;
}

.checkbox {
    font-size: var(--adapt-checkbox-height);
    display: inline-block;
    box-sizing: border-box;
    border: var(--adapt-checkbox-border-width, to-em(1px)) solid;
    width: $checkbox-size;
    height: $checkbox-size;
    color: var(--adapt-checkbox-color);
    border-radius: 50%;
    overflow: hidden;
    position: relative;

    &-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: $checkbox-icon-width;
        height: $checkbox-icon-height;
    }

    &.is-active {
        color: var(--adapt-checkbox-active-color);
    }
    &.type-flat {
        &.is-active {
            background-color: var(--adapt-checkbox-active-color);
            .checkbox-icon {
                color: var(--adapt-checkbox-active-reverse-color);
            }
        }
    }
    &.is-disabled {
        opacity: 0.5;
    }
    &:not(.is-active).is-disabled {
        background: var(--adapt-checkbox-disable-color);
    }
}

.native-checkbox {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0;
}

.checkbox-text {
    font-size: var(--adapt-checkbox-text-font-size);
    color: var(--adapt-checkbox-text-color);
    margin-left: var(--adapt-checkbox-text-margin-left);
    line-height: 1;

    :deep(a) {
        color: var(--adapt-checkbox-text-link-color);
    }
}
</style>
