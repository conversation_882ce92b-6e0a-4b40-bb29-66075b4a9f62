<pet-info lang="json">
{ "title": "功能入口", "description": "功能入口常用icon", "priority": 99 }
</pet-info>

<script setup lang="ts">
import ScanIcon from '../scan-svg.vue';
import WalletIcon from '../wallet-svg.vue';
import TrendsIcon from '../trends-svg.vue';
import StarIcon from '../star-svg.vue';
import RecordIcon from '../record-svg.vue';
import GuideIcon from '../guide-svg.vue';
import RuleIcon from '../rule-bold-svg.vue';
import MusicIcon from '../music-svg.vue';
import SoundOnIcon from '../sound-on-svg.vue';
import SoundOffIcon from '../sound-off-svg.vue';
import ShakeIcon from '../shake-svg.vue';
import ShareIcon from '../share-svg.vue';
import RefreshIcon from '../refresh-svg.vue';
</script>

<template>
    <div>
        <div class="item">
            <ScanIcon class="icon" />
            <h4>
                <p>scan-svg.vue</p>
                <p>扫一扫</p>
            </h4>
        </div>
        <div class="item">
            <WalletIcon class="icon" />
            <h4>
                <p>wallet-svg.vue</p>
                <p>钱包</p>
            </h4>
        </div>
        <div class="item">
            <TrendsIcon class="icon" />
            <h4>
                <p>trends-svg.vue</p>
                <p>动态</p>
            </h4>
        </div>
        <div class="item">
            <StarIcon class="icon" />
            <h4>
                <p>star-svg.vue</p>
                <p>成就</p>
            </h4>
        </div>
        <div class="item">
            <RecordIcon class="icon" />
            <h4>
                <p>record-svg.vue</p>
                <p>记录</p>
            </h4>
        </div>
        <div class="item">
            <GuideIcon class="icon" />
            <h4>
                <p>guide-svg.vue</p>
                <p>攻略</p>
            </h4>
        </div>
        <div class="item">
            <RuleIcon class="icon" />
            <h4>
                <p>rule-bold-svg.vue</p>
                <p>规则</p>
            </h4>
        </div>
        <div class="item">
            <MusicIcon class="icon" />
            <h4>
                <p>music-svg.vue</p>
                <p>音乐</p>
            </h4>
        </div>
        <div class="item">
            <SoundOnIcon class="icon" />
            <h4>
                <p>sound-on-svg.vue</p>
                <p>音效开启</p>
            </h4>
        </div>
        <div class="item">
            <SoundOffIcon class="icon" />
            <h4>
                <p>sound-off-svg.vue</p>
                <p>音效关闭</p>
            </h4>
        </div>
        <div class="item">
            <ShakeIcon class="icon" />
            <h4>
                <p>shake-svg.vue</p>
                <p>震动</p>
            </h4>
        </div>
        <div class="item">
            <ShareIcon class="icon" />
            <h4>
                <p>share-svg.vue</p>
                <p>分享</p>
            </h4>
        </div>
        <div class="item">
            <RefreshIcon class="icon" />
            <h4>
                <p>refresh-svg.vue</p>
                <p>刷新</p>
            </h4>
        </div>
    </div>
</template>

<style src="../assets/css/demo.scss" lang="scss" scoped></style>
