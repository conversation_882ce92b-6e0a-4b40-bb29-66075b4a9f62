<pet-info lang="json">
{
    "title": "自定义风格",
    "description": "可以定义需要的CSS样式"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const numberValueData = ref(100);

function numberChange() {
    const num = 100;
    numberValueData.value += num * Math.random();
}
</script>

<template>
    <div class="demo">
        <h5>原始数值{{ numberValueData }}</h5>
        <div class="container">
            <NumberScroll class="custom" :number-value="numberValueData" :line-height="28" :font-size="16" />
        </div>
        <div class="container">
            <NumberScroll class="custom-2" :number-value="numberValueData" :line-height="48" :font-size="36" />
        </div>
        <button @click="numberChange">增加数值</button>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style>
@import url('https://fonts.googleapis.com/css2?family=Nabla&display=swap');
</style>

<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 24px 0 24px;
}

.custom {
    font-family: 'DIN Alternate', cursive;
    color: #fff;
    :deep(.number-item) {
        background: rgba(0, 0, 0, 0.6);
        padding: 0 8px;
        margin: 0 2px;
        border-radius: 3px;
    }
}

.custom-2 {
    font-family: 'Nabla', system-ui;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings:
        'EDPT' 100,
        'EHLT' 120;
    letter-spacing: 12px;
}
</style>
