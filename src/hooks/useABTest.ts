import { computed } from 'vue';

import { useHomeModel } from '@/models/homeModel';
import { BeginnerGuideStrategy, BeginnerModeOptimize } from '@/types/abTest';

export const useABTest = () => {
    const { abTestConfigView, homeData } = useHomeModel();
    const beginnerGuideStrategy = computed(
        () => abTestConfigView.value?.beginnerGuideStrategy ?? BeginnerGuideStrategy.BASE,
    );

    // 次日强化奖励气泡数据，数据不存在时不展示气泡
    const doubledDetailView = computed(() => abTestConfigView.value?.doubledDetailView);

    // 0714迭代：新标题
    const enableNewMainTitle = computed(() => !!abTestConfigView.value?.enableNewMainTitle);
    // 0714迭代：首日新手引导/次日奖励强化
    const beginnerModeOptimize = computed(
        () => abTestConfigView.value?.beginnerModeOptimize ?? BeginnerModeOptimize.BASE,
    );
    const enableEmphasizeSelected = computed(
        () => beginnerModeOptimize.value === BeginnerModeOptimize.OLD_GUIDE_AND_Emphasize_Llwd,
    );
    // 0714迭代：按钮step气泡强化
    const stepBubbleOptimize = computed(() => enableNewMainTitle.value);
    const isFirstDay = computed(() => homeData.value?.mainButton?.stationDayIndex === 1);
    // 0714迭代：首日/次日奖励强化,仅在第一天生效
    const enableEmphasizeLlwd = computed(
        () =>
            isFirstDay.value &&
            (beginnerModeOptimize.value === BeginnerModeOptimize.NEW_188_GUIDE_AND_Emphasize_Llwd ||
                beginnerModeOptimize.value === BeginnerModeOptimize.OLD_GUIDE_AND_Emphasize_Llwd),
    );

    return {
        doubledDetailView,
        beginnerModeOptimize,
        beginnerGuideStrategy,
        enableNewMainTitle,
        enableEmphasizeLlwd,
        enableEmphasizeSelected,
        stepBubbleOptimize,
    };
};
