<pet-info lang="json">
{ "title": "自定义self动画", "description": "" }
</pet-info>
<script setup lang="ts">
import Pilot from '@pet/adapt.pilot/index.vue';
import { ref } from 'vue';

type PilotType = {
    boot: () => void;
};

const pilotRef = ref<PilotType | null>(null);

const fly = () => {
    pilotRef.value?.boot();
};
</script>

<template>
    <div class="demo">
        <h1>这里是 demo</h1>
        <div class="container">
            <Pilot ref="pilotRef" class="my-pilot" fly-to-target="target" :use-self-animation="true">
                <!-- <Pilot ref="pilotRef" class="my-pilot" fly-to-target="target"> -->
                <div class="start">起始点</div>
            </Pilot>
            <div id="target" class="target">目标点</div>
            <button @click="fly">飞入</button>
        </div>
    </div>
</template>

<style></style>

<style lang="scss" scoped>
div {
    font-size: 16px;
}
.start,
.target {
    width: 100px;
    height: 100px;
}
.start {
    background-color: aqua;
}
.target {
    background-color: sandybrown;
}
.my-pilot {
    :deep(.pilot-self-animation-container) {
        animation: test 500ms linear both;
        @keyframes test {
            0% {
                opacity: 1;
            }
            25% {
                opacity: 0.7;
                transform: rotateX(50deg);
            }
            50% {
                opacity: 0.5;
                transform: scale(1);
            }
            75% {
                opacity: 0.3;
                transform: rotateX(150deg);
            }
            100% {
                opacity: 0;
                transform: scale(0);
            }
        }
    }
}
</style>
