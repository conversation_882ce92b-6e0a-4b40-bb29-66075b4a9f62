<script lang="ts" setup>
import Avatar from '@pet/adapt.avatar/index.vue';
import { useRoute } from 'vue-router';

import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';
import { isInLiveHalf } from '@/utils/live';

import { IpStatusAni } from './types';
import { IPBeforeJumpTime, IPMoveTime } from '../manager/config';
import { RenderZIndex } from '../manager/constant';

const props = defineProps<{
    birthed: boolean;
}>();

const { moving, gUserInfo, ipStyle } = useForwardRushGridModel();
const route = useRoute();
const aniStatus = ref('');
const birthEnd = ref(props.birthed);
const waitingLoop = isInLiveHalf(route) ? 0 : 'infinite';
const birthLoop = isInLiveHalf(route) ? 0 : 'forwards';

const loopStatus = computed(() => {
    return !birthEnd.value ? IpStatusAni.birth : moving.value ? IpStatusAni.jump : IpStatusAni.normalWaiting;
});

onMounted(() => {
    nextTick(() => {
        aniStatus.value = `${loopStatus.value}`;
    });
});

const onAnimatedEnd = () => {
    console.log('birth 🎈🎈onAnimatedEnd');
    nextTick(() => {
        birthEnd.value = true;
    });
};

watch(
    () => loopStatus.value,
    (val) => {
        aniStatus.value = `${val}`;
    },
);

const ipMoveTime = `${IPMoveTime}ms`;
const ipMoveDelayTime = `${IPBeforeJumpTime}ms`;
</script>
<template>
    <div class="ip-ani" :class="[`ip-${aniStatus}`]" @animationend="onAnimatedEnd">
        <div class="ip-bg">
            <Avatar :src="gUserInfo?.headUrl ?? ''" :width="42" class="ip-avatar" default-type="dark" />
        </div>
        <div class="ip-bottom"></div>
    </div>
</template>
<style lang="scss" scoped>
.ip-ani {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transform-origin: bottom center;
    .ip-bg {
        position: absolute;
        width: 50px;
        height: 50px;
        @include bg('../assets/ip/bg.png');
        background-position: center;
        background-size: 100% 100%;
        .ip-avatar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
        }
    }
    .ip-bottom {
        position: absolute;
        bottom: -4px;
        width: 20px;
        height: 14px;
        @include bg('../assets/ip/bottom.png');
        background-position: center;
        background-size: 100% 100%;
        z-index: -1;
    }
}
.ip-birth {
    animation: ip-birth 1.5s steps(20) v-bind(birthLoop);
}
.ip-normalWaiting {
    animation: ip-waiting 1.5s steps(20) v-bind(waitingLoop);
}
.ip-jump {
    animation: ip-moving v-bind(ipMoveTime) v-bind(ipMoveDelayTime) steps(20) 1;
}

@keyframes ip-birth {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 0.3;
        transform: scale(1.3);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes ip-waiting {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes ip-moving {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-30px);
    }
    100% {
        transform: translateY(0);
    }
}
</style>
