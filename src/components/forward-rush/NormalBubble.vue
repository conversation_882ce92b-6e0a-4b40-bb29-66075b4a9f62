<script setup lang="ts">
defineProps<{ mainText: string; subText: string }>();
</script>
<template>
    <div class="normal-bubble common-bg flex-column-center">
        <span class="bubble-text">{{ mainText }}</span>
        <span class="bubble-text bubble-text-space">{{ subText }}</span>
    </div>
</template>
<style lang="scss" scoped>
@use './bubble.scss';
.normal-bubble {
    width: 204px;
    height: 96px;
    @include bg('@/assets/forward-rush/bubble-bg.png');
}
</style>
