import type { ClosePacketProps } from '@pet/25cny.packet/components/ClosePacket.vue';
import type { PlatformCouponData } from '@pet/adapt.coupon/types';
import type { CreatePopupTaskType } from '@pet/adapt.task-queue/task-modules/popupTask';
import { isNotNil } from '@pet/yau.core';

// import CommonClosePacket from '@pet/25cny.packet/CommonClosePacket.vue';
import GridTaskPopup from '@/components/city/popups/GridTaskPopup.vue';
import {
    LinkTypeEnum,
    PopupType,
    WishTravelPrizeType,
    type CommonTaskDetail,
} from '@/services/open-api-docs/home/<USER>/schemas';

import { type PopupBtnClick, type PopupDataType, type PopupTaskConfig } from './types';
import { convertTitleContext } from '../common';

export const gridCommonTaskTypes = [PopupType.GRID_COMMON_TASK_LLCN] satisfies PopupType[];

const getPrizeDetail = (popupData: PopupDataType<typeof gridCommonTaskTypes>) => {
    const customData: Partial<ClosePacketProps> = {};
    const prizeDetail = popupData.llpeDetail
        ?.map((item) => {
            if (item === null) {
                return null;
            }

            if (
                [
                    WishTravelPrizeType.LLCH,
                    WishTravelPrizeType.LLCN,
                    WishTravelPrizeType.TIME_LIMITED_COUNT_LLCH_TASK,
                    WishTravelPrizeType.TIME_LIMITED_COUNT_LLCN_TASK,
                    WishTravelPrizeType.WATCH_LIVE_LLREWD_LLCH_TASK,
                    WishTravelPrizeType.WATCH_LIVE_LLREWD_LLCN_TASK,
                    WishTravelPrizeType.INVOKE_APP_LLREWD_LLCN,
                    WishTravelPrizeType.INVOKE_APP_LLREWD_LLCH,
                    WishTravelPrizeType.FISSION_GOAT_DIVERSATION,
                    WishTravelPrizeType.COMMON_ZT_TASK_LLCN,
                    WishTravelPrizeType.COMMON_ZT_TASK_LLCH,
                    WishTravelPrizeType.SHARE_LLCN_TASK,
                    WishTravelPrizeType.PUSH_SWITCH_LLCN_TASK,
                    WishTravelPrizeType.WATCH_VIDEO_LLCN_TASK,
                    WishTravelPrizeType.COMMON_GRID_TASK_LLCN,
                ].includes(item.llpeType)
            ) {
                return {
                    prizeType: 'amount' as const,
                    amount: item.displayAmount!,
                    unit: item.displayUnit!,
                };
            }

            if (item.llpeType === WishTravelPrizeType.COUPON) {
                return {
                    prizeType: 'coupon' as const,
                    coupon: item.couponView as PlatformCouponData,
                    showUseBtn: item.showUnusedButton ?? false,
                };
            }

            if (
                item.llpeType === WishTravelPrizeType.FOLLOW_LLREWD_LLCH_TASK ||
                item.llpeType === WishTravelPrizeType.FOLLOW_LLREWD_LLCN_TASK
            ) {
                customData.subTitle = item.authorName;
                customData.subTitleIcon = item.authorHeadUrl;
                customData.subTitleIconId = item.authorId;
                return {
                    prizeType: 'amount' as const,
                    amount: item.displayAmount!,
                    unit: item.displayUnit!,
                };
            }
            // if (item.llpeType === RichTreePrize.SOCIAL_POPUP_DIVERSION) {
            //     Object.assign(customData, {
            //         titleContext: convertTitleContext(popupData.titleContext ?? undefined),
            //         subTitleDesc: popupData.subTitleDesc,
            //         iconType: 'open',
            //     });
            // }
            return null;
        })
        .filter((item): item is NonNullable<typeof item> => item !== undefined);

    return {
        customData,
        prizeDetail,
    };
};

export const getTransformData = (
    popupData: PopupDataType<typeof gridCommonTaskTypes>,
    onBtnClick?: PopupBtnClick,
    // eslint-disable-next-line sonarjs/cognitive-complexity
) => {
    const { customData, prizeDetail } = getPrizeDetail(popupData);

    let taskShowInfo;
    const prizeDetailData = popupData.llpeDetail?.[0];
    if (isNotNil(prizeDetailData) && 'taskExtra' in prizeDetailData) {
        // 使用任务数据拼接弹窗
        const task = prizeDetailData.taskExtra as unknown as CommonTaskDetail;
        taskShowInfo = {
            title: task.title ?? '',
            subTitle: task.description ?? '',
            mainButton: {
                linkType: LinkTypeEnum.COMMON_TASK,
                linkText: task.displayText || '去完成',
            },
        };
    } else {
        // 异常情况处理
        taskShowInfo = {
            title: popupData.title ?? '',
            subTitle: popupData.subTitle ?? undefined,
            mainButton: popupData.mainButton ?? undefined,
        };
    }

    // 金色样式
    let styleType: 'gold' | 'red' = 'red';
    if ('goldenStyle' in popupData) {
        styleType = popupData.goldenStyle ? 'gold' : 'red';
    }

    const data = {
        popupType: popupData?.popupType ?? '',
        sponsorLogo: popupData.sponsorLogo ?? undefined,
        sponsorText: popupData.sponsorText ?? undefined,
        titleContext: convertTitleContext(popupData.titleContext ?? undefined),
        blessing: popupData.blessing ?? undefined,
        ...customData,
        ...taskShowInfo,
        prizeDetail,
        btnClick: onBtnClick,
        styleType,
    };

    return data;
};

export const gridCommonTaskTransform = (
    popupData: PopupDataType<typeof gridCommonTaskTypes>,
    createPopupTask: CreatePopupTaskType,
    config?: PopupTaskConfig,
    onBtnClick?: PopupBtnClick,
) => {
    const data = getTransformData(popupData, onBtnClick);
    return createPopupTask({
        component: GridTaskPopup,
        // component: CommonClosePacket,
        data,
        options: config,
    });
};
