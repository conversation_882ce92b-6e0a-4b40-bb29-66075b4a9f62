<script lang="ts" setup>
import { vImage } from '@pet/adapt.directives/image-load';
import Loading from '@pet/adapt.loading/index.vue';
import { computed, ref, onBeforeUnmount, watch } from 'vue-demi';

export type ImageProps = {
    /**
     * 图片地址
     */
    src: string;
    /**
     * 图片加载失败时显示的图片
     */
    fallbackSrc?: string;
    /**
     * 图片加载重试次数
     */
    retryTimes?: number;
    /**
     * 加载方式
     * @values 'lazy' | 'eager'
     */
    loading?: 'lazy' | 'eager';
    /**
     * 跨域设定
     * @values 'anonymous' | 'use-credentials' | ''
     */
    crossorigin?: 'anonymous' | 'use-credentials' | '';
    /**
     * 图片比例
     */
    aspectRatio?: string | number;
    /**
     * 图片填充模式
     * @values 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
     */
    objectFit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';
    /**
     * 加载时长超过多久显示Loading
     */
    spinnerWaitTime?: number;
    /**
     * 图片未加载时候的占位
     */
    placeholder?: string;
    /**
     * img title属性
     */
    title?: string;
    /**
     * img alt属性
     */
    alt?: string;
    /**
     * img srcset 属性
     */
    srcset?: string;
    /**
     * img sizes 属性
     */
    sizes?: string;
    /**
     * loading属性的控制
     * self：使用intersection observer api
     * browser: 优先使用浏览器原生能力
     * @values 'self' | 'browser'
     */
    loadingCtrl?: 'self' | 'browser';
    /**
     * rootMargin
     * loading-ctrl="self" 情况下生效
     */
    rootMargin?: string;
};

export type ImageEmits = {
    /**
     * 加载完成
     */
    (event: 'load'): void;
    /**
     * 加载失败
     */
    (event: 'error'): void;
};

const emit = defineEmits<ImageEmits>();

defineOptions({
    name: 'BaseImage',
});

const TRANSPARENT_IMG =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mN89B8AAskB44g04okAAAAASUVORK5CYII=';

const props = withDefaults(defineProps<ImageProps>(), {
    fallbackSrc: TRANSPARENT_IMG,
    retryTimes: 5,
    loading: 'eager',
    alt: '',
    loadingCtrl: 'browser',
    rootMargin: '0px 50% 50% 0px',
});

const imgRef = ref<HTMLImageElement | null>(null);
const showSpinner = ref(false);
const showHolder = ref(Boolean(props.placeholder));
const imgHolder = computed(() => {
    return showHolder.value
        ? {
              backgroundImage: `url(${props.placeholder!})`,
          }
        : {};
});
const isSupportAspectRatio = computed(() => CSS.supports('aspect-ratio', 'auto'));
const placeholderHeight = computed(() => {
    let ratio = 1;
    if (typeof props.aspectRatio === 'number') {
        ratio = props.aspectRatio;
    } else {
        const ratios = props.aspectRatio?.split('/');
        if (ratios?.length === 2 && ratios[0] != null && ratios[1] != null && +ratios[1] !== 0) {
            ratio = +ratios[0] / +ratios[1];
        }
    }
    // ratio: width / height https://developer.mozilla.org/zh-CN/docs/Web/CSS/aspect-ratio
    return {
        paddingBottom: `${(1 / ratio) * 100}%`,
    };
});

const imgAspectRatio = computed(() => {
    if (props.aspectRatio !== undefined && isSupportAspectRatio.value) {
        return {
            aspectRatio: props.aspectRatio,
        };
    }
    return {};
});
const imgPosition = computed(
    () => props.aspectRatio !== undefined && !isSupportAspectRatio.value && 'set-img-position',
);
const imgLoading = computed(() => showSpinner.value && 'img-loading');
const imgFit = computed(() => props.objectFit !== undefined && `is-${props.objectFit}`);

let timer: ReturnType<typeof setTimeout>;
if (Boolean(props.spinnerWaitTime)) {
    timer = setTimeout(() => {
        showSpinner.value = true;
    }, props.spinnerWaitTime);
}

function resetStatus() {
    if (Boolean(props.spinnerWaitTime)) {
        Boolean(timer) && clearTimeout(timer);
        showSpinner.value = false;
    }
    showHolder.value = false;
}

function onLoad() {
    resetStatus();
    emit('load');
}

function onError() {
    resetStatus();
    emit('error');
}

onBeforeUnmount(() => {
    resetStatus();
});

watch(imgRef, (val) => {
    if (val != null && Boolean(props.crossorigin)) {
        // eslint-disable-next-line no-param-reassign
        val.crossOrigin = props.crossorigin!;
    }
});
</script>

<template>
    <figure class="image-wrapper" :class="[imgPosition, imgLoading]" :style="[imgHolder, imgAspectRatio]">
        <div v-if="showSpinner" class="loading-wrapper">
            <!-- @slot 加载预留位  -->
            <slot name="spinner">
                <Loading class="image-loading" type="circle" />
            </slot>
        </div>
        <img
            ref="imgRef"
            v-image="{ src, fallbackSrc, retryTimes, onLoad, onError, loading, loadingCtrl, rootMargin }"
            class="image-main"
            :class="[imgPosition, imgFit]"
            :title="title"
            :alt="alt"
            :srcset="srcset"
            :sizes="sizes"
        />
        <div v-if="aspectRatio && !isSupportAspectRatio" role="placeholder" :style="placeholderHeight"></div>
    </figure>
</template>

<style>
:root {
    --adapt-image-loading-icon-size: 16px;
}
</style>

<style lang="scss" scoped>
.image-wrapper {
    display: inline-block;
    background-repeat: no-repeat;
    background-size: cover;
    margin: initial;
}
.loading-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}
.image-loading {
    width: var(--adapt-image-loading-icon-size);
    height: var(--adapt-image-loading-icon-size);
}

.image-main {
    vertical-align: top;
    text-indent: -200%;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    &:not([src]) {
        visibility: hidden;
    }
    $objectFitValues: fill contain cover none scale-down;
    @each $value in $objectFitValues {
        &.is-#{$value} {
            object-fit: $value;
        }
    }
}

.set-img-position,
.img-loading {
    position: relative;
}
.set-img-position {
    width: 100%;
    .image-main {
        position: absolute;
        left: 0;
        top: 0;
    }
}
</style>
