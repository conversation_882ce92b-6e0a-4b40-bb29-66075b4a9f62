<script setup lang="ts">
import Picture from '@pet/quantum.PictureImg';
import { computed } from 'vue';

import tailIcon1 from '@/assets/forward-rush/bubble-progress-icon-1.png?preset=modern';
import tailIcon2 from '@/assets/forward-rush/bubble-progress-icon-2.png?preset=modern';
import ProgressIcon from '@/assets/forward-rush/bubble-progress-icon.png?preset=modern';

const props = defineProps<{ mainText: string; stepCount: number; progressDuration: number; progress: string }>();

/** 步数高亮提取 */
const stepDesc = computed(() => {
    const text = props.mainText;
    const matched = text.match(/\{0\}/g)?.[0];
    if (matched) {
        const matchedIndex = text.indexOf(matched);
        return {
            pre: text.slice(0, matchedIndex),
            matched: props.stepCount + '步',
            post: text.slice(matchedIndex + matched.length),
        };
    }
    return {
        pre: text,
        matched: '',
        post: '',
    };
});

const aniWidth = computed(() => `width ${props.progressDuration ?? 360}ms cubic-bezier(0.333, 0.167, 0.166, 0.833)`);
</script>
<template>
    <div class="common-bg step-bubble">
        <div class="bubble-rush-text">
            <span class="bubble-text">{{ stepDesc.pre }}</span>
            <span class="bubble-text bubble-text-highlight">{{ stepDesc.matched }}</span>
            <span class="bubble-text">{{ stepDesc.post }}</span>
        </div>
        <div class="bubble-progress">
            <div class="bubble-progress-bar">
                <div class="bubble-progress-real" :style="{ width: progress, transition: aniWidth }">
                    <div class="tail">
                        <Picture class="tail-icon-1" :src="tailIcon1" />
                        <Picture class="tail-icon-2" :src="tailIcon2" />
                    </div>
                </div>
            </div>
            <Picture class="bubble-progress-icon" :src="ProgressIcon" alt="" />
        </div>
    </div>
</template>
<style lang="scss" scoped>
@use './bubble.scss';
.step-bubble {
    width: 204px;
    height: 96px;
    @include bg('@/assets/forward-rush/bubble-bg.png');
    display: flex;
    flex-direction: column;
    padding-top: 17px;
    padding-left: 21px;
    .bubble-rush-text {
        display: flex;
        flex-direction: row;
        height: 20px;
    }
    .bubble-progress {
        margin-top: 15px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 143px;
        height: 10px;
        position: relative;
        &-bar {
            width: 143px;
            height: 10px;
            border-radius: 12px;
            background-color: #fff;
            overflow: hidden;
            .bubble-progress-real {
                width: 100%;
                height: 10px;
                border-radius: 12px;
                background: linear-gradient(273.58deg, #ffcd4f 7.43%, #fe3666 47.79%, #fe3666 94.81%);
                position: relative;
                overflow: hidden;
                .tail {
                    width: 21px;
                    height: 10px;
                    background: linear-gradient(271.95deg, #f1fa88 1.85%, rgba(241, 250, 136, 0) 71.7%);
                    position: absolute;
                    right: 0;
                    top: 0;
                    .tail-icon-1 {
                        position: absolute;
                        width: 6px;
                        height: 5px;
                        right: 1px;
                        top: 0;
                    }
                    .tail-icon-2 {
                        position: absolute;
                        width: 3px;
                        height: 2.5px;
                        top: 4px;
                        right: 2px;
                    }
                }
            }
        }
        &-icon {
            width: 62.5px;
            height: 61.5px;
            position: absolute;
            top: -30px;
            right: -28px;
        }
    }
}
</style>
