<pet-info lang="json">
{ "title": "通知容器", "description": "" }
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';
import Packet from '../index.vue';
import DragDiv from '../drag-div.vue';

const show = ref(false);
const showAdapt = ref(false);
const showChange = ref(false);
const packetData = ref({});
const adaptPacketData = ref({});
const changePacketData = ref({});
function handleClick() {
    show.value = true;
}

const userInfo = {
    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
    name: '测试1111111',
    userId: '123',
    amount: '8',
    unit: '元',
};

const MOCK_DATA = {
    // popupType: 'WATCH_VIDEO_TODAY',
    headerTitle: '测试弹窗99元标题',
    headerSubTitle: '测试弹窗副标题',
    // assistTitle: '小的副标题',
    title: '可以获得金币奖励',
    mainBtn: {
        linkType: 8,
        linkText: '立即领取',
        linkUrl: '',
    },
    subBtn: {
        linkType: 8,
        linkText: '立即领取',
        linkUrl: '',
    },
    details: {
        assistInfo: {
            desc: '为你助力',
            userList: [
                {
                    avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                    name: '测试1111111',
                    userId: '123',
                    amount: '8',
                    unit: '元',
                },
                // {
                //     avatar: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
                //     name: '测试1111111',
                //     userId: '123',
                //     amount: '8',
                //     unit: '元',
                // },
            ],
        },
        amount: '99.99',
        unit: '元',
        // amountDesc: '可以立即提现哦',
        // midPic: 'https://s1.kskwai.com/kos/nlav111613/united/assets/mid-pic.png',
        // progressInfo: {
        //     startText: '满100提现',
        //     endText: '仅差0.14元',
        //     percent: 0.6,
        // },
        relationInfo: {
            // eslint-disable-next-line sonarjs/no-duplicate-string
            desc: '邀请以下好友奖励更多', // 新增
            totalCount: 12,
            userList: Array.from({ length: 10 }).map(() => userInfo),
        },
        bottomDesc: {
            desc: '已存入「我的钱包」',
            linkType: 0,
            linkText: '立即领取',
            linkUrl: '',
        },
        messageCard: {
            title: '已经开{3/5}个红包，共得{120}元',
            desc: '奖励可在我的钱包中查看',
        },
        style: {
            packetType: 'OPEN', // 'CLOSE' |'OPEN' | 'ACTIVITY'
            midBtnType: 'LOCK', // 'LOCK' | 'UNLOCK'
            // openPacketGapSize: 'SMALL', // 'SMALL' | 'MIDDLE' | 'LARGE'
            cheerLottie: true,
            // cover: true,
            theme: 'RED',
        },
    },
};

const onClose = () => {
    show.value = false;
    showAdapt.value = false;
    showChange.value = false;
};

const openClosePacket = (theme = 'RED', midBtnType = 'LOCK') => {
    packetData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            amount: '99.99',
            unit: '元',
            amountDesc: '可以立即提现哦',
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'CLOSE', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                midBtnType, // 'LOCK' | 'UNLOCK'
                cheerLottie: true,
                theme,
            },
        },
    };
    show.value = true;
};

const openCloseRelationPacket = () => {
    packetData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        subBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            relationInfo: {
                desc: '邀请以下好友奖励更多', // 新增
                totalCount: 12,
                userList: Array.from({ length: 6 }).map(() => userInfo),
            },
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'CLOSE', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                midBtnType: 'LOCK', // 'LOCK' | 'UNLOCK'
                cheerLottie: true,
                theme: 'RED',
            },
        },
    };
    show.value = true;
};
const openOpenPacket = (theme = 'RED') => {
    packetData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            amount: '99.99',
            unit: '元',
            amountDesc: '可以立即提现哦',
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'OPEN', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                cheerLottie: true,
                theme,
            },
        },
    };
    show.value = true;
};

const openGap1 = () => {
    packetData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            amount: '99.99',
            unit: '元',
            amountDesc: '可以立即提现哦',
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'OPEN', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                cheerLottie: true,
                theme: 'RED',
            },
        },
    };

    show.value = true;
};

const openGap2 = () => {
    packetData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            amount: '99.99',
            unit: '元',
            progressInfo: {
                startText: '满100提现',
                endText: '仅差0.14元',
                percent: 0.6,
            },
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'OPEN', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                cheerLottie: true,
                theme: 'RED',
            },
        },
    };

    show.value = true;
};

const openGap3 = (theme = 'RED') => {
    packetData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            relationInfo: {
                desc: '邀请以下好友奖励更多', // 新增
                totalCount: 12,
                userList: Array.from({ length: 10 }).map(() => userInfo),
            },
            amount: '99.99',
            unit: '元',
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'OPEN', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                cheerLottie: true,
                theme,
            },
        },
    };
    show.value = true;
};

const openActivity = () => {
    packetData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            amount: '99.99',
            amountDesc: '金额描述1111111',
            unit: '元',
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'ACTIVITY', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                cheerLottie: true,
                theme: 'RED',
            },
        },
    };
    show.value = true;
};

const openAdapt = () => {
    adaptPacketData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        mainBtn: {
            linkType: 8,
            linkText: '立即领取',
            linkUrl: '',
        },
        details: {
            // assistInfo: {
            //     desc: '为你助力',
            //     userList: Array.from({ length: 2 }).map(() => userInfo),
            // },
            amount: '99.99',
            // amountDesc: '金额描述1111111',
            unit: '元',
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'OPEN', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                cheerLottie: true,
                theme: 'RED',
            },
        },
    };
    showAdapt.value = true;
};

const openChange = () => {
    changePacketData.value = {
        // popupType: 'WATCH_VIDEO_TODAY',
        headerTitle: '测试弹窗99元标题',
        headerSubTitle: '测试弹窗副标题',
        // assistTitle: '小的副标题',
        title: '可以获得金币奖励',
        details: {
            assistInfo: {
                desc: '为你助力',
                userList: Array.from({ length: 2 }).map(() => userInfo),
            },
            amount: '99.99',
            unit: '元',
            bottomDesc: {
                desc: '已存入「我的钱包」',
                linkType: 0,
                linkText: '立即领取',
                linkUrl: '',
            },
            style: {
                packetType: 'CLOSE', // 'CLOSE' |'OPEN' | 'ACTIVITY'
                cheerLottie: true,
                theme: 'RED',
            },
        },
    };
    showChange.value = true;
};
</script>

<template>
    <div class="test">
        <Packet :show="show" v-bind="packetData" fly-target="target" @close="onClose"></Packet>
        <Packet
            class="change-packet"
            :show="showChange"
            v-bind="changePacketData"
            fly-target="fly-target"
            @close="onClose"
        ></Packet>
        <Packet class="ad-packet" :show="showAdapt" v-bind="adaptPacketData" fly-target="fly-target" @close="onClose">
            <div :style="{ width: '200px', height: '130px', background: 'lightblue' }">
                这里是slot内容自适应, 间距可通过css变量调整
            </div>
        </Packet>
        <div>
            1.<button @click="openClosePacket('RED')">红包</button
            ><button @click="openClosePacket('GOLD')">金包</button>
        </div>
        <div>
            <button @click="openClosePacket('RED', 'LOCK')">锁</button
            ><button @click="openClosePacket('RED', 'UNLOCK')">开</button
            ><button @click="openCloseRelationPacket()">关系链</button>
        </div>
        <br />
        <div>
            2.<button @click="openOpenPacket('RED')">红开包</button
            ><button @click="openOpenPacket('GOLD')">金开包</button>
        </div>
        <div>
            <button @click="openGap1">间距1</button><button @click="openGap2">间距2</button
            ><button @click="openGap3('RED')">间距3</button><button @click="openGap3('GOLD')">间距3(金)</button>
        </div>
        <div><button @click="openAdapt">自适应</button></div>
        <br />
        <div>3.<button @click="openActivity">活动红包</button></div>
        <br />
        <div>4.<button @click="openChange">关包换皮</button></div>
        <DragDiv class="drag">
            <div id="target">飞入拖动</div>
        </DragDiv>
    </div>
</template>

<style lang="scss" scoped>
@import '@pet/adapt.fonts/style/puhui105.css';
@import '@pet/adapt.fonts/style/kuaiyuanhui.css';

.test {
    position: relative;
    font-size: 16px;
    width: 100vw;
    height: 100vh;
    padding-top: 100px;
    --adapt-packet-header-title-font-family: kuaiyuanhui;

    .change-packet {
        --adapt-packet-close-type-bg-image: url(../assets/change-skin-bg.png);
        --adapt-packet—inner-top-margin: 46px;
        --adapt-packet-inner-amount-margin-top: 0;
        --adapt-packet-close-type-mid-btn-bottom: 80px;
    }

    .ad-packet {
        --adapt-packet—inner-top-margin: 40px;
        --adapt-packet-inner-amount-margin-top: 0;
    }

    .drag {
        position: absolute;
        top: 0;
        left: 0;
    }
}
</style>
