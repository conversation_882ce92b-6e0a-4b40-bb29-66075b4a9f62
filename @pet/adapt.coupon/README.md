# @pet/adapt.coupon

`index.vue`是适配业务的入口，引用此文件，info 属性传入后端数据，卡券会自动适配展示成对应形态。

如果仅仅需要卡券的展示形式，则可以使用`coupon-ui.vue`，根据自己诉求加工。

卡券基于激励[中台券格式统一](https://docs.corp.kuaishou.com/d/home/<USER>

## index 绑定中台统一数据格式的组件入口

## 属性

| 属性名      | 类型               | 默认值  | 可选值                                                         | 说明                   |
| ----------- | ------------------ | ------- | -------------------------------------------------------------- | ---------------------- |
| info        | PlatformCouponData | -       | -                                                              | 服务端卡券数据         |
| clickAll    | boolean            | false   | -                                                              | 是否整个卡券点击生效   |
| size        | CouponSize         | 'large' | "large" \| "simple" \| "mini" \| "small" \| "medium" \| "full" | 卡券尺寸               |
| showUseBtn  | boolean            | true    | -                                                              | 是否展示立即使用的按钮 |
| btnType     | ButtonType         | -       | "flat" 实心 \| "plain" 空心                                    | 按钮类型               |
| btnText     | string             | -       | -                                                              | 按钮文案               |
| btnDisabled | boolean            | -       | -                                                              | 按钮状态               |
| btnLoading  | boolean            | -       | -                                                              | 按钮加载               |
| noBkg       | boolean            | false   | -                                                              | 是否显示背景           |
| tag         | string             | -       | -                                                              | 标签                   |
| jumpFn      | TSFunctionType     | -       | -                                                              | 传入跳转方法           |
| count       | count              | 1       | -                                                              | 多张卡券一般展示在钱包 |

## 事件

| 事件名       | 载荷                          | 说明     |
| ------------ | ----------------------------- | -------- |
| coupon-click | **info** `PlatformCouponData` | 卡券点击 |

---

## coupon-ui 纯UI展示

## 属性

| 属性名      | 类型              | 默认值    | 可选值                                                                                            | 说明                      |
| ----------- | ----------------- | --------- | ------------------------------------------------------------------------------------------------- | ------------------------- |
| type        | CouponDisplayType | -         | "rebate" \| "discount" \| "default" \| "benefit" \| "special"                                     | 卡券展示类型              |
| size        | CouponSize        | 'small'   | "simple" \| "mini" \| "small" \| "medium" \| "large" \| "full"                                    | 卡券尺寸                  |
| noBkg       | boolean           | -         | -                                                                                                 | -                         |
| status      | UseStatus         | 'not_use' | "not_use" \| "has_used" \| "out_of_date" \| "review" \| "review_fail" \| "not_grant" \| "granted" | 使用状态                  |
| showUseBtn  | boolean           | -         | -                                                                                                 | 显示使用按钮              |
| clickAll    | boolean           | -         | -                                                                                                 | 点击整个卡片生效          |
| valueMain   | string            | -         | -                                                                                                 | 左侧值主要内容            |
| valueDesc   | string            | -         | -                                                                                                 | 左侧值说明                |
| valueImage  | string            | -         | -                                                                                                 | 左侧图链接                |
| mainTitle   | string            | -         | -                                                                                                 | 右侧标题                  |
| mainIcon    | string            | -         | -                                                                                                 | 右侧标题 Icon             |
| mainDesc    | string            | -         | -                                                                                                 | 右侧描述                  |
| mainValue   | string            | -         | -                                                                                                 | 商品卡券`special`价格     |
| mainOgValue | string            | -         | -                                                                                                 | 商品卡券`special`原始价格 |
| mainAttach  | string            | -         | -                                                                                                 | 右侧补充描述              |
| btnType     | ButtonType        | -         | "flat" \| "plain"                                                                                 | 按钮类型                  |
| btnText     | string            | -         | -                                                                                                 | 按钮文案                  |
| btnDisabled | boolean           | -         | -                                                                                                 | 按钮不可用                |
| btnLoading  | boolean           | -         | -                                                                                                 | 按钮加载中                |
| tag         | string            | -         | -                                                                                                 | 卡券标签                  |
| showBtn     | -                 | false     | -                                                                                                 | -                         |

## 事件

| 事件名       | 载荷 | 说明     |
| ------------ | ---- | -------- |
| coupon-click |      | 卡券点击 |

---

## coupon-cube 纯UI展示 方块类型

## 属性

| 属性名      | 类型              | 默认值    | 可选值        | 说明     |
| ----------- | ----------------- | --------- | ------------- | -------- |
| type        | CouponDisplayType | 'default' | 同`coupon-ui` | 卡券类型 |
| valueMain   | string            | -         | -             | 主要值   |
| valueDesc   | string            | -         | -             | 值描述   |
| valueImage  | string            | -         | -             | 图       |
| btnText     | string            | -         | -             | 按钮文案 |
| status      | UseStatus         | 'not_use' | 同`coupon-ui` | 状态     |
| tag         | string            | -         | -             | 标签     |
| btnDisabled | boolean           | -         | -             | 按钮禁用 |
| jump        | -                 | true      | -             | -        |

## 事件

| 事件名       | 载荷 | 说明     |
| ------------ | ---- | -------- |
| coupon-click |      | 卡券点击 |
