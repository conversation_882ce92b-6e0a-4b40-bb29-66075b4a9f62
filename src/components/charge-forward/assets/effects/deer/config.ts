/**
 * transparent-video resource data
 * generate@ 1 for @vision/runtime@^0.7.2-compose.1
 * preview@ https://vision.corp.kuaishou.com/effect/9868
 *
 * wrapper size expected: { width: 94px; height: 400px; }
 */
import '@vision/runtime/adapter/transparent-video';

import entrySrc from './assets/effect.mp4';


export default Object.seal({
    type: 'transparent-video' as const,

    meta: {
        version: 1,
        id: '9868',
        effectVersion: 0,
    },

    layout: {
        adaptive: 414,
    },

    options: {
        /**
         * 默认循环播放
         */
        loop: false,
        /**
         * 默认自动播放
         */
        autoplay: true,
        ratio: 2,
    },


    entrySrc,

    extraData: {
        premultipliedAlpha: false,
        renderWhenLoaded: true,
    },
});