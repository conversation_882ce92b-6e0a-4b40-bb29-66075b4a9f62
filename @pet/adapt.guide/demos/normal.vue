<pet-info lang="json">
{ "title": "普通默认形式", "description": "手指指尖作为对齐圆点，默认absolute" }
</pet-info>
<script lang="ts" setup>
import Guide from '../index.vue';
</script>

<template>
    <div class="frame-box">
        <div class="frame-text">组件位置 50px * 50px</div>
        <Guide class="guide-hand-position" />
    </div>
</template>

<style lang="css" scoped>
.frame-box {
    position: relative;
    width: 200px;
    height: 200px;
    background: rgba(0, 0, 0, 0.5);
    padding: 45px 0 0 10px;
    box-sizing: border-box;
    color: #fff;
    font-size: 12px;
}
.frame-text {
    position: absolute;
    top: 30px;
    left: 10px;
}

.guide-hand-position {
    /* left: 50px;
    top: 50px; */
    --adapt-guide-hand-box-top: 50px;
    --adapt-guide-hand-box-left: 50px;
}
</style>
