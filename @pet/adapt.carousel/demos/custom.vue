<pet-info lang="json">
{ "title": "自定义", "description": "跑马灯弹幕", "priority": 2 }
</pet-info>
<script setup lang="ts">
import { ref } from 'vue-demi';

import carousel from '../carousel.vue';
import carouselItem from '../carousel-item.vue';

const headUrl = 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg';

const data = ref(
    Array.from({ length: 10 }).map((x, i) => {
        if (i === 0) {
            return {
                i,
                headUrl,
                name: '第一个',
                desc: `一个比较长的文案`,
            };
        }
        return {
            i,
            headUrl,
            name: '快手极速版',
            desc: `买了高级礼包`,
        };
    }),
);
</script>

<template>
    <div class="demo">
        <div class="title">精简版动效、展示4条、滚动时间1s</div>
        <div class="container demo1">
            <carousel :list="data" :item-height="50" :show-num="4" :duration="1">
                <template #default="{ item }">
                    <carouselItem :head="item.headUrl" :full-effect="false">
                        {{ `${item.desc}X${item.i}` }}
                    </carouselItem>
                </template>
            </carousel>
        </div>
        <div class="title title-color">弹幕背景色、字色</div>
        <div class="container demo2">
            <carousel :list="data" :item-height="50" :show-num="4" :duration="1">
                <template #default="{ item }">
                    <carouselItem :head="item.headUrl" :full-effect="false">
                        {{ `${item.desc}X${item.i}` }}
                    </carouselItem>
                </template>
            </carousel>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.demo {
    .title {
        font-size: 16px;
    }
    .title-color {
        margin-top: 100px;
    }
}

.demo2 {
    --adapt-carousel-item-background: yellow;
    --adapt-carousel-item-color: red;
}
</style>
