<script setup lang="ts">
/**
 * pet组件使用示例。带动效平台导出尺寸，可直接使用
 * 动效预览：https://vision.corp.kuaishou.com/effect/8781
 *
 * 依赖pet组件 effect-item@^0.4.11
 * 更多组件使用方式、功能见：
 * https://pet-web.corp.kuaishou.com/gallery/detail/?name=@pet/vision.effect-item&version=
 */
import EffectItem from '@pet/vision.effect-item/index.vue';
import { preload } from '@vision/runtime';
import { ref, watch, computed } from 'vue';

import { avatarAvailableJudge } from '@/components/charge-forward/utils';
import { useAnimationPause } from '@/models/animationVisibility';
import { useForwardRushBtnModel } from '@/models/city/forwardRushBtnModel';

import { IpStatusAni } from '../../../user-ip/types';
import defaultAvatar from '../../ip/default-avatar.png';
// import { drawAvatar } from '../../../utils';
const props = defineProps<{
    avatarUrl: string;
}>();

// const replaceData = computed(() => ({avatar: props.avatarUrl}));
const replaceData = ref({ avatar: defaultAvatar });
const { isAnimationPause } = useAnimationPause();
const { showGridPopup } = useForwardRushBtnModel();
const action = defineModel<IpStatusAni>('action', { required: true });

const emit = defineEmits<{
    (e: 'loaded'): void;
    (e: 'stageEnded', stage: IpStatusAni): void;
}>();

// 如果需要依赖动效播放结束ended 事件触发其他逻辑，强烈建议修改effectDuration和在下面添加 :race-end="effectDuration" 保证ended触发。详细见pet组件描述
// const effectDuration = 2000;

// 默认使用dynamic import传入，或可以改成其他promise作异步加载
const effectData = import('./config').then((mod) => mod.default);

const isPlay = ref(false);

const loopActions = [IpStatusAni.normalWaiting, IpStatusAni.stationWaiting];
const isLoop = computed(() => loopActions.includes(action.value));

// 使用import同步加载动效数据。⚠️ pet组件<0.4.9 只支持这种方式使用
// import effectData from './config';

// 使用async function控制加载时机
// const effectData = async () => { /* do something */ return import('./config').then((mod) => mod.default); }

// 如果遇到动效资源加载慢的问题，可以使用preload 进行资源预加载。注意预加载逻辑可能会占用网络带宽
// preload(effectData);
const effectItem = shallowRef<InstanceType<typeof EffectItem> | null>(null);

// const avatarCanvas = drawAvatar(props.avatarUrl, 110, 110);
const onLoaded = async () => {
    isPlay.value = true;
    emit('loaded');

    const avatar = await avatarAvailableJudge(props.avatarUrl);
    replaceData.value = { avatar };
    // const can = await avatarCanvas;
    // can && effectItem.value?.getBaseInstance()?.renderer.elements.forEach((element: { [key:string]: any }) => {
    //     if (element.data.ty === 2 && element.data.nm === 'avatar') {
    //         // eslint-disable-next-line no-param-reassign
    //         element.img = can;
    //         element.renderInnerContent();
    //     }
    // });
};

watch([() => action.value, () => isPlay.value], () => {
    if (isPlay.value) {
        effectItem.value?.playAction(action.value, isLoop.value);
    }
});

const onStageEnded = () => {
    emit('stageEnded', action.value);
    if (action.value === IpStatusAni.beforeJump) {
        action.value = IpStatusAni.jump;
    } else if (action.value === IpStatusAni.jump) {
        action.value = IpStatusAni.afterJump;
    }
};

watch(
    () => isAnimationPause.value,
    (val) => {
        if (val && !showGridPopup.value) {
            effectItem.value?.pause();
        } else {
            effectItem.value?.resume();
        }
    },
);
</script>

<template>
    <EffectItem
        ref="effectItem"
        class="effect-wrapper"
        :data="effectData"
        :replace-data="replaceData"
        @loaded="onLoaded"
        @ended="onStageEnded"
    ></EffectItem>
</template>

<style scoped lang="scss">
.effect-wrapper {
    position: relative;
    width: 150px;
    height: 235px;
    transform: scale(0.79);
    overflow: hidden;
}
</style>
