<script lang="ts" setup>
import { useLogger } from '@/init/logger';
import { useForwardRushGridModel } from '@/models/city/forwardRushGridModel';
import { useCityHomeModel } from '@/models/city/homeModel';
import { useNavigate } from '@/models/utils/useNavigate';

import BackBtn from './BackBtn.vue';
import NewsBtn from './NewsBtn.vue';
import TravelIcon from './TravelIcon.vue';

const { backBtnStyle } = useForwardRushGridModel();
const { cityTourismAccount, cityNewsUrl, cityName, homeDoneStatus } = useCityHomeModel();
const { gotoHomePage } = useNavigate();
const { sendClick } = useLogger();

const handleBackToMain = () => {
    sendClick('OP_ACTIVITY_BACK_BUTTON', {});
    gotoHomePage();
};
</script>

<template>
    <div class="slot-widget-container">
        <div class="sponsor"></div>
        <BackBtn class="back-btn" :style="{ ...backBtnStyle }" @click="handleBackToMain" />
        <TravelIcon
            v-if="cityTourismAccount?.userId && cityTourismAccount?.nickName"
            v-show-log="{
                action: 'OP_ACTIVITY_COUNTRY_PROFILE_BANNER',
                params: { user_id: cityTourismAccount?.userId, user_name: cityTourismAccount?.nickName },
            }"
            :style="{ ...backBtnStyle }"
            class="travel-icon"
            :avatar="cityTourismAccount?.avatar ?? ''"
            :name="cityTourismAccount?.nickName"
            :uid="cityTourismAccount?.userId"
            :followed="cityTourismAccount?.followed ?? false"
            :refresh="homeDoneStatus"
        />
        <NewsBtn
            v-if="cityNewsUrl && cityName"
            :style="{ ...backBtnStyle }"
            :cityNewsUrl="cityNewsUrl"
            :cityName="cityName"
            class="news-btn"
        />
    </div>
</template>

<style lang="scss" scoped>
.slot-widget-container {
    position: absolute;
    width: 52px;
    height: 24px;
    z-index: 98;

    .back-btn {
        top: -17px;
        left: -26px;
    }

    .news-btn {
        top: -232px;
        left: calc(266px + 26px);
    }

    .travel-icon {
        top: -45.5px;
        left: calc(293.5px + 26px);
    }
}
</style>
