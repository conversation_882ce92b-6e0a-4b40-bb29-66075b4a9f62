<pet-info lang="json">
{ "title": "命令调用参数", "description": "基本形式，只需要传参" }
</pet-info>
<script setup lang="ts">
import { getCurrentInstance } from 'vue';
import Button from '@pet/adapt.button/index.vue';
import { useNotice } from '../useNotice';
const currentInstance = getCurrentInstance()?.proxy!;
const { createNotice } = useNotice(currentInstance);

function handleClick() {
    createNotice({
        figure: 'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
        title: '摇一摇PK赢<b style="color: #FA2B40">红包</b>',
        desc: '和附近朋友一起摇一摇赢红包',
        buttonText: '去赢钱',
        attachedText: '',
    });
}
</script>

<template>
    <div>
        <div class="demo-box">
            <Button class="button" @click="handleClick">发送一个通知</Button>
        </div>
        <div class="placeholder"></div>
    </div>
</template>

<style scoped>
@import '@pet/adapt.reset/reset.css';

.demo-box {
    position: fixed;
    inset: 100px 0 0 0;
    margin: auto;
    width: 200px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
}

.placeholder {
    height: 200vh;
}
</style>
