<script setup lang="ts">
import { type VNode, computed, type Ref, useSlots } from 'vue-demi';

import EmptyStatus from '@pet/adapt.empty-status/index.vue';
import Loading from '@pet/adapt.loading/index.vue';
import type { TaskListOptions } from '@pet/25cny.task-list/components/types';
import {
    isTaskCanBeTaken,
    isTaskDisabled,
    isTaskLoading,
    shouldDisplayTask,
} from '@pet/work.task-list-core/utils/displayRelatedUtils';
import { type TaskLogParam, useLoggerEmits } from '@pet/work.task-list-core/utils/useLoggerEmits';

import TaskItem, { type TimeStates } from './TaskItem.vue';
import { useTask } from '@pet/work.task-list-core/useTask';
import type { TaskActionEventInfoSet, TaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';

export interface TaskListProps {
    /** 任务列表的配置 */
    readonly config: TaskListOptions;
}
export interface TaskListEmits {
    (e: 'doTask', task: TaskInfo, index: number, eventInfo?: TaskActionEventInfoSet): void;
    (e: 'taskLog', params: TaskLogParam, taskInfo: TaskInfo): void;
}
const props = defineProps<TaskListProps>();
const emits = defineEmits<TaskListEmits>();
const slots = useSlots();

const { taskList, doTask, pendingTaskIds, isFirstLoading, error, fetchTaskList, timeLimitedTaskList, isLoading } =
    useTask(props.config);

const displayTaskList = computed(() => {
    const filteredTaskList = taskList.value.filter((task) => {
        if (!shouldDisplayTask(task)) {
            return false;
        }
        if (props.config.displayTaskFilter) {
            return props.config.displayTaskFilter(task);
        }
        return true;
    });
    if (props.config.rearrangeTaskList) {
        return props.config.rearrangeTaskList(filteredTaskList);
    }
    return filteredTaskList;
});

const handleDoTask = (task: TaskInfo, eventInfo?: TaskActionEventInfoSet) => {
    doTask(task, eventInfo);
    emits(
        'doTask',
        task,
        taskList.value.findIndex((t) => t.id === task.id),
        eventInfo,
    );
};

const getExposedDoTask = (task: TaskInfo) => {
    return (eventInfo?: TaskActionEventInfoSet) => {
        handleDoTask(task, eventInfo);
    };
};

const getIsTaskLoading = (task: TaskInfo) => {
    return isTaskLoading(task, pendingTaskIds.value);
};

const getTaskTimeLimitedInfo = (task: TaskInfo) => {
    return timeLimitedTaskList.value.find((t) => t.taskId === task.id)!;
};

const errorProps = computed(() => {
    if (!error.value) {
        return {
            props: {
                type: 'inside' as const,
            },
            text: '',
        };
    }

    if (error.value.result === 10002) {
        return {
            props: {
                icon: 'end' as const,
                button: false,
                type: 'inside' as const,
            },
            text: '很遗憾，活动已下线',
        };
    }

    return {
        props: {
            icon: 'network' as const,
            button: true,
            buttonText: '刷新',
            type: 'inside' as const,
        },
        text: '网络似乎断开了，请再试一试',
    };
});

const emptyStatusProps = computed(() => {
    const baseProps = props.config.uiPassThroughConfig?.emptyStatus?.base ?? {};
    const computedProps = errorProps.value?.props ?? {};
    const overrideProps = props.config.uiPassThroughConfig?.emptyStatus?.override ?? {};
    return {
        ...baseProps,
        ...computedProps,
        ...overrideProps,
    };
});

const errorText = computed(() => errorProps.value.text ?? '');

const { registerEmitSource } = useLoggerEmits();

registerEmitSource();
// 后门，某些会场想用已有任务数据但又不想配成任务，又有些会场想在外头用加载态数据
defineExpose({
    taskList,
    isLoading,
    isFirstLoading,
    error,
    fetchTaskList,
});
</script>
<template>
    <div class="task-list">
        <template v-if="isFirstLoading || (errorProps.text && isLoading)">
            <div class="loading">
                <Loading type="infinity" />
                <div class="loading-text">正在加载</div>
            </div>
        </template>

        <template v-else-if="errorProps.text">
            <EmptyStatus class="empty-status" v-bind="emptyStatusProps" @btn-click="fetchTaskList"
                >{{ errorText }}
            </EmptyStatus>
        </template>

        <template v-else>
            <template v-for="(task, index) in displayTaskList">
                <slot
                    v-if="slots.customTaskItem"
                    name="customTaskItem"
                    :task-list="taskList"
                    :task="task"
                    :index="index"
                    :is-loading="getIsTaskLoading(task)"
                    :disabled="isTaskDisabled(task)"
                    :do-task="getExposedDoTask(task)"
                    :time-limited-info="getTaskTimeLimitedInfo(task)"
                ></slot>
                <TaskItem
                    v-else
                    :key="task.id + '-' + task.btnText + '-' + task.title + '-' + index"
                    class="task-item"
                    :class="config.getTaskItemClass ? config.getTaskItemClass(task, index) : ''"
                    :task="task"
                    :index="index"
                    :is-loading="getIsTaskLoading(task)"
                    :hide-button-loading="config.hideButtonLoading"
                    :disabled="isTaskDisabled(task)"
                    :guide-task-id="config.guideTaskId"
                    :time-limited-info="getTaskTimeLimitedInfo(task)"
                    :size="config.size"
                    :ui-pass-through-config="config.uiPassThroughConfig"
                    @doTask="handleDoTask(task, $event)"
                >
                    <template v-if="slots.title" #title="{ timeStates }">
                        <slot
                            name="title"
                            :task="task"
                            :time-states="timeStates"
                            :is-loading="getIsTaskLoading(task)"
                            :is-disabled="isTaskDisabled(task)"
                            :index="index"
                        ></slot>
                    </template>

                    <template v-if="slots.desc" #desc="{ timeStates }">
                        <slot
                            name="desc"
                            :task="task"
                            :time-states="timeStates"
                            :is-loading="getIsTaskLoading(task)"
                            :is-disabled="isTaskDisabled(task)"
                            :index="index"
                        ></slot>
                    </template>

                    <template v-if="slots.figure" #figure="{ timeStates }">
                        <slot
                            name="figure"
                            :task="task"
                            :time-states="timeStates"
                            :is-loading="getIsTaskLoading(task)"
                            :is-disabled="isTaskDisabled(task)"
                            :index="index"
                        ></slot>
                    </template>

                    <template v-if="slots.ctrl" #ctrl="{ timeStates }">
                        <slot
                            name="ctrl"
                            :time-states="timeStates"
                            :task="task"
                            :is-loading="getIsTaskLoading(task)"
                            :is-disabled="isTaskDisabled(task)"
                            :index="index"
                            :do-task="getExposedDoTask(task)"
                        ></slot>
                    </template>

                    <template v-if="slots.timeLimitedTaskItem" #timeLimitedTaskItem="{ timeStates }">
                        <slot
                            name="timeLimitedTaskItem"
                            :time-states="timeStates"
                            :task="task"
                            :is-loading="getIsTaskLoading(task)"
                            :is-disabled="isTaskDisabled(task)"
                            :index="index"
                            :do-task="getExposedDoTask(task)"
                        ></slot>
                    </template>
                </TaskItem>
            </template>
        </template>
    </div>
</template>

<style lang="scss" scoped>
.task-list {
    .task-item {
        &:not(:first-child) {
            margin-top: 12px;
        }
    }
}

.loading {
    display: flex;
    height: 100%;
    width: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    --adapt-infinityLoading-color: #fe3666;
    &-text {
        font-size: 26px;
        line-height: 36px;
    }
}
</style>
