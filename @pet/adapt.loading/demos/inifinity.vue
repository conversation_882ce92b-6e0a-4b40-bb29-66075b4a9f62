<pet-info lang="json">
{ "title": "8字", "description": "8字加载" }
</pet-info>
<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import { ref } from 'vue-demi';

import Loading from '../infinity-loading.vue';

const showLoading = ref(false);

function openLoading() {
    showLoading.value = !showLoading.value;
}
</script>

<template>
    <div>
        <div>默认样式</div>
        <div class="dark">
            <Loading />
        </div>
        <div>彩色loading</div>
        <div class="colorful">
            <Loading />
        </div>
        <div>全屏loading</div>
        <div class="fullscreen">
            <Loading v-show="showLoading" fullscreen />
            <Button class="button" @click="openLoading">
                {{ !showLoading ? '全屏Loading' : '关闭Loading' }}
            </Button>
        </div>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
div {
    display: flex;
    flex-direction: column;
    font-size: 14px;
}
.dark {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    margin: 20px 0;
    background: rgba(0, 0, 0, 0.6);
}
.colorful {
    --infinityLoading-color: #d62828;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    margin-top: 20px;
}
.fullscreen {
    height: 100px;
}
.button {
    position: relative;
    margin-top: 20px;
    z-index: 6;
}
</style>
