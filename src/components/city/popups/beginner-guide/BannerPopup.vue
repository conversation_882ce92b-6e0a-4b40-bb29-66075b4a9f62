<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import type { ClickPosition } from '@pet/25cny.packet/type';
import Popup from '@pet/adapt.popup/index.vue';
import PopupBanner from '@pet/adapt.popup-banner/SummerBanner.vue';
import type { AudioItem } from '@yoda/audio';
import { onMounted, ref } from 'vue';

import BannerEffect from './@effect/effect_9355/EffectIndex.vue';

const { effectShowStatus } = useDowngradeLevel();

const show = ref(true);
const popupRef = ref();
const props = defineProps<{
    rightText: string;
    onShowLog?: () => void;
    playPopupShowSound?: AudioItem;
}>();

const emits = defineEmits<{
    (event: 'end', val: { event: string }): void;
}>();

const position = ref<ClickPosition>('close');

const onClose = () => {
    emits('end', {
        event: position.value,
    });
};

const effectEnded = () => {
    show.value = false;
    popupRef.value?.close();
};
const titleArr = computed(() => {
    return props.rightText?.split('\n');
});

onMounted(() => {
    props.onShowLog?.();
    setTimeout(() => {
        props.playPopupShowSound?.play?.();
    }, 100);
});
</script>

<template>
    <Popup
        v-if="effectShowStatus.L1"
        ref="popupRef"
        class="popup-banner"
        :show="show"
        ani-type="no-effect"
        position="center"
        :hide-mask="false"
        :show-close="false"
        @after-leave="onClose"
    >
        <template #extension>
            <BannerEffect class="banner-effect" :text="rightText" @ended="effectEnded" />
        </template>
    </Popup>
    <PopupBanner
        v-else
        v-model:show="show"
        :hide-mask="false"
        :right-text="titleArr?.[0]"
        :sub-title="titleArr?.[1]"
        class="entry-promotion-banner"
        @end="onClose"
    >
    </PopupBanner>
</template>

<style lang="scss" scoped>
.entry-promotion-banner {
    --adapt-popup-banner-font-family: 'KuaiYuanHuiTi';
    --adapt-popup-banner-extension-z-index: -1;
    --adapt-popup-banner-right-font-size: 32px;
    :deep(.right-text) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-indent: 0;
        letter-spacing: 0;
        .right-text-inner {
            text-align: left;
        }
        .right-text-sub-title {
            font-size: 22px;
            text-align: left;
            padding-left: 10px;
        }
    }

    @include bg('./assets/banner-background.png', --adapt-right-banner-background);
}
</style>
