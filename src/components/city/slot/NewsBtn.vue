<script lang="ts" setup>
import { useOpenPage } from '@pet/yau.yoda';

import backgroundImage from './assets/btn_news.png';

const props = defineProps<{
    cityNewsUrl: string;
    cityName: string;
}>();

const openPage = useOpenPage();

const handleNewsBtnClick = () => {
    if (!props.cityNewsUrl) {
        return false;
    }
    openPage(props.cityNewsUrl);
};
</script>

<template>
    <div class="main-venue">
        <!-- 按钮 -->
        <img :src="backgroundImage" alt="城市新鲜事" class="btn-image" />

        <!-- 点击热区 -->
        <div
            class="click-mask"
            v-show-log="{ action: 'OP_ACTIVITY_COUNTRY_NEW_BANNER', params: { country_name: cityName } }"
            v-click-log="{ action: 'OP_ACTIVITY_COUNTRY_NEW_BANNER', params: { country_name: cityName } }"
            @click="handleNewsBtnClick"
        ></div>
    </div>
</template>

<style scoped>
.main-venue {
    box-sizing: border-box;
    width: 68px;
    height: 24px;
    position: absolute;
}

.btn-image {
    box-sizing: border-box;
    position: absolute;
    top: 0px;
    left: 0px;
    width: 68px;
    height: 24px;
    /* background: linear-gradient(135deg, #FE3666 0%, #FE3666 40.05%, #FF7D49 100%); */
}

.click-mask {
    width: 90px;
    height: 100px;
    position: absolute;
    left: 50%;
    top: -76px;
    transform: translateX(-50%);
}
</style>
