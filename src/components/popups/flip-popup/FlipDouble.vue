<script setup lang="ts">
import coinImg from '@/assets/first-day-0714-exp/coin.png';
import packetImg from '@/assets/first-day-0714-exp/packet.png';
import type { RewardDoubledDetailView } from '@/services/open-api-docs/home/<USER>/schemas';
import { vAnimationDelay } from '@/utils/animationDelay';
import { computedBeishu } from '@/utils/popupTransform/util';

export interface Props {
    data?: RewardDoubledDetailView;
    showAni?: boolean;
}

// 定义组件的 props 并设置默认值
const props = withDefaults(defineProps<Props>(), {});

const isHighest = computed(() => props.data?.tag === '最高');
const isCoin = computed(() => props.data?.displayOriginalLlrewdUnit === '金币');
const beishu = computed(() => computedBeishu(props.data));
</script>

<template>
    <div class="double-layer">
        <div class="double-layer_base" :class="{ 'base-ani': !!showAni }">
            <div class="base-icon">
                <img class="icon" :src="isCoin ? coinImg : packetImg" alt="" />
            </div>
            <div class="llawd">
                <div class="amount">{{ data?.displayOriginalLlrewdAmount || '' }}</div>
                <div v-if="data?.displayOriginalLlrewdUnit" class="unit">{{ data?.displayOriginalLlrewdUnit }}</div>
            </div>
            <div class="llawd-desc">
                {{ data?.originalLlrewdDesc ?? '' }}
            </div>
        </div>
        <div v-animation-delay class="double-layer_arrow" :class="{ 'arrow-ani': !!showAni }">
            <div v-if="beishu" class="beishu">{{ beishu }}倍</div>
        </div>
        <div class="double-layer_double" :class="{ 'double-ani': !!showAni }">
            <div class="double-icon">
                <img class="icon" :src="isCoin ? coinImg : packetImg" alt="" />
                <div
                    v-if="data?.tag"
                    :class="['corner', isHighest ? 'corner-highest' : '', isCoin ? 'corner-coin' : '']"
                >
                    {{ data.tag }}
                </div>
            </div>
            <div class="llawd">
                <div class="amount">{{ data?.displayDoubleLlrewdAmount || '' }}</div>
                <div v-if="data?.displayDoubleLlrewdUnit" class="unit">{{ data?.displayDoubleLlrewdUnit }}</div>
            </div>
            <div class="llawd-desc">
                {{ data?.doubleLlrewdDesc ?? '' }}
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.double-layer {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: baseline;
    justify-content: center;
    position: relative;
    margin-top: 11px;

    .llawd-desc {
        margin-top: 2px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0px;
        color: #550000;
        opacity: 0.5;
        white-space: nowrap;
        text-align: center;
    }

    .amount {
        font-family: 'Alibaba PuHuiTi 3.0';
        font-weight: 800;
        font-size: 18px;
        line-height: 25px;
        letter-spacing: 0px;
    }

    &_base {
        width: 60px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transform-origin: 28.5px 18.5px;

        opacity: 0;
        transform: matrix3d(0.4, -0, 0, 0, 0, 0.4, 0, 0, 0, 0, 1, 0, 75.677, 0, 0, 1);

        .base-icon {
            width: 60px;
            height: 60px;

            .icon {
                width: 100%;
                height: 100%;
                background-size: 100% 100%;
            }

            .corner {
                position: relative;
                top: -50px;
                left: 30px;
                display: inline-block;
                color: #fff;
                font-size: 12px;
                font-weight: 600;
                line-height: 15px;
                padding: 2px 8px;
                width: auto;
                border-radius: 8px 10px 10px 2px;
                border: 1px solid #fff;
                background-color: #f7211d;
                white-space: nowrap;
            }
        }

        .llawd {
            margin-top: 3px;
            width: 100%;
            color: #f7211d;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            height: 25px;

            .amount {
                font-family: AlibabaPuHuiTi-2-95-ExtraBold;
                font-weight: 800;
                font-size: 18px;
                line-height: 25px;
                letter-spacing: 0px;
            }

            .unit {
                font-family: PingFang SC;
                font-size: 14px;
                font-weight: bold;
                line-height: 25px;
                white-space: nowrap;
            }
        }
    }

    .base-ani {
        animation: hash38ee14ea_kf 0.8333333333333334s 0s linear forwards;
    }

    &_arrow {
        width: 68px;
        height: 58px;
        margin: 0 3px 0 1px;
        @include bg('@/assets/first-day-0714-exp/arrow-new.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        transform-origin: 1.7970000505447388px 39.70600128173828px;
        opacity: 0.95829;
        transform: matrix3d(0.433, 0.25, 0, 0, -0.25, 0.433, 0, 0, 0, 0, 1, 0, -11.976, -5.814, 0, 1);
        position: relative;
        z-index: 1;
        .beishu {
            width: 68px;
            font-family: KuaiYuanHuiTi;
            font-weight: 400;
            font-size: 17px;
            line-height: 24px;
            letter-spacing: 0px;
            text-align: center;
            color: #f7211d;
            position: absolute;
            top: -25px;
            left: 13px;
            transform: skew(-5deg);
            white-space: nowrap;
        }
    }
    .arrow-ani {
        animation: hash55a60397_kf 0.4666666666666667s 0.43333333333333335s linear forwards;
    }

    &_double {
        width: 84px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transform-origin: 41.5px 29px;
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
        animation-fill-mode: both;

        .double-icon {
            width: 84px;
            height: 84px;
            position: relative;

            .icon {
                width: 100%;
                height: 100%;
                background-size: 100% 100%;
            }

            .corner {
                position: absolute;
                top: -6px;
                right: -4px;
                display: inline-block;
                color: #fff;
                font-size: 12px;
                font-weight: 600;
                line-height: 18px;
                padding: 0 5.5px;
                width: auto;
                height: 20px;
                border-radius: 8px 10px 10px 2px;
                border: 1px solid #fff;
                background-color: #f7211d;
                box-sizing: border-box;
            }
            .corner-highest {
                background-color: #ffb79d;
            }
            .corner-coin {
                top: 14px;
            }
        }

        .llawd {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-end; /* 底部对齐 */
            color: #f7211d;
            height: 28px;

            .amount {
                font-family: AlibabaPuHuiTi-2-105-Heavy;
                font-size: 28px;
                line-height: 28px;
                letter-spacing: 0px;
            }

            .unit {
                font-family: PingFang SC;
                position: relative;
                top: 1.5px;
                font-size: 19px;
                font-weight: bold;
                line-height: 26px;
                white-space: nowrap;
            }
        }
    }
    .double-ani {
        animation: coin_anim 0.6333333333333333s 0.3333333333333333s linear forwards;
    }
}

// 小金堆
@keyframes hash38ee14ea_kf {
    0% {
        opacity: 1;
        transform: matrix3d(0.4, -0, 0, 0, 0, 0.4, 0, 0, 0, 0, 1, 0, 75.677, 0, 0, 1);
    }

    12% {
        opacity: 1;
        transform: matrix3d(0.85, -0.001, 0, 0, 0.001, 0.85, 0, 0, 0, 0, 1, 0, 75.677, 0, 0, 1);
    }

    24% {
        opacity: 1;
        transform: matrix3d(1, -0.001, 0, 0, 0.001, 1, 0, 0, 0, 0, 1, 0, 75.677, 0, 0, 1);
    }

    36% {
        opacity: 1;
        transform: matrix3d(1.1, -0.001, 0, 0, 0.001, 1.1, 0, 0, 0, 0, 1, 0, 70.978, 0, 0, 1);
    }

    48% {
        opacity: 1;
        transform: matrix3d(1.065, -0.001, 0, 0, 0.001, 1.065, 0, 0, 0, 0, 1, 0, 24.299, 0, 0, 1);
    }

    60% {
        opacity: 1;
        transform: matrix3d(0.962, -0.001, 0, 0, 0.001, 0.962, 0, 0, 0, 0, 1, 0, -9.053, 0, 0, 1);
    }

    72% {
        opacity: 1;
        transform: matrix3d(0.978, -0.001, 0, 0, 0.001, 0.978, 0, 0, 0, 0, 1, 0, -6.188, 0, 0, 1);
    }

    84% {
        opacity: 1;
        transform: matrix3d(1.03, -0.001, 0, 0, 0.001, 1.03, 0, 0, 0, 0, 1, 0, -1.416, 0, 0, 1);
    }

    96% {
        opacity: 1;
        transform: matrix3d(1.006, -0.001, 0, 0, 0.001, 1.006, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    100% {
        opacity: 1;
        transform: matrix3d(1, -0.001, 0, 0, 0.001, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}

// 大金堆

@keyframes coin_anim {
    0% {
        opacity: 0;
        transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    15.789% {
        opacity: 0.779650534424428;
        transform: matrix3d(0.87, 0, 0, 0, 0, 0.87, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    31.579% {
        opacity: 1;
        transform: matrix3d(1.148, 0, 0, 0, 0, 1.148, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    47.368% {
        opacity: 1;
        transform: matrix3d(1.099, 0, 0, 0, 0, 1.099, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    63.158% {
        opacity: 1;
        transform: matrix3d(0.995, 0, 0, 0, 0, 0.995, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    78.947% {
        opacity: 1;
        transform: matrix3d(0.982, 0, 0, 0, 0, 0.982, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    94.737% {
        opacity: 1;
        transform: matrix3d(0.998, 0, 0, 0, 0, 0.998, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }

    100% {
        opacity: 1;
        transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}

// 箭头
@keyframes hash55a60397_kf {
    0% {
        opacity: 0.95829;
        transform: matrix3d(0.433, 0.25, 0, 0, -0.25, 0.433, 0, 0, 0, 0, 1, 0, -11.976, -5.814, 0, 1);
    }

    21.429% {
        opacity: 0.95829;
        transform: matrix3d(0.974, 0.111, 0, 0, -0.111, 0.974, 0, 0, 0, 0, 1, 0, -0.637, -1.807, 0, 1);
    }

    42.857% {
        opacity: 0.95829;
        transform: matrix3d(1.157, -0.181, 0, 0, 0.181, 1.157, 0, 0, 0, 0, 1, 0, 4.035, -0.947, 0, 1);
    }

    64.286% {
        opacity: 0.95829;
        transform: matrix3d(1.102, -0.148, 0, 0, 0.148, 1.102, 0, 0, 0, 0, 1, 0, 3.847, -0.826, 0, 1);
    }

    85.714% {
        opacity: 0.95829;
        transform: matrix3d(1.013, -0.029, 0, 0, 0.029, 1.013, 0, 0, 0, 0, 1, 0, 0.879, -0.191, 0, 1);
    }

    100% {
        opacity: 0.95829;
        transform: matrix3d(0.985, 0, 0, 0, 0, 0.985, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
    }
}
</style>
