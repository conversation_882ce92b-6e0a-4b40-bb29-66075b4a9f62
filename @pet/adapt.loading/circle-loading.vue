<script lang="ts">
export default {
    name: 'AdaptCircleLoading',
};
</script>

<script lang="ts" setup>
import { computed } from 'vue-demi';

interface Props {
    /**
     * colorful为false的时候loading是白色
     */
    colorful?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    colorful: false,
});

const circleType = computed(() => ['circle', props.colorful && 'circle-colorful']);
</script>

<template>
    <span :class="circleType" />
</template>
<style>
:root {
    /* 旋转loading的宽度 */
    --adapt-circleLoading-width: 1em;
    /* 旋转loading的高度 */
    --adapt-circleLoading-height: 1em;
}
</style>
<style lang="scss" scoped>
.circle {
    display: inline-block;
    width: var(--adapt-circleLoading-width);
    height: var(--adapt-circleLoading-height);
    background-image: url('./assets/circle.png');
    background-size: 100% 100%;
    animation: rolling 1s linear infinite;
    &.circle-colorful {
        background-image: url('./assets/circle-dark.png');
    }
}

@keyframes rolling {
    100% {
        transform: rotate(-360deg);
    }
}
</style>
