<script lang="ts" setup>
import { computed } from 'vue-demi';

const props = defineProps<{
    preText: string;
    replaceText?: string[];
}>();

const divider = '$DIVIDER_PLACEHOLDER';

const getHighlightTextList = (text: string, replaceText?: string[]) => {
    const textReplacedPlaceholder = text.replace(/\$\{([^}]*)\}/g, (matched, key: string) => {
        return divider + key + divider;
    });

    const list = textReplacedPlaceholder.split(/\{|\}/);

    if (!replaceText) {
        return list;
    }

    return list
        .filter((_, i) => !(i % 2))
        .reduce((res, text, i) => {
            res.push(text);
            if (replaceText![i]) {
                res.push(replaceText![i]!);
            }
            return res;
        }, [] as string[]);
};

const textList = computed(() => getHighlightTextList(props.preText, props.replaceText));
</script>

<template>
    <div class="text-wrapper">
        <span v-for="(item, i) in textList" :key="i" :class="{ highlight: i % 2 }">{{ item }}</span>
    </div>
</template>

<style lang="scss" scoped>
.text-wrapper {
    display: flex;
    justify-items: center;

    span {
        display: inline-block;
        color: var(--normal-text-color);
        &.highlight {
            margin: var(--adapt-highlight-text-margin);
            color: var(--adapt-highlight-text-color);
            font-size: var(--adapt-highlight-text-font-size);
            font-weight: var(--adapt-highlight-text-font-bold);
        }
    }
}
</style>
