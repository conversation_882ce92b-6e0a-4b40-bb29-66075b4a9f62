<pet-info lang="json">
{ "title": "按钮状态-横向", "description": "常规通用列表，适用于任务面板", "priority": 3 }
</pet-info>
<script lang="ts" setup>
import '@pet/adapt.reset/reset.css';
import Card from '../index.vue';
const taskInfo = {
    // eslint-disable-next-line sonarjs/no-duplicate-string
    figure: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '抽新年福签抽新年福签抽新年福签',
    desc: '抽福签得礼物，愿望金+10测试',
    buttonText: '去打开',
    isLoading: false,
    isDisabled: false,
};

const taskInfo2 = {
    figure: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '关注快手',
    desc: '体力值+20',
    buttonText: '去打开',
    isLoading: true,
    isDisabled: false,
};

const taskInfo3 = {
    figure: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '打开快看点App',
    desc: '体力值+10',
    buttonText: '已完成',
    isLoading: false,
    isDisabled: true,
};
</script>

<template>
    <div class="card-wrapper">
        <h5>size="normal"</h5>
        <Card v-bind="taskInfo" class="card-item"></Card>
        <Card v-bind="taskInfo2" class="card-item"></Card>
        <Card v-bind="taskInfo3" class="card-item"></Card>
    </div>
</template>

<style scoped>
h5 {
    font-size: 16px;
}
.card-wrapper {
    width: 100%;
    background: #f5f5f5;
    padding: 10px;
}

.card-item + .card-item {
    margin-top: 12px;
}

:deep(.g-card-title),
:deep(.g-card-desc) {
    background: #f0f0f0;
}
</style>
