<script setup lang="ts">
import type { Props as PilotProps } from '@pet/adapt.bezier-path-fly/index.vue';
import AdaptLayer from '@pet/adapt.layer/index.vue';
import type { AdaptTransitionName } from '@pet/adapt.transition/types';
import type { AudioItem } from '@yoda/audio';

import ClonePilot from '@/components/common-modals/ClonePilot.vue';
import { useLogger } from '@/init/logger';
import { useSelectProductModel } from '@/models/selectProductModel';
import type { PopupBtnClick } from '@/utils/popupTransform/types';

const props = withDefaults(
    defineProps<{
        hideMask?: boolean;
        sponsorLogo?: string;
        title?: string[];
        subTitle?: string;
        desc?: string;
        icon?: string;
        message?: string;
        mainButton?: string;
        subButton?: string;
        btnClick?: PopupBtnClick;
        flyToTarget?: string;
        /** 要飞入的元素 */
        cloneFlyToTarget: string;
        notFlyToTarget?: Ref<boolean>;
        cloneTargetSelector?: string;
        clonePilotProps?: PilotProps;
        sponsorText?: string;
        bottomText?: string;
        playPopupShowSound?: AudioItem;
        aniType?: AdaptTransitionName;
        btnLoading?: MaybeRef<boolean>;
        bottomDesc?: string;
        /** 商品id */
        productId?: number;
        /** 自动关闭时间，单位毫秒， 为零不自动关闭 */
        autoCloseTime?: number;
    }>(),
    {
        hideMask: false,
        lightType: 'normal',
    },
);
const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'end'): void;
}>();
const { sendShow } = useLogger();
const { setChoiceProductAnim } = useSelectProductModel();

const show = ref(true);

const modalRef: any = useTemplateRef('modalRef');
const pilotRef: any = useTemplateRef('pilotRef');

sendShow('OP_ACTIVITY_REWARD_INFO', { title: props.title, prize_id: props.productId });

/** 要飞的元素 */
const localCloneTarget = computed(
    () => (modalRef.value?.$el as HTMLElement)?.querySelector('.layer-main-image') as HTMLElement | null,
);

const onCloseClick = () => {
    pilotRef.value?.boot();
    setChoiceProductAnim(true);
    show.value = false;
    emits('end');
};

if (props.autoCloseTime) {
    onMounted(() => {
        const timer = setTimeout(() => {
            pilotRef.value?.boot();
            setChoiceProductAnim(true);
            show.value = false;
            emits('end');
        }, props.autoCloseTime);

        onUnmounted(() => {
            clearTimeout(timer);
        });
    });
}
</script>

<template>
    <AdaptLayer
        ref="modalRef"
        v-bind="$attrs"
        class="common-model-wrapper"
        :show="show"
        :has-logo="!!sponsorLogo"
        show-close
        :custom-logo="sponsorLogo"
        :brand-logo-degrade-text="sponsorText"
        :title="title as unknown as string"
        :sub-title="subTitle"
        :cover="icon"
        :info-main="desc"
        :show-main-btn="!!mainButton"
        :second-btn-text="subButton"
        :fly-to-target="flyToTarget"
        :bottom-text="bottomText"
        light-type="weaker"
        :hide-mask="hideMask"
        :ani-type="aniType ?? 'pop-25cny'"
        @close="onCloseClick"
    />
    <ClonePilot
        ref="pilotRef"
        :class="$attrs.class"
        :fly-el="localCloneTarget!"
        :fly-to-target="cloneFlyToTarget"
        hide-source-el
        v-bind="clonePilotProps"
    />
</template>

<style scoped lang="scss"></style>
