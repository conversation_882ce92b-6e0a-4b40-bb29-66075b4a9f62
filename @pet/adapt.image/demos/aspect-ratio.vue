<script setup lang="ts">
import { ref } from 'vue-demi';

import XImage from '../index.vue';
const imgSrcHD =
    'https://3.bp.blogspot.com/-Ms-aCM-ScQ4/X1kAaGttQiI/AAAAAAAACYc/i8JFRyQJtBQxQqEf_f2U28u57--XE_kMgCPcBGAYYCw/w919/kame-house-island-dragon-ball-uhdpaper.com-4K-6.2501-wp.thumbnail.jpg';
</script>

<template>
    <div class="demo">
        <h5>aspect-ratio="3/4" object-fit="cover"</h5>
        <div class="container">
            <XImage class="image-item" :src="imgSrcHD" aspect-ratio="4 / 3" object-fit="cover" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
}
.image-item {
    width: 300px;
}
div {
    font-size: 16px;
}
</style>
