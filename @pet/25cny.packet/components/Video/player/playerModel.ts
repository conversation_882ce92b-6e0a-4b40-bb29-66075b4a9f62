import { createUseModel } from '@gundam/model';
import type { Ref } from 'vue';
import { ref, nextTick } from 'vue';

type IsPlayerMountRef = Ref<boolean>;

export const usePlayerModel = createUseModel(() => {
    const playerMountedRefs: IsPlayerMountRef[] = [];

    const pushPlayer = (): Ref<boolean> => {
        console.log('[playerModel] pushPlayer');

        // 卸载上一个
        const last = playerMountedRefs[playerMountedRefs.length - 1];
        if (last) {
            console.log('[playerModel] 卸载上一个播放器实例');
            last.value = false;
        }

        // 默认 false, nextTick 置为 true
        const mount = ref<boolean>(false);
        playerMountedRefs.push(mount);
        // 在 nextTick 挂载
        nextTick(() => {
            console.log('[playerModel] 挂载新播放器实例');
            mount.value = true;
        });

        return mount;
    };

    const popPlayer = (ref: IsPlayerMountRef) => {
        console.log('[playerModel] popPlayer');

        const indexOfRef = playerMountedRefs.indexOf(ref);
        const current = playerMountedRefs.splice(indexOfRef, 1)[0];
        if (current) {
            console.log('[playerModel] 卸载当前播放器实例');
            current.value = false;
        }

        nextTick(() => {
            const last = playerMountedRefs[playerMountedRefs.length - 1];
            if (last) {
                console.log('[playerModel] 挂载上一个播放器实例');
                last.value = true;
            }
        });
    };

    return {
        pushPlayer,
        popPlayer,
    };
});
