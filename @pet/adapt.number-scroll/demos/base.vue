<pet-info lang="json">
{
    "title": "基础示例",
    "description": "基本用法"
}
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import NumberScroll from '../index.vue';

const numberValueData = ref(666);

function numberChange(type: '+' | '-') {
    const num = 33;
    if (type === '+') {
        numberValueData.value += num;
    } else {
        numberValueData.value -= num;
    }
}
</script>

<template>
    <div class="demo">
        <h5>原始数值{{ numberValueData }}</h5>
        <div class="container">
            <div>入场展示动画</div>
            <NumberScroll
                :font-size="36"
                :number-value="numberValueData"
                :animation-duration="1200"
                :item-effect-delay="233"
            />
            <div>入场不展示动画</div>
            <NumberScroll
                :font-size="36"
                :number-value="numberValueData"
                :animation-duration="1200"
                :item-effect-delay="233"
                :immediate-effect="false"
            />
        </div>
        <button @click="numberChange('+')">增加数值</button>
        <button @click="numberChange('-')">减少数值</button>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.demo {
    width: 100%;
}
.container {
    margin: 48px 0 24px;
    font-size: 12px;
    text-align: center;
}
h5 {
    position: absolute;
    font-size: 14px;
}

button {
    display: block;
}
</style>
