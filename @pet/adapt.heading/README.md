# @pet/adapt.heading

基于同一设计规范，提供组件按钮能力

## 属性

| 名称            | 类型               | 默认值 | 说明               |
| --------------- | ------------------ | ------ | ------------------ |
| title           | string \| string[] | ''     | 主标题文案         |
| subTitle        | string             | ''     | 副（子）标题文案） |
| titleImg        | string             | ''     | 主标题切图         |
| subTitleImg     | string             | ''     | 副（子）标题切图   |
| showLottie      | boolean            | false  | 是否使用默认lottie |
| showBgShine     | boolean            | true   | 是否展示背光       |
| showEnterActive | boolean            | false  | 是否展示入场动效   |

## slot

| 名称     | 说明         |
| -------- | ------------ |
| title    | 主标题       |
| subTitle | 副（子）标题 |
| lottie   | 背景lottie   |
