<script setup lang="ts">
import { ref, computed } from 'vue-demi';

import XImage from '../index.vue';

const imgSrc = ref('https://memeprod.sgp1.digitaloceanspaces.com/meme/314c19b6b3d8a7ee7e07265cd5d27a64.png');
const imgSrcErr = ref('');

const fallBackSrc = 'https://icons.iconarchive.com/icons/paomedia/small-n-flat/256/sign-error-icon.png';
const holderImage = 'https://miro.medium.com/max/720/1*W35QUSvGpcLuxPo3SRTH4w.png';

const imgSrcSlow = computed(() => `https://deelay.me/2000/${imgSrc.value}`);
const imgSrcHD =
    'https://3.bp.blogspot.com/-Ms-aCM-ScQ4/X1kAaGttQiI/AAAAAAAACYc/i8JFRyQJtBQxQqEf_f2U28u57--XE_kMgCPcBGAYYCw/w919/kame-house-island-dragon-ball-uhdpaper.com-4K-6.2501-wp.thumbnail.jpg';
const imgSrcLazy =
    'https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Twemoji_1f600.svg/1280px-Twemoji_1f600.svg.png';

const imgSrcLoad = ref('');
const imgSrcErrLoad = ref('');
function handleLoad() {
    imgSrcLoad.value = '加载成功';
}
function handleError() {
    imgSrcErrLoad.value = '加载失败';
}
</script>

<template>
    <div class="demo">
        <h4>基本使用</h4>
        <h5>加载状态@load: {{ imgSrcLoad }}</h5>
        <div class="container">
            <XImage :src="imgSrc" class="image" srcset="imgavicon144.png 2x" @load="handleLoad" />
        </div>
        <h4>加载设置时间超时显示Loading</h4>
        <h5>:spinner-wait-time="100"</h5>
        <div class="container">
            <XImage :src="imgSrcSlow" class="image" :spinner-wait-time="100" />
        </div>
        <h4>加载重试</h4>
        <h5>:retry-times=3 加载状态@error: {{ imgSrcErrLoad }}</h5>
        <div class="container">
            <XImage :src="imgSrcErr" class="image" :retry-times="3" :fallback-src="fallBackSrc" @error="handleError" />
        </div>
        <h4>占位图</h4>
        <h5>:placeholder="yourPlaceHolderImage"</h5>
        <div class="container">
            <XImage :src="imgSrcSlow" class="image" :placeholder="holderImage" />
        </div>
        <h4>按比例显示</h4>
        <h5>:aspect-ratio="9/16"</h5>
        <div class="container">
            <XImage :src="imgSrcHD" :aspect-ratio="16 / 9" />
        </div>

        <h4>懒加载</h4>
        <h5>loading="lazy"</h5>
        <div class="container is-scroll">
            <h5>向下滚动, 查看请求</h5>
            <XImage :src="imgSrcLazy" class="image" loading="lazy" />
        </div>

        <h4>crossorigin="anonymous"</h4>
        <div class="container">
            <XImage :src="imgSrc" class="image" crossorigin="anonymous" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    font-size: 32px;
}
.container {
    border-bottom: 1px solid #666;
    padding-bottom: 60px;
    &.is-scroll {
        max-height: 200px;
        overflow: auto;
    }
    h5 {
        margin-bottom: 1000px;
    }
}
.image {
    width: 100px;
    height: 100px;
}
</style>
