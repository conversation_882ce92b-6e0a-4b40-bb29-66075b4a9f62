<script lang="ts">
export default {
    name: 'AdaptCard',
};
</script>

<script lang="ts" setup>
import Avatar from '@pet/adapt.avatar/index.vue';
import Box from '@pet/adapt.box/box.vue';
import Button from '@pet/adapt.button/index.vue';
import type { ButtonType, ButtonHeight } from '@pet/adapt.button/types';
import { computed, ref } from 'vue-demi';
import AdaptImage from '@pet/adapt.image/index.vue';

import type { CardSize } from './types';

interface Props {
    /**
     * 用户卡片的头像
     */
    avatar?: string;
    /**
     * 左侧的封面图
     */
    figure?: string;
    /**
     * 卡片是横向展示还是纵向展示
     */
    vertical?: boolean;
    /**
     * 主内容区的标题
     */
    title?: string;
    /**
     * 主内容区的描述
     */
    desc?: string;
    /**
     * 如果带按钮，按钮的文案
     */
    buttonText?: string;
    /**
     * 如果带按钮，按钮的类型
     */
    buttonType?: ButtonType;
    /**
     * 如果带按钮，按钮的高度
     */
    buttonHeight?: ButtonHeight;
    /**
     * 是否在加载中
     */
    isLoading?: boolean;
    /**
     * 按钮不可用
     */
    isDisabled?: boolean;
    /**
     * 按钮是否禁止态但响应点击
     */
    looksLikeDisabled?: boolean;
    /**
     * 附件信息
     */
    attachedText?: string;
    /**
     * 卡片的尺寸
     * normal 默认大小
     * small 在页面中面板内的卡片尺寸
     */
    size?: CardSize;
    /**
     * 卡片描述超出显示...
     */
    descEllipsis?: boolean;
    /**
     * 卡片头像兜底图
     */
    fallbackAvatar?: string;
    /**
     * 卡片 icon 兜底图
     */
    fallbackFigure?: string;
}

const props = withDefaults(defineProps<Props>(), {
    avatar: '',
    vertical: false,
    title: '',
    desc: '',
    buttonText: '',
    isLoading: false,
    isDisabled: false,
    attachedText: '',
    buttonType: 'primary',
    buttonHeight: 38,
    size: 'normal',
    descEllipsis: true,
});
const cardLayout = computed(() => (props.vertical ? 'col' : 'row'));
const sizeMark = computed(() => {
    if (props.size === 'small') {
        return '-small';
    }
    return '';
});
const cardType = computed(() =>
    props.vertical ? `is-vertical${sizeMark.value}-card` : `is-horizontal${sizeMark.value}-card`,
);
const descTextEllipsis = computed(() => props.descEllipsis && 'card-title-cut');
const emit = defineEmits<{
    (event: 'button-click'): void;
    (event: 'ctrl-show'): void;
    (event: 'figure-click'): void;
}>();

function clickHandler() {
    emit('button-click');
}
function figureClickHandler() {
    emit('figure-click');
}
const ctrlElm = ref<HTMLDivElement | null>();
</script>
<template>
    <Box class="card" :class="[cardType]" :direction="cardLayout" align="center">
        <div v-if="$slots.widget" class="card-widget">
            <slot name="widget"></slot>
        </div>
        <div v-if="$slots.figure || avatar || figure" class="card-figure" @click="figureClickHandler">
            <slot v-if="$slots.figure" name="figure" />
            <Avatar v-else-if="avatar" :src="avatar" :fallback-head="fallbackAvatar" class="card-avatar" />
            <AdaptImage v-else-if="figure" :src="figure" class="card-image" :fallback-src="fallbackFigure" />
        </div>
        <div class="card-main">
            <slot name="main" />
            <div v-if="$slots.title || title" :class="['card-title', 'card-title-cut']">
                <slot v-if="$slots.title" name="title" />
                <template v-else>
                    {{ title }}
                </template>
            </div>
            <div v-if="$slots.desc || desc" class="card-desc" :class="descTextEllipsis">
                <slot v-if="$slots.desc" name="desc" />
                <template v-else>
                    {{ desc }}
                </template>
            </div>
        </div>
        <div v-if="$slots.ctrl || buttonText || attachedText" ref="ctrlElm" class="card-ctrl">
            <slot v-if="$slots.ctrl" name="ctrl" />
            <Button
                v-else-if="buttonText"
                :type="buttonType"
                :height="buttonHeight"
                class="card-btn"
                :disabled="isDisabled"
                :looks-like-disabled="looksLikeDisabled"
                :loading="isLoading"
                @click="clickHandler"
            >
                {{ buttonText }}
            </Button>
            <span v-else class="attached-text">
                {{ attachedText }}
            </span>
        </div>
    </Box>
</template>
<style>
:root {
    /* 卡片背景 */
    --adapt-card-background: #fff;
    /* 卡片圆角 */
    --adapt-card-radius: 16px;
    /* 卡片内容pading */
    --adapt-card-padding: unset;
    /* 卡片icon图尺寸：宽高相同 */
    --adapt-card-figure-size: unset;
    /* 标题字号 */
    --adapt-card-title-font-size: unset;
    /* 标题字重 */
    --adapt-card-title-font-weight: 700;
    /* 标题行高 */
    --adapt-card-title-line-height: unset;
    /* 标题字色 */
    --adapt-card-title-color: #222;
    /* 描述文案字色 */
    --adapt-card-desc-color: rgba(0, 0, 0, 0.4);
    /* incon图片、头像与内容区间距 */
    --adapt-card-figure-main-gap: unset;
    /* 标题与描述间距 */
    --adapt-card-title-desc-gap: unset;
    /* 内容区与按钮控制区间距 */
    --adapt-card-main-ctrl-gap: unset;
    /* 按钮控制区文案字号 */
    --adapt-card-ctrl-font-size: unset;
    /* 控制区字色 */
    --adapt-card-ctrl-color: rgba(0, 0, 0, 0.4);
    /* 按钮控制区按钮高度 */
    --adapt-card-col-btn-height: 36px;
    /* 最右侧文案内间距 */
    --adapt-card-attached-text-padding: 0;
}
</style>
<style lang="scss" scoped>
@use 'sass:map';

$h-card: (
    name: 'horizontal',
    type: 'horizontal',
    radius: 16px,
    inner-gap: 12px 16px,
    figure-size: 56px,
    figure-main-gap: 12px,
    title-font-size: 17px,
    title-line-height: 24px,
    desc-font-size: 13px,
    desc-line-height: 18px,
    title-desc-gap: 5px,
    main-ctrl-gap: 12px,
    ctrl-font-size: 14px,
);

$h-card-small: (
    name: 'horizontal-small',
    type: 'horizontal',
    radius: 16px,
    inner-gap: 12px 16px,
    figure-size: 50px,
    figure-main-gap: 10px,
    title-font-size: 16px,
    title-line-height: 22px,
    desc-font-size: 13px,
    desc-line-height: 18px,
    title-desc-gap: 4px,
    main-ctrl-gap: 12px,
    ctrl-font-size: 14px,
);

$v-card: (
    name: 'vertical',
    type: 'vertical',
    radius: 16px,
    inner-gap: 12px 5px 16px,
    figure-size: 50px,
    figure-main-gap: 10px,
    title-font-size: 16px,
    title-line-height: 22px,
    desc-font-size: 11px,
    desc-line-height: 15px,
    title-desc-gap: 5px,
    main-ctrl-gap: 12px,
    ctrl-font-size: 14px,
);

$v-card-small: (
    name: 'vertical-small',
    type: 'vertical',
    radius: 16px,
    inner-gap: 12px 5px 16px,
    figure-size: 50px,
    figure-main-gap: 10px,
    title-font-size: 15px,
    title-line-height: 22px,
    desc-font-size: 11px,
    desc-line-height: 15px,
    title-desc-gap: 5px,
    main-ctrl-gap: 12px,
    ctrl-font-size: 14px,
);

.card {
    background: var(--adapt-card-background) no-repeat;
    background-size: auto 100%;
    border-radius: var(--adapt-card-radius);
    padding: var(--adapt-card-padding);

    &-avatar,
    &-image {
        width: 100% !important;
        height: 100% !important;
    }

    &-figure {
        display: flex;
        min-width: 0;
        flex-shrink: 0;
        width: var(--adapt-card-figure-size);
        height: var(--adapt-card-figure-size);
    }

    &-ctrl {
        min-width: 0;
        flex-shrink: 0;
        font-size: var(--adapt-card-ctrl-font-size);
        color: var(--adapt-card-ctrl-color);
    }

    &-main {
        flex: 1 1 0%;
        min-width: 0;
        max-width: 100%;
    }

    &-title {
        font-size: var(--adapt-card-title-font-size);
        line-height: var(--adapt-card-title-line-height);
        font-weight: var(--adapt-card-title-font-weight);
        color: var(--adapt-card-title-color);

        + .card-desc {
            margin-top: var(--adapt-card-title-desc-gap);
        }
    }

    /* stylelint-disable-next-line no-descending-specificity */
    &-desc {
        font-size: var(--adapt-card-desc-font-size);
        line-height: var(--adapt-card-desc-line-height);
        color: var(--adapt-card-desc-color);
    }

    &-btn,
    .btn {
        vertical-align: top;
    }

    .attached-text {
        padding: var(--adapt-card-attached-text-padding);
    }
}

.card-title-cut {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@each $options in $h-card, $v-card, $h-card-small, $v-card-small {
    .is-#{map.get($options, 'name')}-card {
        --adapt-card-radius: #{map.get($options, 'radius')};
        --adapt-card-padding: #{map.get($options, 'inner-gap')};
        --adapt-card-figure-size: #{map.get($options, 'figure-size')};
        --adapt-card-figure-main-gap: #{map.get($options, 'figure-main-gap')};
        --adapt-card-title-font-size: #{map.get($options, 'title-font-size')};
        --adapt-card-title-line-height: #{map.get($options, 'title-line-height')};
        --adapt-card-desc-font-size: #{map.get($options, 'desc-font-size')};
        --adapt-card-desc-line-height: #{map.get($options, 'desc-line-height')};
        --adapt-card-title-desc-gap: #{map.get($options, 'title-desc-gap')};
        --adapt-card-main-ctrl-gap: #{map.get($options, 'main-ctrl-gap')};
        --adapt-card-ctrl-font-size: #{map.get($options, 'ctrl-font-size')};

        @if (map.get($options, 'type') == 'horizontal') {
            --adapt-card-attached-text-padding: 0 8px 0 0;
        }

        .card-main {
            @if map.get($options, 'type') == 'vertical' {
                text-align: center;
            } @else {
                text-align: left;
            }
        }

        .card-figure + .card-main {
            @if map.get($options, 'type') == 'horizontal' {
                margin-left: var(--adapt-card-figure-main-gap);
            } @else {
                margin-top: var(--adapt-card-figure-main-gap);
            }
        }

        .card-main + .card-ctrl {
            @if map.get($options, 'type') == 'horizontal' {
                margin-left: var(--adapt-card-main-ctrl-gap);
            } @else {
                margin-top: var(--adapt-card-main-ctrl-gap);
            }
        }
    }
}
</style>
