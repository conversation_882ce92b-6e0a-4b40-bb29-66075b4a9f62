<script lang="ts" setup>
import backgroundImage from './assets/btn_home.png';
</script>

<template>
    <div class="main-venue">
        <!-- 按钮 -->
        <img :src="backgroundImage" alt="主会场" class="btn-image" />
    </div>
</template>

<style scoped>
.main-venue {
    box-sizing: border-box;
    height: 24px;
    width: 52px;
    position: absolute;
}

.btn-image {
    box-sizing: border-box;
    position: absolute;
    top: 0px;
    left: 0px;
    width: 52px;
    height: 24px;
    /* background: linear-gradient(135deg, #FE3666 0%, #FE3666 40.05%, #FF7D49 100%); */
}
</style>
