# @pet/adapt.image

增强了图片功能，提供了加载出错，懒加载，展示比例等能力

## 属性

| 属性名          | 类型   | 默认值            | 可选值                                                   | 说明                                                                                            |
| --------------- | ------ | ----------------- | -------------------------------------------------------- | ----------------------------------------------------------------------------------------------- |
| src             | string | -                 | -                                                        | 图片地址                                                                                        |
| fallbackSrc     | string | TRANSPARENT_IMG   | -                                                        | 图片加载失败时显示的图片                                                                        |
| retryTimes      | number | 3                 | -                                                        | 图片加载重试次数                                                                                |
| loading         | union  | 'eager'           | 'lazy' \| 'eager'                                        | 加载方式                                                                                        |
| crossorigin     | union  | -                 | 'anonymous' \| 'use-credentials' \| ''                   | 跨域设定                                                                                        |
| aspectRatio     | union  | -                 | -                                                        | 图片比例                                                                                        |
| objectFit       | union  | -                 | 'fill' \| 'contain' \| 'cover' \| 'none' \| 'scale-down' | 图片填充模式                                                                                    |
| spinnerWaitTime | number | -                 | -                                                        | 加载时长超过多久显示 Loading                                                                    |
| placeholder     | string | -                 | -                                                        | 图片未加载时候的占位                                                                            |
| title           | string | -                 | -                                                        | img title 属性                                                                                  |
| alt             | string | ''                | -                                                        | img alt 属性                                                                                    |
| srcset          | string | -                 | -                                                        | img srcset 属性                                                                                 |
| sizes           | string | -                 | -                                                        | img sizes 属性                                                                                  |
| loadingCtrl     | union  | 'browser'         | 'self' \| 'browser'                                      | loading 属性的控制<br/>self：使用 intersection observer api<br/>browser: 优先使用浏览器原生能力 |
| rootMargin      | string | '0px 50% 50% 0px' | -                                                        | rootMargin<br/>loading-ctrl="self" 情况下生效                                                   |

## 事件

| 事件名 | 载荷 | 说明     |
| ------ | ---- | -------- |
| load   |      | 加载完成 |
| error  |      | 加载失败 |

## 插槽

| 名称    | 说明       | 绑定 |
| ------- | ---------- | ---- |
| spinner | 加载预留位 | -    |
