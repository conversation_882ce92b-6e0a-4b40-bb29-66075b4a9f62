<pet-info lang="json">
{ "title": "横向", "description": "横向滚动" }
</pet-info>

<script setup lang="ts">
import Marquee from '../index.vue';
</script>

<template>
    <Marquee class="marquee-box-row">
        <div class="item">滚动文案滚动文案滚动文案1</div>
        <div class="item">滚动文案滚动文案滚动文案2</div>
        <div class="item">滚动文案滚动文案滚动文案3</div>
    </Marquee>
</template>

<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.marquee-box-row .item {
    margin-right: 20px;
}
</style>
