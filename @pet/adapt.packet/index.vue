<script lang="ts">
export default {
    name: 'AdaptPacket',
};
</script>
<script lang="ts" setup>
import Box from '@pet/adapt.box/box.vue';
import SFTransition from '@pet/adapt.transition/index.vue';
import { ref, watch, nextTick, computed, defineProps, onBeforeUnmount } from 'vue-demi';
import { useBarInfo, isAtFourTab, isInImmersiveTab } from '@pet/yau.core';
import CloseBtn from '@pet/adapt.icons/close-big-solid-svg.vue';
import PopupMask from './mask/mask.vue';
import Heading from '@pet/adapt.heading/index.vue';
import BezierFly from '@pet/adapt.bezier-path-fly/index.vue';
import MainContent from './content/content.vue';
import AssistInfo from './assist-info/assist-info.vue';
import BtmBtn from './btm-button/btm-button.vue';
import GroupPopover from './group-popover/group-popover.vue';
import BgLottie from './lotties/bg-light/EffectIndex.vue';
import CheerLottie from './lotties/cheer-lottie/EffectIndex.vue';
import MessageCard from './message-card/message-card.vue';
import type { PacketProps, PilotType } from './types';
const LOCK_SCREEN_CLASS = 'u-lock-screen';

const props = withDefaults(defineProps<PacketProps>(), {
    show: false,
    flyTarget: '',
    innerScroll: true,
    needTabSpace: true,
});

const emits = defineEmits<{
    /**
     * 进入前
     */
    (event: 'beforeEnter'): void;
    /**
     * 进入
     */
    (event: 'enter'): void;
    /**
     * 进入后
     */
    (event: 'afterEnter'): void;
    /**
     * 离开前
     */
    (event: 'beforeLeave'): void;
    /**
     * 离开
     */
    (event: 'leave'): void;
    /**
     * 离开后
     */
    (event: 'afterLeave'): void;
    /**
     * 触发关闭
     */
    (event: 'close'): void;
    /**
     * 点击关闭按钮
     */
    (event: 'closeBtnClick'): void;
    /**
     * 中部圆形按钮点击
     */
    (event: 'midBtnClick'): void;
    /**
     * 主按钮点击
     */
    (event: 'mainBtnClick'): void;
    /**
     * 副按钮点击
     */
    (event: 'subBtnClick'): void;
    /**
     * 底部描述点击
     */
    (event: 'btmDescClick'): void;
    /**
     * 邀请头像点击
     */
    (event: 'inviteAvatarClick'): void;
}>();

// 弹窗整体展示消失动效事件
const beforeEnter = () => {
    emits('beforeEnter');
};
const enter = () => {
    emits('enter');
};
const afterEnter = () => {
    emits('afterEnter');
};
const beforeLeave = () => {
    emits('beforeLeave');
};
const leave = () => {
    initState();
    emits('leave');
};
const afterLeave = () => {
    emits('afterLeave');
};

// 蒙层滚动透出处理
let scrollTop = 0;
const root = document.documentElement;
const app = document.body;
const orgTop = getComputedStyle(app).getPropertyValue('top');

const setLock = () => {
    scrollTop = window.scrollY || root.scrollTop;
    if (root != null) {
        root.classList.add(LOCK_SCREEN_CLASS);
    }
    if (app != null) {
        app.style.top = `-${scrollTop}px`;
    }
};
const removeLock = () => {
    if (root != null) {
        root.classList.remove(LOCK_SCREEN_CLASS);
        window.scrollTo(0, scrollTop);
    }

    if (app != null) {
        if (orgTop === 'auto') {
            app.style.removeProperty('top');
        } else {
            app.style.top = orgTop;
        }
    }
};

// 兼容处理
const barInfo = useBarInfo();
const fixSpace = computed(() => {
    return {
        bottom: Boolean(barInfo.toolBarHeight) ? `${barInfo.bottomHeight}px` : 0,
        paddingTop: isAtFourTab() && !isInImmersiveTab() && props.needTabSpace ? `${barInfo.statusBarHeight}px` : 0,
    };
});

const showRef = ref(false);
const showGroupPopover = ref(false);
const wrapperShowRef = ref(props.show);
const isInit = ref(true);
const pilotRef = ref<PilotType | null>(null);

const changeLeaveDuration = ref(false);
const validFlyTarget = computed(() => {
    return Boolean(props.flyTarget && document.getElementById(props.flyTarget));
});
const needLeaveFly = computed(() => changeLeaveDuration.value || validFlyTarget.value);
// 动画时长
const maskTransDuration = computed(() => {
    return {
        enter: 233,
        leave: 233,
    };
});
const maskTransDelay = {
    enter: 67,
};

const initState = () => {
    isInit.value = false;
    changeLeaveDuration.value = false;
};

const aniTypeComputed = computed(() => {
    // flyout use fade leave animation
    if (isInit.value && needLeaveFly.value) {
        return 'flyout';
    }
    return 'packet-anim';
});

watch(
    () => props.show,
    (val) => {
        showRef.value = val;
        if (val) {
            nextTick(() => {
                wrapperShowRef.value = val;
            });
            setLock();
        } else {
            removeLock();
        }
    },
    {
        immediate: true,
    },
);

const onGroupClick = () => {
    showGroupPopover.value = !showGroupPopover.value;
};

// 主题样式
const theme = computed(() => {
    if (
        props.details?.style?.packetType &&
        props.details?.style?.theme &&
        ['CLOSE', 'OPEN'].includes(props.details?.style.packetType) &&
        ['GOLD', 'RED'].includes(props.details?.style.theme)
    ) {
        return `${props.details?.style?.packetType!}-${props.details?.style?.theme!}`;
    }
    return 'ACTIVITY';
});

// 间距规则
const openPacketGapSize = computed(() => {
    if (props.details?.style?.packetType !== 'OPEN') {
        return '';
    }
    if (
        props.details?.style?.openPacketGapSize &&
        ['SMALL', 'MIDDLE', 'LARGE'].includes(props.details?.style?.openPacketGapSize)
    ) {
        return props.details?.style?.openPacketGapSize;
    }
    if (props.details?.relationInfo || props.details?.midPic) {
        return 'LARGE';
    } else if (props.details?.progressInfo) {
        return 'MIDDLE';
    } else {
        return 'SMALL';
    }
});

const triggerLeave = () => {
    showRef.value = false;
    if (validFlyTarget.value) {
        changeLeaveDuration.value = true;
        pilotRef.value?.boot();
    }
};

function handleCloseBtnClick() {
    emits('closeBtnClick');
    changeLeaveDuration.value = false;
    showGroupPopover.value = false;
    triggerLeave();
}

const handleMainContentLeave = () => {
    wrapperShowRef.value = false;
    nextTick(() => {
        emits('close');
    });
};

onBeforeUnmount(async () => {
    if (props.innerScroll && isInit.value) {
        removeLock();
    }
    initState();
});
</script>

<template>
    <SFTransition
        appear
        name="no-effect"
        @before-enter="beforeEnter"
        @enter="enter"
        @after-enter="afterEnter"
        @leave="leave"
        @before-leave="beforeLeave"
        @after-leave="afterLeave"
    >
        <Box
            v-if="wrapperShowRef"
            justify="center"
            align="center"
            direction="col"
            :class="['popup', theme, openPacketGapSize]"
            :style="fixSpace"
        >
            <SFTransition appear :duration="maskTransDuration" :delay="maskTransDelay">
                <PopupMask v-if="showRef" class="popup-mask" />
            </SFTransition>
            <SFTransition appear :duration="maskTransDuration" :delay="maskTransDelay">
                <div v-if="showRef" class="background-lottie">
                    <!-- 背景动效 -->
                    <BgLottie class="lottie" />
                    <!-- 氛围动效 -->
                    <CheerLottie class="lottie" />
                    <slot name="bg-lottie" />
                </div>
            </SFTransition>
            <div class="popup-main">
                <div v-if="showRef" class="header">
                    <CloseBtn class="close-btn" @click="handleCloseBtnClick" />
                    <Heading
                        :title="[headerTitle ?? '', headerSubTitle ?? '']"
                        :sub-title="assistTitle ?? ''"
                        :show-bg-shine="false"
                        class="packet-header"
                    >
                        <template v-if="details?.assistInfo?.userList.length" #subTitle>
                            <!-- 助力信息 -->
                            <AssistInfo :info="details.assistInfo" />
                        </template>
                        <slot name="header" />
                    </Heading>
                </div>
                <Transition :name="aniTypeComputed" appear @after-leave="handleMainContentLeave">
                    <div v-if="showRef || changeLeaveDuration" class="content">
                        <BezierFly ref="pilotRef" fly-to-target="target" @end="handleMainContentLeave">
                            <!-- 固定高单红包样式 -->
                            <div v-if="details?.style && details.style.packetType !== 'OPEN'" class="normal-packet">
                                <MainContent
                                    v-bind="props"
                                    @group-click="onGroupClick"
                                    @invite-avatar-click="emits('inviteAvatarClick')"
                                >
                                    <slot />
                                </MainContent>
                                <!-- CLOSE 关闭样式红包展示中部按钮区 -->
                                <div
                                    v-if="details.style?.packetType === 'CLOSE'"
                                    :class="['mid-btn', details.style?.midBtnType === 'UNLOCK' && 'unlock-type']"
                                    @click="emits('midBtnClick')"
                                ></div>
                                <!-- ACTIVITY 样式红包展示按钮组 -->
                                <BtmBtn
                                    v-if="details.style?.packetType === 'ACTIVITY' && mainBtn"
                                    class="btn-group"
                                    :main-btn="mainBtn"
                                    :sub-btn="subBtn"
                                    :bottom-desc="details?.bottomDesc"
                                    @main-btn-click="emits('mainBtnClick')"
                                    @sub-btn-click="emits('subBtnClick')"
                                    @btm-desc-click="emits('btmDescClick')"
                                />
                            </div>
                            <!-- 上半部分自适应红包样式 -->
                            <div v-else class="adapt-packet">
                                <div class="top-part">
                                    <div class="background-wrapper">
                                        <div class="top"></div>
                                        <div class="middle"></div>
                                    </div>
                                    <MainContent
                                        :class="['open-content']"
                                        v-bind="props"
                                        @group-click="onGroupClick"
                                        @invite-avatar-click="emits('inviteAvatarClick')"
                                    >
                                        <slot />
                                    </MainContent>
                                </div>
                                <div class="bottom-part">
                                    <BtmBtn
                                        v-if="mainBtn"
                                        :main-btn="mainBtn"
                                        :sub-btn="subBtn"
                                        :bottom-desc="details?.bottomDesc"
                                        @main-btn-click="emits('mainBtnClick')"
                                        @sub-btn-click="emits('subBtnClick')"
                                        @btm-desc-click="emits('btmDescClick')"
                                    />
                                </div>
                            </div>
                        </BezierFly>
                    </div>
                </Transition>
                <div v-show="showRef" class="footer">
                    <Transition v-if="!subBtn" name="button" appear>
                        <div
                            v-if="details?.style?.packetType === 'CLOSE' && mainBtn"
                            class="btm-btn"
                            @click="emits('mainBtnClick')"
                        >
                            {{ mainBtn.linkText }}
                        </div>
                    </Transition>
                    <MessageCard v-if="details?.messageCard" class="message-card" :info="details.messageCard" />
                    <slot name="footer" />
                </div>
            </div>
            <GroupPopover
                v-if="showGroupPopover && details?.relationInfo"
                :show="showGroupPopover"
                :info="details.relationInfo"
            />
        </Box>
    </SFTransition>
</template>

<style>
:root {
    /* 红包标题间隙 */
    --adapt-packet-header-title-margin-bottom: 20px;
    /* 红包外部 - 头部主标题文字字体 */
    --adapt-packet-header-title-font-family: kuaiyuanhui;
    /* 红包外部 - 关闭按钮 - 颜色 */
    --adapt-packet-close-btn-color: #999999;
    /* 红包外部 - 底部大按钮 - 字色 */
    --adapt-packet-footer-btn-font-color: unset;
    /* 红包外部 - 底部大按钮 - 背景 */
    --adapt-packet-footer-btn-bg-image: unset;

    /* 红包内部 - 上边距 */
    --adapt-packet—inner-top-margin: unset;
    /* 红包内部 - 标题 - 字体颜色 */
    --adapt-packet-inner-title-color: unset;
    /* 红包内部 - 金额 - 字体颜色 */
    --adapt-packet-inner-amount-color: unset;
    /* 红包内部 - 金额 - 单位 - 字体颜色 */
    --adapt-packet-inner-amount-unit-color: unset;
    /* 红包内部 - 金额 - 背景渐变 */
    --adapt-packet-inner-amount-background: unset;
    /* 红包内部 - 金额 - 距离顶部间距 */
    --adapt-packet-inner-amount-margin-top: unset;
    /* 红包内部 - 金额 - 距离顶部间距 */
    --adapt-packet-inner-amount-margin-bottom: 0px;
    /* 红包内部 - 金额区内 - 描述 - 上边距 */
    --adapt-packet-inner-amount-desc-margin-top: 14px;
    /* 红包内部 - 金额 - 描述 - 字体颜色 */
    --adapt-packet-inner-amount-desc-color: unset;
    /* 红包内部 - 关系链 - 描述 - 字体颜色 */
    --adapt-packet-inner-relation-desc-color: unset;
    /* 红包内部 - 关系链 - 描述 - 字体大小 */
    --adapt-packet-inner-relation-desc-font-size: unset;
    /* 红包内部 - 关系链 - 描述 - 字重 */
    --adapt-packet-inner-relation-desc-font-weight: unset;
    /* 红包内部 - 关系链 - 描述 - 字体 */
    --adapt-packet-inner-amount-font-family: 'PingFang SC';
    /* 红包内部 - 关系链 - 头像组 - 上边距 */
    --adapt-packet-inner-relation-avatar-margin-top: unset;
    /* 红包内部 - 关系链 - 头像组 - 头像边框色 */
    --adapt-packet-inner-relation-avatar-border-color: unset;

    /* 红包内部 - 中部图 - 宽 */
    --adapt-packet-inner-mid-pic-width: 236px;
    /* 红包内部 - 中部图 - 高 */
    --adapt-packet-inner-mid-pic-height: 106px;
    /* 红包内部 - 中部图 - 圆角 */
    --adapt-packet-inner-mid-pic-radius: 16px;
    /* 红包内部 - 按钮 - 字体 */
    --adapt-packet-inner-btn-font-family: 'PingFang SC';
    /* 红包内部 - 单按钮 - 宽 */
    --adapt-packet-inner-btn-width: 188px;
    /* 红包内部 - 单按钮 - 高 */
    --adapt-packet-inner-btn-height: 66px;
    /* 红包内部 - 单按钮 - 背景图 */
    --adapt-packet-inner-btn-bg-image: url(./assets/big-bt-bg.png);
    /* 红包内部 - 单按钮 - 字色 */
    --adapt-packet-inner-btn-font-color: unset;
    /* 红包内部 - 单按钮 - 字体大小 */
    --adapt-packet-inner-btn-font-size: 18px;
    /* 红包内部 - 双按钮 - 宽 */
    --adapt-packet-inner-group-btn-width: 115px;
    /* 红包内部 - 双按钮 - 高 */
    --adapt-packet-inner-group-btn-height: 60px;
    /* 红包内部 - 双按钮 - 字体大小 */
    --adapt-packet-inner-group-btn-font-size: 16px;
    /* 红包内部 - 双按钮 - 左 - 字色 */
    --adapt-packet-inner-left-btn-font-color: unset;
    /* 红包内部 - 双按钮 - 右 - 字色 */
    --adapt-packet-inner-right-btn-font-color: unset;
    /* 红包内部 - 双按钮 - 左 - 背景图 */
    --adapt-packet-inner-left-btn-bg-image: 236px;
    /* 红包内部 - 双按钮 - 右 - 背景图 */
    --adapt-packet-inner-right-btn-bg-image: 106px;
    /* 红包内部 - 底部描述 - 字色 */
    --adapt-packet-inner-btm-desc-font-color: unset;
    /* 红包内部 - 底部描述 - 跳转icon */
    --adapt-packet-inner-btm-desc-icon-bg: unset;
    /* 红包内部 - 底部描述 - 上边距 */
    --adapt-packet-inner-btm-desc-margin: 6px;

    /* 开启态红包 - 背景图 - 上部分 */
    --adapt-packet-open-type-top-bg-image: unset;
    /* 开启态红包 - 背景图 - 下部分 */
    --adapt-packet-open-type-btm-bg-image: unset;

    /* 关闭态红包 - 背景图 */
    --adapt-packet-close-type-bg-image: unset;
    /* 关闭态红包 - 中部圆形按钮 - 图片 */
    --adapt-packet-close-type-mid-btn-bg-image: unset;
    /* 关闭态红包 - 中部圆形按钮 - 宽 */
    --adapt-packet-close-type-mid-btn-width: unset;
    /* 关闭态红包 - 中部圆形按钮 - 高 */
    --adapt-packet-close-type-mid-btn-height: unset;
    /* 关闭态红包 - 中部圆形按钮 - 圆角 */
    --adapt-packet-close-type-mid-btn-radius: unset;
    /* 关闭态红包 - 中部圆形按钮 - 距离底部距离 */
    --adapt-packet-close-type-mid-btn-bottom: unset;

    /* 活动态红包 - 按钮组 - 底部距离 */
    --adapt-packet-activity-btn-bottom: 20px;
}
</style>

<style lang="scss" scoped>
@use 'sass:map';
@import './common.scss';

.CLOSE-RED {
    --adapt-packet—inner-top-margin: 46px;
    --adapt-packet-footer-btn-font-color: #ff0b24;
    --adapt-packet-footer-btn-bg-image: url(./assets/btm-btn-bg.png);
    --adapt-packet-inner-title-color: #fff4ca;
    --adapt-packet-inner-amount-color: #fff4ca;
    --adapt-packet-inner-amount-unit-color: #fff4ca;
    --adapt-packet-inner-amount-desc-color: rgba(255, 244, 202, 0.7);
    --adapt-packet-inner-relation-desc-color: #fff4ca;
    --adapt-packet-inner-relation-avatar-icon-color: #fff4ca;
    --adapt-packet-inner-relation-text-color: #fff4ca;
    --adapt-packet-inner-relation-amount-color: #fff4ca;
    --adapt-packet-close-type-mid-btn-bg-image: url(./assets/off-red.png);
    --adapt-packet-inner-relation-icon-mask-color: rgba(255, 255, 255, 0.14);
    --adapt-packet-close-type-bg-image: url(./assets/close-packet-bg.png);
    --adapt-packet-inner-btm-desc-font-color: #fef6ca;

    .unlock-type {
        --adapt-packet-close-type-mid-btn-bg-image: url(./assets/on-red.png);
    }
}

.CLOSE-GOLD {
    --adapt-packet—inner-top-margin: 46px;
    --adapt-packet-footer-btn-font-color: #ffffff;
    --adapt-packet-footer-btn-bg-image: url(./assets/btm-btn-bg-red.png);
    --adapt-packet-inner-title-color: #b73700;
    --adapt-packet-inner-amount-color: #b73700;
    --adapt-packet-inner-amount-unit-color: #b73700;
    --adapt-packet-inner-amount-desc-color: rgba(183, 55, 0, 0.7);
    --adapt-packet-inner-relation-desc-color: #b73700;
    --adapt-packet-inner-relation-avatar-icon-color: #b73700;
    --adapt-packet-inner-relation-text-color: #b73700;
    --adapt-packet-inner-relation-amount-color: #b73700;
    --adapt-packet-inner-relation-icon-mask-color: rgba(255, 255, 255, 0.3);
    --adapt-packet-close-type-bg-image: url(./assets/close-packet-bg-gold.png);
    --adapt-packet-close-type-mid-btn-bg-image: url(./assets/off-gold.png);
    --adapt-packet-inner-btm-desc-font-color: #b73700;

    .unlock-type {
        --adapt-packet-close-type-mid-btn-bg-image: url(./assets/on-gold.png);
    }
}

.CLOSE-RED,
.CLOSE-GOLD {
    --adapt-packet-inner-relation-desc-font-size: 18px;
    --adapt-packet-inner-relation-desc-font-weight: bold;
    --adapt-packet-inner-relation-avatar-margin-top: 24px;
    --adapt-packet-inner-relation-avatar-border-color: #fff4ca;
    --adapt-packet-inner-amount-margin-top: 14px;
    --adapt-packet-close-type-mid-btn-width: 127px;
    --adapt-packet-close-type-mid-btn-height: 127px;
    --adapt-packet-close-type-mid-btn-radius: 50%;
    --adapt-packet-close-type-mid-btn-bottom: 43px;
    --adapt-packet-inner-amount-font-family: 'PingFang SC';

    --adapt-packet-relation-left-icon-bg: transparent;
    --adapt-packet-relation-right-icon-bg: transparent;
}

.OPEN-RED {
    --adapt-packet-footer-btn-font-color: #ff0b24;
    --adapt-packet-footer-btn-bg-image: url(./assets/btm-btn-bg.png);
    --adapt-packet-open-type-top-bg-image: url(./assets/close-back-top.png);
    --adapt-packet-open-type-btm-bg-image: url(./assets/close-back-btm.png);
    --adapt-packet-inner-btn-bg-image: url(./assets/big-bt-bg.png);
    --adapt-packet-inner-btn-font-color: #ff0b24;
    --adapt-packet-inner-left-btn-font-color: #ffdcb4;
    --adapt-packet-inner-right-btn-font-color: #ff0b24;
    --adapt-packet-inner-left-btn-bg-image: url(./assets/left-btn-s.png);
    --adapt-packet-inner-right-btn-bg-image: url(./assets/right-btn-s.png);
    --adapt-packet-inner-btm-desc-font-color: #fff4ca;
}

.OPEN-GOLD {
    --adapt-packet-footer-btn-font-color: #ff0b24;
    --adapt-packet-footer-btn-bg-image: url(./assets/btm-btn-bg.png);
    --adapt-packet-open-type-top-bg-image: url(./assets/close-back-top-gold.png);
    --adapt-packet-open-type-btm-bg-image: url(./assets/close-back-btm-gold.png);
    --adapt-packet-inner-btn-bg-image: url(./assets/big-bt-bg-red.png);
    --adapt-packet-inner-btn-font-color: #ffffff;
    --adapt-packet-inner-left-btn-font-color: #bd420d;
    --adapt-packet-inner-right-btn-font-color: #ffffff;
    --adapt-packet-inner-left-btn-bg-image: url(./assets/left-btn-s-red.png);
    --adapt-packet-inner-right-btn-bg-image: url(./assets/right-btn-s-red.png);
    --adapt-packet-inner-btm-desc-font-color: #b73700;
}

.OPEN-RED,
.OPEN-GOLD {
    --adapt-packet-inner-title-color: #ff0b24;
    --adapt-packet-inner-amount-color: #ff0b24;
    --adapt-packet-inner-amount-unit-color: #ff0b24;
    --adapt-packet-inner-amount-desc-color: #9c9c9c;
    --adapt-packet-inner-relation-desc-color: #9c9c9c;
    --adapt-packet-inner-relation-desc-font-size: 13px;
    --adapt-packet-inner-relation-desc-font-weight: normal;
    --adapt-packet-inner-relation-avatar-margin-top: 12px;
    --adapt-packet-inner-relation-avatar-border-color: #ffebed;
    --adapt-packet-inner-relation-avatar-icon-color: #ff1d50;
    --adapt-packet-inner-relation-text-color: #9c9c9c;
    --adapt-packet-inner-relation-amount-color: #ff0b24;
    --adapt-packet-inner-relation-icon-mask-color: #ffebed;
    --adapt-packet-inner-amount-font-family: 'PingFang SC';

    --adapt-packet-relation-left-icon-bg: linear-gradient(90deg, #ffffff 0%, #c9c9c9 100%);
    --adapt-packet-relation-right-icon-bg: linear-gradient(270deg, #ffffff 0%, #c9c9c9 100%);
}

.SMALL {
    --adapt-packet—inner-top-margin: 46px;
    --adapt-packet-inner-amount-margin-top: 18px;
    --adapt-packet-inner-amount-margin-bottom: 0px;
}

.MIDDLE {
    --adapt-packet—inner-top-margin: 46px;
    --adapt-packet-inner-amount-margin-top: 2px;
    --adapt-packet-inner-amount-margin-bottom: 2px;
}

.LARGE {
    --adapt-packet—inner-top-margin: 30px;
    --adapt-packet-inner-amount-margin-top: -4px;
    --adapt-packet-inner-amount-margin-bottom: -4px;
}

.ACTIVITY {
    --adapt-packet—inner-top-margin: 60px;
    --adapt-packet-footer-btn-font-color: #ff0b24;
    --adapt-packet-footer-btn-bg-image: url(./assets/btm-btn-bg.png);
    --adapt-packet-inner-title-color: #fffee8;
    --adapt-packet-inner-amount-color: linear-gradient(0deg, #fff 33.3%, #fff85a 118.66%, #fffeed 156.09%, #fff 210.9%);
    --adapt-packet-inner-amount-unit-color: #fff;
    --adapt-packet-inner-amount-margin-top: 14px;
    --adapt-packet-inner-amount-desc-color: #fff;
    --adapt-packet-inner-relation-desc-color: #ffffff;
    --adapt-packet-inner-relation-desc-font-size: 13px;
    --adapt-packet-inner-relation-desc-font-weight: normal;
    --adapt-packet-inner-relation-avatar-margin-top: 12px;
    --adapt-packet-inner-relation-avatar-border-color: #ffebed;
    --adapt-packet-inner-relation-avatar-icon-color: #ff1d50;
    --adapt-packet-inner-relation-text-color: #9c9c9c;
    --adapt-packet-inner-relation-amount-color: #ff0b24;
    --adapt-packet-inner-relation-icon-mask-color: #ffebed;
    --adapt-packet-close-type-bg-image: url(./assets/activity-bg.png);
    --adapt-packet-inner-btm-desc-font-color: #b73700;
    --adapt-packet-inner-btn-width: 192px;
    --adapt-packet-inner-btn-height: 66px;
    --adapt-packet-inner-btn-bg-image: url(./assets/activity-btn.png);
    --adapt-packet-inner-btn-font-color: #ff214e;
    --adapt-packet-inner-btn-font-family: kuaiyuanhui;
    --adapt-packet-inner-btn-font-size: 20px;
    --adapt-packet-inner-amount-font-family: puhui105;

    --adapt-packet-relation-left-icon-bg: linear-gradient(270deg, #ffffff 0%, #c9c9c9 100%);
    --adapt-packet-relation-right-icon-bg: linear-gradient(90deg, #ffffff 0%, #c9c9c9 100%);
}

.popup {
    position: fixed;
    z-index: 9999;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    font-size: 16px;
    text-align: center;

    --adapt-avatar-stutas-mask-opacity: 1;
    --adapt-avatar-more-status-color: #fff4ca;

    .packet-header {
        margin-bottom: var(--adapt-packet-header-title-margin-bottom);
        pointer-events: none;
    }

    .background-lottie {
        position: absolute;
        width: 414px;
        height: 896px;
        overflow: hidden;

        .lottie {
            position: absolute;
            top: 0;
        }
    }

    .popup-main {
        width: 100%;
        z-index: 1;
        position: relative;

        .header {
            position: absolute;
            transform: translateY(-100%);
            top: 0;
            width: 100%;
            text-align: center;
            --adapt-heading-title-font-family: var(--adapt-packet-header-title-font-family);
            --adapt-heading-title-font-style: skewX(-7deg);
            .close-btn {
                position: absolute;
                top: -14px;
                right: 14px;
                animation: show-close-btn 0.83s 0s linear;
            }
        }

        .content {
            width: 100%;
            position: relative;
            text-align: center;

            .normal-packet {
                margin: auto;
                width: 300px;
                height: 366px;
                background: url('./assets/close-packet-bg.png') center/100% no-repeat;
                background-image: var(--adapt-packet-close-type-bg-image);
                position: relative;

                .mid-btn {
                    position: absolute;
                    width: var(--adapt-packet-close-type-mid-btn-width);
                    height: var(--adapt-packet-close-type-mid-btn-height);
                    bottom: var(--adapt-packet-close-type-mid-btn-bottom);
                    left: 50%;
                    transform: translateX(-50%);
                    border-radius: var(--adapt-packet-close-type-mid-btn-radius);
                    background: center/100% no-repeat;
                    background-image: var(--adapt-packet-close-type-mid-btn-bg-image);
                }

                .btn-group {
                    position: absolute;
                    bottom: 30px;
                }
            }

            .adapt-packet {
                display: flex;
                flex-direction: column;
                width: 300px;
                margin: auto;

                .top-part {
                    position: relative;
                    display: flex;
                    flex: 1;
                    justify-content: center;
                    margin-bottom: -60px;
                    min-height: 248px;

                    .background-wrapper {
                        position: absolute;
                        display: flex;
                        flex-direction: column;
                        width: 300px;
                        height: 100%;
                        background: no-repeat;
                        background-position: 0 bottom;
                        background-size: 300px 216px;
                        background-image: var(--adapt-packet-open-type-top-bg-image);

                        .top {
                            height: 242px;
                            background: url(./assets/open-top.png) no-repeat center/100%;
                            background-position: -0.5px 0;
                            margin-bottom: -1px;
                        }

                        .middle {
                            flex: 1;
                            margin: 0 15px;
                            background-color: #fff;
                        }
                    }

                    .open-content {
                        padding-bottom: 40px;
                    }
                }

                .bottom-part {
                    box-sizing: border-box;
                    z-index: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-direction: column;
                    width: 100%;
                    height: 178px;
                    padding: 60px 0 18px;
                    background: no-repeat center/100%;
                    background-image: var(--adapt-packet-open-type-btm-bg-image);
                }
            }
        }

        .footer {
            position: absolute;
            transform: translateY(100%);
            bottom: 0;
            width: 100%;
            text-align: center;

            .btm-btn {
                margin: auto;
                margin-top: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 230px;
                height: 72px;
                background: center/100% no-repeat;
                background-image: var(--adapt-packet-footer-btn-bg-image);
                font-size: 20px;
                font-weight: bold;
                color: var(--adapt-packet-footer-btn-font-color);
            }

            .message-card {
                margin-top: 30px;
                --adapt-highlight-text-color: #fff4ca;
                --adapt-highlight-text-font-size: 17px;
                --adapt-highlight-text-font-bold: bold;
                --adapt-highlight-text-margin: 0 2px;
            }
        }
    }
}

// 红包区动效
.packet-anim-enter-from,
.packet-anim-leave-to {
    opacity: 0;
    transform: matrix3d(0.3, 0, 0, 0, 0, 0.3, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
}
.packet-anim-enter-active {
    animation: show-package 0.5333333333333333s 0s linear;
}
.packet-anim-leave-active {
    animation: show-package 0.5333333333333333s 0s linear reverse;
}

// flyout动效
.flyout-leave-to {
    opacity: 0;
}

.flyout-leave-active {
    animation: flyout 1s forwards;
}

// 主按钮动效
.button-enter-active {
    animation: btn-shake 0.7666666666666667s 1.3333333333333333s linear;
}
</style>

<style lang="scss">
.u-lock-screen {
    width: 100%;
    height: 100%;
    body {
        position: fixed;
        overflow: hidden;
        width: 414px;
    }
}
</style>
./types
