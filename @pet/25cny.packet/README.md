# @pet/adapt.bezier-path-fly

按路径飞行。这里预设了三组设计师给出的默认配置值：

```js
const flyTopBezierConfig = {
    bezier: [0, -147, 0, 2],
    timeBezier: [0.17, 0, 0.09, 1],
    fadeBezier: [0, 0, 1, 1],
    duration: 667,
};
const flyMiddleBezierConfig = {
    bezier: [-1, -146, 1, -223],
    timeBezier: [0.17, 0, 0.09, 1],
    fadeBezier: [0, 0, 1, 1],
    duration: 700,
};
const flyBtmBezierConfig = {
    bezier: [1.5, -93, 21, -340],
    timeBezier: [0.33, 0.0, 0.67, 1.0],
    fadeBezier: [1, 0, 0.89, 1],
    duration: 330,
};
```

如果有自定义需求可以参考 custom.vue 内的参数配置。
