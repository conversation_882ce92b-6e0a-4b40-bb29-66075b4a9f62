<pet-info lang="json">
{ "title": "滚动布局", "description": "中间预留出可用区域" }
</pet-info>
<script lang="ts" setup>
import { ref } from 'vue-demi';
import Button from '@pet/adapt.button/index.vue';
import Frame from '../index.vue';

const showFrame = ref(false);
</script>
<template>
    <div>
        <div v-if="showFrame" class="fake-view">
            <Frame :full-screen="false" :has-status-bar="false">
                <div class="main-content">中间高度自适应</div>
            </Frame>
        </div>
        <Button :class="showFrame && 'button'" @click="showFrame = !showFrame">
            {{ !showFrame ? '显示布局' : '隐藏布局' }}
        </Button>
    </div>
</template>

<style scoped>
.fake-view {
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: #fff;
    overflow: auto;
}

.main-content {
    height: 200vh;
    background: #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
}

.button {
    position: relative;
    z-index: 1;
}
</style>
