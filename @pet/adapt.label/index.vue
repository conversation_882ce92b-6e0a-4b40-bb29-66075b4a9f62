<script lang="ts" setup>
import Arrow from './components/arrow.vue';

interface LabelProps {
    /**
     * 文案
     */
    text?: string;
    /**
     * 显示箭头
     */
    showArrow?: boolean;
}

const props = withDefaults(defineProps<LabelProps>(), {
    text: '去查看',
    showArrow: true,
});

const emit = defineEmits<{
    (e: 'click', params: Event): void;
}>();

const handleClick = (e: Event) => {
    emit('click', e);
};
</script>

<template>
    <!-- TODO：点击热区待定 -->
    <div class="label" @click="handleClick">
        <div class="label-text">{{ text }}</div>
        <Arrow v-if="showArrow" class="label-arrow" />
    </div>
</template>

<style>
:root {
    /* 文案颜色 */
    --adapt-label-text-color: #fffbec;
    /* 文案字体大小 */
    --adapt-label-text-font-size: 13px;
    /* 文案字重 */
    --adapt-label-text-font-weight: 500;
    /* 箭头宽度 */
    --adapt-label-arrow-width: 6px;
    /* 箭头高度 */
    --adapt-label-arrow-height: 9px;
    /* 箭头与文案间距 */
    --adapt-label-arrow-gap: 2px;
}
</style>

<style lang="scss" scoped>
.label {
    display: inline-flex;
    align-items: center;
    color: var(--adapt-label-text-color);
    &-text {
        font-size: var(--adapt-label-text-font-size);
        font-weight: var(--adapt-label-text-font-weight);
    }
    &-arrow {
        margin-left: var(--adapt-label-arrow-gap);
    }
}
</style>
