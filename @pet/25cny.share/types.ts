import type { OptionShareEvent } from '@ks-share/share-utils';
/**
 * 字体设置
 */
export type FontSetting = {
    /*
     * 字体大小，默认14
     */
    size: number;
    /*
     * 颜色，默认 #000
     */
    color: string;
    /*
     * 加粗，斜体, ''(默认) 'italic' 'bold' 'italic bold'
     */
    style: string;
    opacity: string;
    fontFamily: string;
};

export type FontSettingParam = Partial<FontSetting>;

export type Direction = 'left' | 'right' | 'center';

export enum DrawDirection {
    left = 'left',
    right = 'right',
    up = 'up',
    down = 'down',
    center = 'center',
}

export type Formatter = (filledContent: Record<string, string | number>) => string;

export interface BaseDrawParams {
    type: string;
    startX: number;
    startY: number;
    depFields?: Record<string, string>;
    isAvatar?: boolean;
}

export interface ImageDrawParams extends BaseDrawParams {
    type: 'image';
    url?: string;
    formatter?: Formatter;
    width?: number;
    height?: number;
    circular?: boolean;
    radius?: number;
    borderWidth?: number;
    borderColor?: string;
}

export interface TextDrawParams extends BaseDrawParams {
    type: 'text';
    content?: string;
    formatter?: Formatter;
    endX?: number;
    direction?: Direction;
    font: FontSetting;
    width?: number;
    wrap?: boolean;
    lineHeight?: number;
}

export interface RectDrawParams extends BaseDrawParams {
    type: 'rectangle';
    width: number;
    height: number;
    color: string;
    radius?: number;
    opacity?: number;
}

export interface ImgWithTextDrawParams extends BaseDrawParams {
    type: 'imgWithText';
    url?: string;
    startX: number;
    startY: number;
    content?: string;
    imgWidth: number;
    imgHeight: number;
    circular?: boolean;
    padding?: number;
    radius?: number;
    borderWidth?: number;
    borderColor?: string;
    font: FontSetting;
    textOffsetY?: number;
}

export interface FlexContainerDrawParams extends BaseDrawParams {
    type: 'flex';
    padding: number;
    children: Array<
        (
            | Omit<CardWithCountDrawParams, 'startY' | 'startX'>
            | Omit<ImageDrawParams, 'startY' | 'startX'>
            | Omit<TextDrawParams, 'startY' | 'startX'>
        ) & {
            resolvedWidth?: number;
        }
    >;
}

export interface FilledFlexDrawParams extends BaseDrawParams {
    type: 'flex';
    padding: number;
    children: Array<ImageDrawParams | TextDrawParams | Omit<CardWithCountDrawParams, 'startX' | 'startY'>>;
}

export interface QrCodeDrawParams extends BaseDrawParams {
    type: 'qrCode';
    url?: string;
    width: number;
    height: number;
}

export interface CardWithCountDrawParams extends BaseDrawParams {
    type: 'cardWithCount';
    /** 卡图片 */
    url: string;
    /** 卡宽 */
    width: number;
    /** 卡高 */
    height: number;
    /** 卡阴影图片 */
    bgUrl?: string;
    /** 卡阴影宽 */
    bgWidth?: number;
    /** 卡阴影高 */
    bgHeight?: number;
    /** 卡阴影 相对图片的位置X */
    bgPositionX?: number;
    /** 卡阴影 相对图片的位置Y */
    bgPositionY?: number;

    /** 圆角 */
    radius?: number;
    /** 边框 */
    borderWidth?: number;
    /** 边框颜色 */
    borderColor?: string;
    /** 碎片名称 */
    patchName?: string;
    /** 卡数量 */
    count: number;
    fontSetting?: FontSetting;
    /** 碎片文字 */
    patchNameFontSetting?: FontSetting;
    /** 碎片文字 */
    patchNameMaxLength?: number;
    countBcg: string;
    countBcgX: number;
    countBcgY: number;
    countBcgWidth: number;
    countBcgHeight: number;
}

export type DrawParams =
    | ImageDrawParams
    | TextDrawParams
    | RectDrawParams
    | ImgWithTextDrawParams
    | QrCodeDrawParams
    | FlexContainerDrawParams
    | FilledFlexDrawParams
    | CardWithCountDrawParams;

export interface QrPosition {
    qrImageAspectRatio: number;
    qrImageRelativeX: number;
    qrImageRelativeY: number;
    qrImageRelativeWidth: number;
}

export interface StaticShareConfig {
    qrPosition?: QrPosition;
    /** 海报的显示类型，海报图片超过手机显示高度时对图片显示的处理，值为0则支持图片滚动，值为1则对图片进行缩放整体显示 */
    posterShowType?: 0 | 1;
    posterMetaData?: {
        /** TODO：默认 */
        width: number;
        height: number;
    };
    posterDrawProcess: Array<DrawParams>;
}

export interface CustomShareParams extends ShareParams {
    subBiz: string;
    tokenStoreParams: Record<string, any>;
    commonConfigs: Record<string, any>;
}

export interface ExtraChannelOptions {
    /** 分享渠道的ID，qq，qzone，wechat，wechatMoments，weibo，copyLink，wechatWow, im */
    shareChannel: string;
    /** 分享方式，卡片、口令等，如果已经接入对抗策略，则不可指定 */
    shareMethod: string;
    /** 分享模式，调用三方分享的底层方式，如果已经接入对抗策略，则不可指定 */
    shareMode: string;
}

export interface Params {
    /** 旧海报协议，本次使用 */
    posterConfigs?: {
        /** 通过base64的方式定义海报图片 */
        posterImageBytes?: string;
        /** posterImageUrl */
        posterImageUrl?: string;
        /** posterImageAspectRatio */
        posterImageAspectRatio: number;
        /** 海报图上的二维码的宽/高比 */
        qrImageAspectRatio: number;
        /** 待填充二维码区域左上角的相对X坐标，范围[0,1]，这个值是相对于海报图的 */
        qrImageRelativeX: number;
        /** 二维码 Y 坐标 */
        qrImageRelativeY: number;
        /** 待填充二维码区域的相对宽度，范围[0,1]，相对于图片的值 */
        qrImageRelativeWidth: number;
        /** 海报的显示类型，海报图片超过手机显示高度时对图片显示的处理 0 支持图片滑动 1 不支持滑动，图片整体缩放 */
        posterShowType?: 0 | 1;
    };
    /** 业务标识 */
    subBiz: string;
    /** 显示面板调用（share/init) 透传参数 */
    shareInitConfigs?: {
        /** 分享链接上的透传参数 */
        extTransientParams?: Record<string, any>;
        /** 回流链接上的透传参数 */
        extTokenStoreParams?: Record<string, any>;
        /** 新海报的显示参数，本次未启用 */
        // extInitPosterParams?: PosterDisplayParams;
        /** 新海报的绘制参数，本次未启用 */
        // extInitPainterParams?: PosterPainterParams;
    };
    /** JSON字符串，该字段传的内容，客户端将直接透传到打点平台 */
    logParams?: string;
    /** 用于埋点上报，区分分享内容 */
    shareContent?: string;
    /** 分享业务中一个分享的唯一标识，因为是唯一值，可以取user_id,或者shop_id,photo_id这种值 */
    shareObjectId?: string;
    /** 是否直接跳转某个渠道，和showSharePannel互相影响 */
    shareDirectActionUrl?: string;
    /**
     * 分享链接上的透传参数，这里透传的参数在回流链接上无效，如果不需要严格细分，推荐使用tokenStoreParams
     * share/any 分享内容接口透传
     * */
    commonConfigs?: Record<string, any>;
    /**
     * 回流链接上的透传参数，作用效果周期更长，推荐默认使用
     * share/any 分享内容接口透传
     * */
    tokenStoreParams?: Record<string, any>;
    /** 是否显示分享面板 */
    showSharePanel?: boolean;
    /** 不同分享渠道的特殊配置 */
    extraChannelOptions?: ExtraChannelOptions[];
    /** 强制要覆盖服务端下发的字段值，该参数仅支持4个字段值，目前只有除夕摇一摇会场使用到 */
    forceCommonConfigs?: {
        bigPicBytes?: string[];
    };
}

// export interface ShareParams {
//     /** 默认传递 Cookie.get('ud') */
//     shareObjectId?: string;
//     /** 是否展示分享面板，默认 true */
//     showSharePanel?: boolean;
//     /** 用于埋点上报，区分分享内容，值取决于管理端配置的分享内容 */
//     shareContent?: string;
//     /** 搭配 showPanel false 使用 */
//     shareDirectActionUrl?: string;
//     /** 不同分享渠道的特殊配置 */
//     extraChannelOptions?: ExtraChannelOptions[];
//     /** JSON字符串，该字段传的内容，客户端将直接透传到打点平台 */
//     logParams?: string;
// }

export interface NativeShareDialogEvent {
    /** 触发时机 */
    eventId: /** 前端调起分享面板展示 */
    | 'panel_show'
        /** 分享面板点击取消按钮 */
        | 'panel_cancel'
        /** 分享面板消失 */
        | 'panel_dismiss'
        /** 用户点击某个渠道 */
        | 'user_select'
        /** 面板的 banner 展示事件 */
        | 'banner_show'
        /** banner 的点击事件 */
        | 'banner_clicked'
        /** 面板的 header 展示事件 */
        | 'header_show'
        /** header 的点击事件 */
        | 'header_clicked';
    kpn: string;
}

export type shareSubBiz = string;

export interface PosterPainterParams {
    /** 海报图模板类型 */
    type: 'long_pic' | 'short_pic' | 'big_pic' | 'big_qr';
    /** 背景图/主图的链接地址 */
    imageUrl?: string;
    /** 背景图/主图的 base64 字符，如果传递，客户端会优先取这个值 */
    imageBytes?: string;
    /** 昵称或图片主题，在type=big_qr｜big_pic 中使用 */
    title?: string;
    /** 副标题，一般是描述性文案，在type=big_qr｜big_pic 中使用 */
    subTitle?: string;
    /** 在type=big_qr模板中用于定义用户昵称 */
    icon?: string;
    /** 二维码相对海报横轴偏移量，0～1值，在二维码位置固定的模板类型中不起作用，默认值0 */
    qrImageRelativeX?: number;
    /** 二维码相对海报纵轴偏移量，0～1值，在二维码位置固定的模板类型中不起作用，默认值0 */
    qrImageRelativeY?: number;
    /** 二维码容错率，离散可取值【0，1，2，3】，3容错率最高，默认值3 */
    qrCorrectionLevel?: number;
    /** 二维码相对海报图的宽度比值，0～1 */
    qrImageRelativeWidth?: number;
}

export interface PosterDisplayParams {
    /** 海报宽度/屏幕宽度，必填，要求 小于等于1的值；图片显示时会按原图的等比例缩放 */
    relativeWidth: number;
    /** 海报显示区域距离屏幕顶部的距离，可不填，若没有定义位置信息，图片自动垂直居中显示 */
    absoluteTopMargin?: number;
    /** 海报显示区域距离分享面板的顶部的距离，可不填，若没有定义位置信息，图片自动垂直居中显示 */
    absoluteBottomMargin?: number;
}

export interface CommonShareParams {
    /** subBiz */
    shareSubbiz: shareSubBiz;
    /** 常用参数 */
    shareParams?: ShareParams;
    /** 完整周期透传占位符参数 */
    tokenStoreParams?: Record<string, any>;
    /** 分享链接上的透传参数，这里透传的参数在回流链接上无效，如果分不清参数在何时使用，推荐使用tokenStoreParams */
    commonConfigs?: Record<string, any>;
    /** 绘制海报的静态配置 */
    staticShareConfig?: StaticShareConfig;
    /** 绘制海报时携带的参数 */
    posterParams?: Record<string, string | number>;
    /** 是否只是获取分享参数 */
    getParam?: boolean;
    /** 绘制二维码使用的链接 */
    qrLink?: string;
}
export interface ShareParams {
    /** 分享对应业务，一个分享流程对应的唯一标识，分享后台配置 */
    subBiz: string;
    /** 标识一个分享内容唯一值，可以取user_id、shop_id 或者 photo_id 等 */
    shareObjectId?: string;
    /** 分享占位符 */
    placeholder: Record<string, unknown>;
    /** 绘制海报所需配置，详情见 https://docs.corp.kuaishou.com/k/home/<USER>/fcACYHdKt8Ic6m7RqY0a2_FYl */
    posterConfig: any;
    /** 二维码相对海报横轴偏移量 取值 0-1 */
    qrImageRelativeX: number;
    /** 二维码相对海报纵轴偏移量，取值 0-1 */
    qrImageRelativeY: number;
    /** 二维码相对海报图的宽度比值 取值 0-1 */
    qrImageRelativeWidth: number;
    /** 分享上报数据 */
    logExt?: Record<string, unknown>;
}
export type PanelCancelParamsType = {
    e: string;
    kpn: string;
};
export type PlaceholderParams = {
    [key: string]: any;
};
export type PaintConfigParams = {
    backgroundImg: string;
    avatar: string;
    username: string;
    message: string;
    title: string;
    subTitle: string;
    info: string;
    subMessage: string;
    type?: string;
};
export interface GenSendCardParams {
    levelOneCards?: {
        cardImg: string;
        cardCount: number;
        countBcg?: string;
    }[];
    levelTwoCards?: {
        cardImg: string;
        cardCount: number;
        countBcg?: string;
    }[];
    patchs?: {
        patchImg: string;
        patchCount: number;
        countBcg?: string;
        patchName?: string;
    }[];
}
export type commonShareType = {
    subBiz: string;
    type: 'immersive' | 'normal';
    staticShareConfig?: GenSendCardParams;
    placeholder?: PlaceholderParams;
    callback?: OptionShareEvent;
    downgrade?: boolean;
    paintConfig?: PaintConfigParams;
    logExt?: PlaceholderParams;
    absoluteBottomMargin?: number;
    absoluteTopMargin?: number;
};

export type channelShareType = {
    subBiz: string;
    type?: string;
    placeholder?: PlaceholderParams;
    channel: 'COPY_LINK' | 'QQ' | 'WECHAT';
    callback?: OptionShareEvent;
    staticShareConfig?: GenSendCardParams;
    paintConfig?: PaintConfigParams;
    logExt?: PlaceholderParams;
};
export interface GetListParams {
    type: string;
    placeholder?: PlaceholderParams;
}

export enum PosterElementType {
    RadiusImage = 'radius-image',
    Image = 'image',
    Text = 'text',
}
