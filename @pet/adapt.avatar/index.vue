<script lang="ts">
export default {
    name: 'Adaptvatar',
};
</script>
<script lang="ts" setup>
import { vImage } from '@pet/adapt.directives/image-load';
import { debounce } from '@pet/yau.core/lodash';
import { px2rem } from '@pet/core.mobile';
import { computed } from 'vue-demi';
import { isBrowser } from '@pet/yau.core/consts';
import darkImage from './assets/default-dark.png';
import lightImage from './assets/default-light.png';
import IconMore from './assets/icon-more.vue';
import IconInvite from './assets/icon-invite.vue';
interface Props {
    /**
     * 头像地址
     */
    src?: string;
    /**
     * 头像宽高
     */
    width?: number;
    /**
     * 兜底样式类型 “light” ｜ “dark” | "none"
     */
    defaultType?: 'none' | 'dark' | 'light';
    /**
     * 其他样式类型 "none" ｜"mask" ｜ “invite” | "more"
     */
    status?: 'none' | 'mask' | 'invite' | 'more';
    /**
     * 用户uid，输入可跳转到对应profile页面
     */
    authorId?: string | number;
    /**
     * 兜底头像
     */
    fallbackHead?: string;
    /**
     * 头像加载失败重试次数
     */
    retryTimes?: number;
    /**
     * 头像加载方式
     */
    loading?: 'lazy' | 'eager';
}

const props = withDefaults(defineProps<Props>(), {
    src: '',
    width: 38,
    defaultType: 'none',
    status: 'none',
    authorId: '',
    loading: 'eager',
});

const emit = defineEmits<{
    (e: 'click'): void;
    (e: 'error'): void;
    (e: 'load'): void;
}>();

function clickAction() {
    emit('click');
    if (isBrowser && props.authorId !== undefined && props.authorId !== '') {
        window.location.href = `kwai://profile/${props.authorId}`;
    }
}

const clickHandler = debounce(clickAction);

const defaultAvatarSrc = computed(() => props.fallbackHead ?? (props.defaultType === 'light' ? lightImage : darkImage));

const boxStyle = computed(() => {
    return {
        width: px2rem(props.width ?? 38),
        height: px2rem(props.width ?? 38),
    };
});

function onError() {
    emit('error');
}

function onLoad() {
    emit('load');
}
</script>

<template>
    <div class="wrapper" :style="boxStyle">
        <span class="avatar" @click="clickHandler">
            <!-- 头像 -->
            <span v-if="src"
                ><img
                    v-image="{
                        src: src,
                        fallbackSrc: defaultAvatarSrc,
                        retryTimes: retryTimes,
                        onLoad,
                        onError,
                        loading,
                    }"
            /></span>
            <span v-else-if="['light', 'dark'].includes(defaultType)"
                ><img
                    v-image="{
                        src: src,
                        fallbackSrc: defaultAvatarSrc,
                        retryTimes: 3,
                        onLoad,
                        onError,
                        loading,
                    }"
            /></span>
            <!-- 蒙层 -->
            <span v-if="['mask', 'invite', 'more'].includes(status)" class="mask"></span>
            <!-- 邀请加号 icon -->
            <span v-if="status === 'invite'"><IconInvite /></span>
            <!-- 更多三个点 icon -->
            <span v-if="status === 'more'"><IconMore /></span>
            <span>
                <!-- 自定义 slot -->
                <slot />
            </span>
        </span>
    </div>
</template>
<style>
:root {
    /* 头像边框大小 */
    --adapt-avatar-border-width: 0;
    /* 头像边框颜色 */
    --adapt-avatar-border-color: transparent;
    /* 头像蒙层颜色 */
    --adapt-avatar-mask-color: rgba(0, 0, 0, 0.6);
    /* 头像蒙层 icon 颜色 */
    --adapt-avatar-icon-color: #fff4ca;
}
</style>
<style lang="scss" scoped>
.wrapper {
    background-color: var(--adapt-avatar-border-color);
    padding: var(--adapt-avatar-border-width);
    border-radius: 50%;
    box-sizing: border-box;

    .avatar {
        position: relative;
        display: inline-block;
        box-sizing: border-box;
        border-radius: 50%;
        width: 100%;
        height: 100%;
        overflow: hidden;
        vertical-align: top;

        > span {
            display: inline-block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                vertical-align: top;
                text-indent: -200%;
                &:not([src]) {
                    visibility: hidden;
                }
            }
        }

        .mask {
            background: var(--adapt-avatar-mask-color);
        }
    }
}
</style>
