<pet-info lang="json">
{
    "title": "大小&兜底",
    "description": "默认的兜底头像,fallbackImage更换兜底图"
}
</pet-info>
<script setup lang="ts">
import Avatar from '../index.vue';
const fallbackImage =
    'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg';
const ava =
    'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png';
const sizeArr = [88, 72, 54, 52, 48, 38, 36, 32, 28, 24, 20];
</script>

<template>
    <section class="demo-frame">
        <div>
            <h5>默认兜底</h5>
            <div v-for="(item, index) in sizeArr" :key="index" class="wrapper">
                <span>{{ item }}: </span>
                <Avatar :src="ava" :width="sizeArr[index]" class="head" />
                <Avatar :width="sizeArr[index]" default-type="light" />
                <Avatar :width="sizeArr[index]" default-type="dark" />
            </div>
            <h5>替换兜底图</h5>
            <Avatar class="border" src="123" :fallback-head="fallbackImage" />
            <h5>其他形态</h5>
            <div class="wrapper">
                <Avatar status="mask" />
                <Avatar :src="ava" status="mask" />
                <Avatar :src="ava" status="invite" />
                <Avatar :src="ava" status="more" />
            </div>
            <div class="wrapper style2">
                <Avatar status="mask" />
                <Avatar :src="ava" status="mask" />
                <Avatar :src="ava" status="invite" />
                <Avatar :src="ava" status="more" />
            </div>
        </div>
    </section>
</template>

<style lang="scss" scoped>
.demo-frame {
    display: flex;
    font-size: 15px;
    h5 {
        font-size: 15px;
    }

    .wrapper {
        width: 414px;
        display: flex;
        justify-content: space-around;
        align-items: center;

        .head {
            /* 边框变量 */
            --adapt-avatar-border-width: 1px;
            --adapt-avatar-border-color: #ffde9f;
        }
    }

    .style2 {
        --adapt-avatar-mask-color: rgba(255, 192, 203, 0.6);
        --adapt-avatar-icon-color: #000;
    }
}
</style>
