<script lang="ts" setup>
import { computed, watch } from 'vue-demi';
import { debounce as effectDebounce } from '@pet/yau.core/lodash';
import Loading from '@pet/adapt.loading/circle-loading.vue';
import type { ButtonHeight, ButtonSize, ButtonType, AniType } from './types';
interface ButtonProps {
    /**
     * 按钮类型
     */
    type?: ButtonType;
    /**
     * 按钮高度
     */
    height?: ButtonHeight;
    /**
     * 禁用状态
     */
    disabled?: boolean;
    /**
     * 禁止态，但可以点击
     */
    looksLikeDisabled?: boolean;
    /**
     * 加载态
     */
    loading?: boolean;
    /**
     * 防抖时间
     */
    debounce?: number;
    /**
     * 动效类型
     */
    aniType?: AniType | AniType[];
    /**
     * 是否有兜底背景色，用于点击以及disable的透明度变化
     */
    hasSubstrate?: boolean;
}

const props = withDefaults(defineProps<ButtonProps>(), {
    type: 'primary',
    disabled: false,
    looksLikeDisabled: false,
    loading: false,
    height: 66,
    debounce: 200,
});
type SizeMap = Record<ButtonHeight, ButtonSize>;
// 按按钮高度对应按钮尺寸
const sizeMap: SizeMap = {
    22: 'xs',
    30: 'xms',
    38: 's',
    46: 'ms',
    60: 'm',
    66: 'lm',
    72: 'l',
};
const btnClasses = computed(() => {
    return ['btn-main-box', `btn-${props.type}`];
});

const btnStatus = computed(() => {
    const aniTypeClass = Array.isArray(props.aniType) ? props.aniType : [props.aniType];
    return [
        'btn-wrapper',
        `btn-size-${sizeMap[props.height] ?? 'lm'}`,
        (props.disabled || props.looksLikeDisabled) && 'btn-disabled',
        props.loading && 'btn-loading',
        props.hasSubstrate && 'has-substrate',
        ...aniTypeClass,
    ];
});
const emit = defineEmits<{
    (e: 'click', params: Event): void;
    (e: 'disabled-click', params: Event): void;
}>();
function clickHandle(e: Event) {
    if (props.loading) {
        e.preventDefault();
    } else if (props.disabled) {
        emit('disabled-click', e);
    } else {
        emit('click', e);
    }
}
let debounceClick = clickHandle;
watch(
    () => props.debounce,
    (val) => {
        debounceClick = effectDebounce(clickHandle, val);
    },
    {
        immediate: true,
    },
);
</script>
<script lang="ts">
export default {
    name: 'AdaptButton',
};
</script>

<template>
    <span role="button" class="btn-wrapper" :class="btnStatus" @click="debounceClick">
        <span :class="btnClasses">
            <!-- 扫光动效 -->
            <span v-if="btnStatus.includes('shine') && !disabled" class="btn-shine-box">
                <span class="btn-shine"></span>
            </span>
            <Loading v-if="loading" class="loading" :colorful="type === 'plain'" />
            <span v-else class="btn-words">
                <span v-if="$slots.icon" class="btn-icon">
                    <slot name="icon" />
                </span>
                <div class="btn-main-text">
                    <slot />
                </div>
                <span v-if="$slots.plugin" class="btn-words-plugin">
                    <slot name="plugin"></slot>
                </span>
            </span>
        </span>
    </span>
</template>
<style>
:root {
    /* 主类型按钮字色 */
    --adapt-button-primary-font-color: #fff;
    /* 渐变字体颜色 */
    --adapt-button-primary-font-linear: var(--adapt-button-primary-font-color);
    /* 主类型按钮字间距 */
    --adapt-button-primary-letter-spacing: 0;
    /* 主类型按钮背景色 */
    --adapt-button-primary-background-color: #fe3666;
    /* 线条类型按钮字色 */
    --adapt-button-plain-font-color: #fe3666;
    /* 线条类型按钮背景色 */
    --adapt-button-plain-background-color: transparent;
    /* 线条按钮boder宽度 */
    /* stylelint-disable-next-line plugin/no-unsupported-browser-features */ /* prettier-ignore */
    --adapt-button-plain-border-width: max(1px, 1PX);
    /* 线条按钮boder颜色 */
    --adapt-button-plain-border-color: #fe3666;
    /* 按钮点击动效时间 */
    --adapt-button-active-transition-time: 150ms;
    /* 按钮点击透明度 */
    --adapt-button-active-opacity: 0.5;
    /* 按钮禁用透明度 */
    --adapt-button-disabled-opacity: 0.5;
    /* 按钮点击缩放 */
    --adapt-button-active-transform-scale: 0.95;
    /* icon宽度 */
    --adapt-button-icon-width: 25px;
    /* icon高度 */
    --adapt-button-icon-height: 25px;
    /* icon距离右边文案边距 */
    --adapt-button-icon-margin-right: 5px;
    /* 按钮副文案字号 */
    --adapt-button-plugin-font-size: 10px;
    /* 按钮副文案行高 */
    --adapt-button-plugin-line-height: 14px;
    /* 按钮副文案透明度 */
    --adapt-button-plugin-opcity: 0.7;
    /* 按钮副文案距离顶部边距 */
    --adapt-button-plugin-margin-top: 2px;
    /* 按钮loading大小 */
    --adapt-button-loading-size: calc(54em / 32);
    /* 按钮主文案字重 */
    --adapt-button-font-weight: var(--adapt-global-font-weight-medium, normal);
    /* 按钮副文案plugin字重 */
    --adapt-button-plugin-font-weight: normal;
    /* 呼吸动画时长 */
    --adapt-button-breath-animation-duration: 1000ms;
    /* 呼吸动画函数 */
    --adapt-button-breath-animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
    /* 呼吸动画缩放大小 */
    --adapt-button-breath-scale: 0.96;
    /* 扫光动画时长 */
    --adapt-button-shine-animation-duration: 2668ms;
    /* 按钮最小宽度 */
    --adapt-button-width: unset;
    /* 按钮高度 */
    --adapt-button-height: unset;
    /* 按钮主文案字号 */
    --adapt-button-font-size: unset;
    /* 按钮主文案行高 */
    --adapt-button-line-height: unset;
    /* 按钮内边距空白 */
    --adapt-button-padding: unset;
    /* 按钮圆角 */
    --adapt-button-border-radius: unset;
    /* 衬底色 */
    --adapt-button-substrate-background: #fff;
    /* 按钮字体 */
    --adapt-button-main-font-family: unset;
}
</style>
<style lang="scss" scoped>
@use 'sass:map';
$btn-sets: (
    l: (
        w: 210px,
        h: 72px,
        font-size: 18px,
        line-height: 26px,
        padding: 28px,
        radius: 200px,
    ),
    lm: (
        w: 210px,
        h: 66px,
        font-size: 17px,
        line-height: 24px,
        padding: 28px,
        radius: 127px,
    ),
    m: (
        w: 120px,
        h: 60px,
        font-size: 16px,
        line-height: 22px,
        padding: 22px,
        radius: 200px,
    ),
    ms: (
        w: 102px,
        h: 46px,
        font-size: 14px,
        line-height: 20px,
        padding: 18px,
        radius: 64px,
    ),
    s: (
        w: 76px,
        h: 38px,
        font-size: 13px,
        line-height: 18px,
        padding: 14px,
        radius: 92px,
    ),
    xms: (
        w: 52px,
        h: 30px,
        font-size: 12px,
        line-height: 18px,
        padding: 14px,
        radius: 92px,
    ),
    xs: (
        w: 40px,
        h: 22px,
        font-size: 10px,
        line-height: 14px,
        padding: 10px,
        radius: 118px,
    ),
);

@each $size, $info in $btn-sets {
    .btn-size-#{$size} {
        --adapt-button-font-size: #{map.get($info, 'font-size')};
        --adapt-button-line-height: #{map.get($info, 'line-height')};
        --adapt-button-width: #{map.get($info, 'w')};
        --adapt-button-height: #{map.get($info, 'h')};
        --adapt-button-border-radius: #{map.get($info, 'radius')};
        --adapt-button-padding: 0 #{map.get($info, 'padding')};
        &::before {
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }
    }
}

.btn-wrapper {
    font-size: var(--adapt-button-font-size);
    line-height: var(--adapt-button-line-height);
    min-width: var(--adapt-button-width);
    height: var(--adapt-button-height);
    display: inline-flex;
    cursor: pointer;
    transition: transform var(--adapt-button-active-transition-time) ease-in-out;
    &:active:not(.btn-disabled):not(.btn-loading) {
        transform: scale(var(--adapt-button-active-transform-scale));
        .btn-main-box {
            opacity: var(--adapt-button-active-opacity);
        }
    }

    /* stylelint-disable-next-line no-descending-specificity */
    .btn-main-box {
        border-radius: var(--adapt-button-border-radius);
        padding: var(--adapt-button-padding);
    }
}

.has-substrate {
    border-radius: var(--adapt-button-border-radius);
    background: var(--adapt-button-substrate-background);
}

.btn-words {
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    flex-wrap: wrap;
}

/* stylelint-disable-next-line no-descending-specificity */
.btn-main-box {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    -webkit-user-select: none;
    box-sizing: border-box;
    font-weight: var(--adapt-button-font-weight);
    .loading {
        font-size: var(--adapt-button-loading-size);
    }
}

.btn-icon {
    width: var(--adapt-button-icon-width);
    height: var(--adapt-button-icon-height);
    margin-right: var(--adapt-button-icon-margin-right);
    :deep(img),
    :deep(svg) {
        width: 100%;
        height: 100%;
    }
}

.btn-words-plugin {
    display: block;
    width: 100%;
    margin-top: var(--adapt-button-plugin-margin-top);
    font-size: var(--adapt-button-plugin-font-size);
    font-weight: var(--adapt-button-plugin-font-weight);
    font-family: var(--adapt-button-plugin-font-family);
    line-height: var(--adapt-button-plugin-line-height);
    opacity: var(--adapt-button-plugin-opcity);
}
.btn-shine {
    display: block;
    width: 100%;
    height: 100%;
    // background-image: var(--adapt-button-shine-animation-background-image);
    background-image: url('./assets/shine.png');
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    animation: btn-sharp var(--adapt-button-shine-animation-duration) steps(40) infinite;
    pointer-events: none;
}
.btn-disabled {
    /* stylelint-disable-next-line no-descending-specificity */
    .btn-main-box {
        opacity: var(--adapt-button-disabled-opacity);
    }
    .btn-shine {
        display: none;
    }
    &.breath {
        animation: none;
    }
}
.btn-primary,
.btn-primary-linear {
    color: var(--adapt-button-primary-font-color);
    background-color: var(--adapt-button-primary-background-color);
    /* liner渐变背景色和背景图 */
    background-image: var(--adapt-button-primary-background-image);
    background-size: 100% 100%;
    .btn-main-text {
        font-family: var(--adapt-button-main-font-family);
        letter-spacing: var(--adapt-button-primary-letter-spacing);
    }
}

.btn-primary-linear {
    --adapt-button-font-weight: 400;
    .btn-main-text {
        background: var(--adapt-button-primary-font-linear);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.btn-plain {
    color: var(--adapt-button-plain-font-color);
    background: var(--adapt-button-plain-background-color);
    border: var(--adapt-button-plain-border-width) solid var(--adapt-button-plain-border-color);
}
.shine {
    /* stylelint-disable-next-line no-descending-specificity */
    .btn-main-box {
        position: relative;
    }
}
.btn-shine-box {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    overflow: hidden;
    transform: translate(0);
    pointer-events: none;
    -webkit-mask-image: -webkit-radial-gradient(#fff, #000);
}

.breath {
    // animation: btn-breathing var(--adapt-button-breath-animation-duration)
    //     var(--adapt-button-breath-animation-timing-function) infinite;
    animation: btn-breathing var(--adapt-button-breath-animation-duration) steps(20) infinite;
}

@keyframes btn-sharp {
    0% {
        transform: translate(-100%, 0);
    }
    50% {
        transform: translate(200%, 0);
    }
    100% {
        transform: translate(200%, 0);
    }
}
@keyframes btn-breathing {
    0%,
    100% {
        transform: scale(var(--adapt-button-breath-scale));
    }
    48% {
        transform: scale(1);
    }
}
</style>
