<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';

import { channelShare, commonShare } from './index';

const onShareShort = async () => {
    commonShare({
        subBiz: 'SF_RABBIT_WARMUP_MAIN',
        type: 'normal',
        callback: {
            onPanelCancel: (e) => {
                console.log('onPanelCancel', e);
            },
        },
    }).catch((error) => {
        console.log('error', JSON.stringify(error));
    });
};
const onChannelShare = async () => {
    channelShare({
        subBiz: 'SF_RABBIT_WARMUP_MAIN',
        channel: 'WECHAT',
    });
};

const onImmersiveShare = async () => {
    commonShare({
        subBiz: 'SF_RABBIT_WARMUP_MAIN_TASK',
        type: 'immersive',
        callback: {
            onPanelCancel: (e) => {
                console.log('onPanelCancel', e);
            },
        },
    }).catch((error) => {
        console.log('error', JSON.stringify(error));
    });
};
</script>

<template>
    <div class="container">
        <Button @click="onShareShort">通用分享</Button>
        <Button @click="onChannelShare">单渠道分享</Button>
        <Button @click="onImmersiveShare">沉浸式分享</Button>
    </div>
</template>

<style lang="scss" scoped></style>
