<pet-info lang="json">
{ "title": "基本使用", "description": "" }
</pet-info>
<script setup lang="ts">
import Label from '@pet/adapt.label/index.vue';

const handleClick = (e: Event) => {
    alert('点击了');
};
</script>

<template>
    <div class="demo">
        <Label @click="handleClick" /><br />
        <Label text="提示啊啊啊" /><br />
        <Label text="提示啊啊啊" :show-arrow="false" />
    </div>
</template>

<style lang="scss" scoped>
div {
    font-size: 16px;
    background-color: red;
}
</style>
