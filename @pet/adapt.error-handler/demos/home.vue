<script lang="ts" setup>
import { ref } from 'vue-demi';
import { RouterView } from 'vue-router';

import { errorHandler, ErrorPageName } from '../index';

import { install, router, RouterName } from '../router';
//@ts-expect-error
// eslint-disable-next-line no-underscore-dangle
const PetApp = globalThis.__pet_app;
install(PetApp);

const nowUrl = window.location.href;

const ErrorData = ref({
    MaskLogin: {
        result: 109,
        message: '',
        needLoginList: [RouterName.Home],
    },
    NormalLogin: {
        result: 109,
        message: '',
        needLoginList: [],
    },
    NormalError: {
        result: 10001,
        message: '活动还没开始，请稍后参与~',
        needLoginList: [],
    },
    NetworkError: {
        result: 'status not 200',
        message: '',
        needLoginList: [],
    },
});

const errInfo = ref(ErrorData.value.MaskLogin);

function callErrorPage() {
    errorHandler(
        {
            url: '/your/api/path',
        },
        {
            result: errInfo.value.result,
            message: errInfo.value.message,
        },
        router,
        ErrorPageName,
        {
            blacklist: ['/your/api/path'],
            needLoginList: errInfo.value.needLoginList,
        },
    );
}

function goBack() {
    window.location.replace(nowUrl);
}
</script>

<template>
    <div class="ctrl-button">
        <div class="chooses">
            <label><input v-model="errInfo" type="radio" name="error" :value="ErrorData.MaskLogin" />遮罩登录</label>
            <label><input v-model="errInfo" type="radio" name="error" :value="ErrorData.NormalLogin" />普通登录</label>
            <label><input v-model="errInfo" type="radio" name="error" :value="ErrorData.NormalError" />普通错误</label>
            <label><input v-model="errInfo" type="radio" name="error" :value="ErrorData.NetworkError" />网络错误</label>
        </div>
        <button @click="callErrorPage">进入兜底</button>
        <button @click="goBack">返回页面</button>
    </div>
    <RouterView />
</template>

<style lang="scss">
html,
body {
    padding: 0;
    margin: 0;
    --adapt-error-handler-background: #fff3e4;
    --adapt-error-handler-color: #500;
}
.ctrl-button {
    font-size: 14px;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    margin-top: 120px;
    text-align: center;
    .chooses {
        margin-bottom: 20px;
    }
    button {
        margin: 0 10px;
    }
}

.login-mask {
    &::after {
        height: 100%;
        display: flex;
        font-size: 16px;
        content: '⚠️这里有透明遮罩盖住页面, 刷新页面重置';
        color: #fe6b33;
        justify-content: center;
        align-items: center;
    }
}
</style>
