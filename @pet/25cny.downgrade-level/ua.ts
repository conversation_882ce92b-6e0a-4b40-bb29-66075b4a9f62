/**
 * @file 获取 UA 上的信息，参考：
 * https://docs.corp.kuaishou.com/k/home/<USER>/fcAAQGKnbOdPdGz-rDAUx6usu
 */

const ModeType = {
    islp: 'isLowPerformanceDevice',
    icfo: 'isCrashFrequentlyDevice',
    isdm: 'isDarkMode',
    islb: 'isLowBatteryMode',
    isld: 'isLowDiskSpaceMode',
    islm: 'isLowMemoryMode',
};

const CountType = {
    tbht: 'tabHeight',
    bht: 'bottomHeight',
    ct: 'crashTotalCount',
    titleht: 'titleHeight',
    statusht: 'statusHeight',
    ftsfht: 'fourTabTopHeight',
    fbsfht: 'fourTabBottomHeight',
};

// ISLP 是否低端机
enum Mode {
    NO = '0',
    YES = '1',
}

type AzPrefix = 'az1' | 'az2' | 'az3' | 'az4';

interface UAInfoType {
    applewebkit?: string;
    chrome?: string;
    mozilla?: string;
    safari?: string;
    kwai?: string;
    yoda?: string;
    build?: string;
    nettype?: string;
    version?: string;
    mobile?: string;
    ksnebula?: string;
    islp?: string;
    icfo?: string;
    isdm?: string;
    islb?: string;
    isld?: string;
    islm?: string;
    tbht?: string;
    bht?: string;
    ct?: string;
    titleht?: string;
    statusht?: string;
    ftsfht?: string;
    fbsfht?: string;
    AZPREFIX?: AzPrefix;
    [key: string]: string | undefined;
}

export type UAInfo<T = keyof typeof ModeType, Count = keyof typeof CountType> = {
    [K in keyof UAInfoType]: K extends T ? Mode : K extends Count ? number : string;
};

function generateUserAgentInfo(): UAInfo {
    const matchList = window.navigator.userAgent.match(/([a-zA-Z]+)\/(\S+)(:\s)?/g) ?? [];
    const info: UAInfo = {};
    for (const str of matchList) {
        const res = str.split('/');
        const key = res[0]?.toLowerCase();
        const value = res[1]?.split(',')[0];

        if (!key || !value) {
            continue;
        }

        if (Object.keys(ModeType).includes(key)) {
            info[key] = +value === 1 ? Mode.YES : Mode.NO;
        } else if (Object.keys(CountType).includes(key)) {
            info[key as keyof typeof CountType] = +value;
        } else {
            info[key] = value;
        }
    }
    return info;
}

const uaInfo = generateUserAgentInfo();

export function uaGetInfo(): UAInfo {
    return Object.freeze(uaInfo);
}
