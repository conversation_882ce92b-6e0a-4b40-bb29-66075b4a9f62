<script setup lang="ts">
import { nativeCloud } from '@pet/yau.cloud-native';

const open = () => {
    nativeCloud.show();
    setTimeout(() => {
        nativeCloud.hide();
    }, 1500);
};
</script>
<template>
    <div class="container">
        <div class="btn" @click="open">打开卷轴</div>
    </div>
</template>
<style lang="scss" scoped>
.container {
    width: 414px;
    height: 100vh;
    background-color: pink;
    display: flex;
    justify-content: center;
    align-items: center;

    .btn {
        width: 200px;
        height: 56px;
        border-radius: 28px;
        background-color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
    }
}
</style>
