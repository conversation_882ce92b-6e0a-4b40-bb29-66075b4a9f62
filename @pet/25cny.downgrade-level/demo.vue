<script lang="ts" setup>
import { useDowngradeLevel } from './index';
import EffectItem from '@pet/vision.effect-item/index.vue';
const effectData = import('./effect/config').then((mod) => mod.default);

const { switchEffectLevel, effectShowStatus } = useDowngradeLevel();
switchEffectLevel(0);
</script>

<template>
    <button @click="switchEffectLevel(0)">L0</button>
    <button @click="switchEffectLevel(1)">L1</button>
    <button @click="switchEffectLevel(2)">L2</button>
    <button @click="switchEffectLevel(3)">L3</button>
    <br />
    <div>
        <span>L1:</span>
        <EffectItem
            v-if="effectShowStatus.L1"
            class="effect-wrapper"
            :data="effectData"
            :loop="true"
            autoplay
        ></EffectItem>
        <div v-else>降级</div>
        <span>L2:</span>
        <EffectItem
            v-if="effectShowStatus.L2"
            class="effect-wrapper"
            :data="effectData"
            :loop="true"
            autoplay
        ></EffectItem>
        <div v-else>降级</div>
        <span>L3:</span>
        <EffectItem
            v-if="effectShowStatus.L3"
            class="effect-wrapper"
            :data="effectData"
            :loop="true"
            autoplay
        ></EffectItem>
        <div v-else>降级</div>
    </div>
</template>

<style lang="scss" scoped>
body {
    margin: 0;
}
span {
    font-size: 20px;
    display: inline;
}
div {
    font-size: 20px;
}
.effect-wrapper {
    position: relative;
    width: 100px;
    height: 100px;
    overflow: hidden;
}
</style>
