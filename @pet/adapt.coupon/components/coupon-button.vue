<script setup lang="ts">
import { computed } from 'vue-demi';
import Button from '@pet/adapt.button/index.vue';

import type { CouponSize } from '../types';
type ButtonType = 'flat' | 'plain' | 'line';

interface ButtonProps {
    type?: ButtonType;
    size?: CouponSize;
    btnText?: string;
    btnDisabled?: boolean;
    btnLoading?: boolean;
}
const props = withDefaults(defineProps<ButtonProps>(), {
    type: 'flat',
    size: 'small',
    btnText: '立即使用',
});

const emit = defineEmits<{
    (event: 'btn-click'): void;
}>();

const buttonType = computed(() => [`button-${props.type}`, `size-${props.size === 'mini' ? 'mini' : 'normal'}`]);
const ogButtonType = computed(() => (props.type === 'flat' ? 'primary' : 'plain'));
function handleClick() {
    emit('btn-click');
}
</script>

<template>
    <Button
        :type="ogButtonType"
        :height="38"
        :disabled="btnDisabled"
        :loading="btnLoading"
        :class="buttonType"
        @click="handleClick"
    >
        {{ btnText }}
    </Button>
</template>

<style lang="scss" scoped>
$button-color: #fe3666;
$line-bg: rgba($button-color, 0.1);
$line-border: rgba($button-color, 0.36);

.button-line {
    min-width: 0;
    color: $button-color;
    --adapt-button-plain-border-color: #{$line-border};
    --adapt-button-plain-background-color: #{$line-bg};
    --adapt-button-plain-border-width: 1px;
    --adapt-button-padding: 0;
    --adapt-button-font-weight: 400;
    &.size-normal {
        --adapt-button-font-size: 10px;
        --adapt-button-height: 20px;
        width: 56px;
    }
    &.size-mini {
        --adapt-button-font-size: 8px;
        --adapt-button-height: 16px;
        width: 44px;
    }
}
.button-flat {
    min-width: 0;
    width: 72px;
    --adapt-button-font-weight: 700;
    --adapt-button-font-size: 12px;
    --adapt-button-height: 28px;
    --adapt-background-color: #{$button-color};
}

.button-plain {
    min-width: 0;
    width: 72px;
    --adapt-button-font-weight: 700;
    --adapt-button-font-size: 12px;
    --adapt-button-height: 28px;
}
</style>
