import { createModel, useModel } from '@gundam/model';

import { useForwardStationModel } from './forwardStationModel';
import { useCityHomeModel } from './homeModel';

/**
 * ConfigModel
 * 在 Mock 和 kconf 中配置后，记得在这里TS类型加上
 */
export const configModel = createModel(() => {
    const { homeData } = useCityHomeModel();
    const { gCurrentStation } = useForwardStationModel();

    const kconfConfig = computed<KconfFEConfig>(() => homeData.value?.homeFEConstantsConfig);
    const kconfUEConfig = computed<KconfUEConfig>(() => homeData.value?.homeUEConstantsConfig);

    /** 主题key */
    const themeKey = computed(() => (gCurrentStation.value?.stationInfo.stationThemeKey as ThemeKey) ?? 'default');

    // 兜底数据
    const defaultKconfConfig = {
        finalBubble: ['今天的钱已到手🎉', '明天记得来哦！'],
        gridGiftPopupText: {
            title: '抽奖可获得奖品',
            mainButton: '我知道了',
        },
    };

    return {
        /** FE Config */
        kconfConfig,
        defaultKconfConfig,
        /** UE Config */
        kconfUEConfig,
        /** 主题key */
        // themeKey,
        /** 页面配置 */
        themeConfig: computed(
            () =>
                kconfConfig.value?.frameConfig?.themeMap?.[themeKey.value] ??
                kconfConfig.value?.frameConfig?.themeMap.default,
        ),
        /** inpush配置 */
        inpushConfig: computed(() => kconfConfig.value?.inpushConfig),
    };
});

export const useConfigModel = () => useModel(configModel);

export type IMainButtonConfig = Record<ForwardRushBtnStatus, ForwardRushBtnInfo>;
export type ThemeKey = 'default' | 'jinghua' | 'xile' | 'furao';

// TODO:
export interface KconfFEConfig {
    // 页面设置
    frameConfig: {
        // 键为地图的key
        themeMap: Record<
            ThemeKey,
            {
                // 主题色，状态栏颜色
                themeColor: string;
                // 头部背景遮罩主颜色，第一个颜色需要和主题色一致，不然会有断层
                headerBackMaskBackground: string;
                // 底部背景遮罩颜色
                footerBackMaskBackground: string;
                // 背景颜色
                backgroundColor: string;
            }
        >; // 主题色配置
    };
    /** 新手引导标题 */
    guideTitle: string;
    /** 主按钮的文案配置 */
    mainButtonConfig: IMainButtonConfig;
    inpushConfig?: {
        // 兜底轮询间隔
        interval: number;
    };
    // 完成绕圈气泡文案
    finalBubble: string[];
    // 格子大奖弹窗
    gridGiftPopupText: {
        title: string;
        mainButton: string;
    };
}

export interface KconfUEConfig extends Record<string, string> {
    /** 会场主链接 */
    activityUrl: string;

    /** 活动规则页链接 */
    activityRuleUrl: string;
    /** 钱包页链接 */
    walletUrl: string;
    /** 客服页面链接 */
    customerServiceUrl: string;
    /** 格子大奖弹窗 */
    gridGiftPopupLogo: string;
    gridGiftPopupIcon: string;
}

/**
 * 主按钮的状态
 * 按照优先级排列：断签>已打卡>免任务卡>标准态
 */
export enum ForwardRushBtnStatus {
    PROCESSING = 'PROCESSING', // 进行中
    SIGNED = 'SIGNED', // 已完成
}

export interface ForwardRushBtnInfo {
    buttonText: string; // 主按钮文案
    newButtonText: string; // 新主按钮文案--0611的变更，已完成打卡的情况下需要展示保持“向前冲”
    buttonSubText?: string; // 主按钮副文案
    bubbleText?: string; // 气泡第一行文案
    bubbleSubText?: string; // 气泡第二行文案
    duration?: number; // 气泡展示时长
}
