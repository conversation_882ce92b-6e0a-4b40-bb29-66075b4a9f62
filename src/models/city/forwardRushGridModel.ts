import { createUseModel } from '@gundam/model';
import { screenDetect } from '@pet/core.mobile/screenDetectForCity';
import { nativeCloud } from '@pet/yau.cloud-native';
import { isAtFourTab, isAtSearchTab, isInIOS } from '@pet/yau.core';
import useCaptureDebugLog from '@pet/yau.logger';
import { watchDebounced } from '@vueuse/core';

import type { StationTagView } from '@/components/charge-forward/types';
import {
    type MapConfig,
    type RenderMapType,
    type StationConfig,
} from '@/components/city/charge-forward/manager/config';
import { MapConfigData, MapKeyType } from '@/components/city/charge-forward/manager/constant';
import type { ChargeForwardManager } from '@/components/city/charge-forward/manager/manager';
import type { ChessProgressView, UserBasicView } from '@/services/open-api-docs/home/<USER>/schemas';

import { useForwardStationModel } from './forwardStationModel';
import { useCityHomeModel } from './homeModel';
import { useSnapShotModel } from './snapShotModel';

export const useForwardRushGridModel = createUseModel(() => {
    const { log } = useCaptureDebugLog();
    const { homeData } = useCityHomeModel();
    const { currentStep } = useSnapShotModel();
    const { gCurrentStation } = useForwardStationModel();
    const isFourTabMode = isAtFourTab();
    const isSearchTabMode = isAtSearchTab();
    const isSmallScreen = screenDetect().isSmallScreenDevice;
    // 小屏 有tab 的情况下需要自动滚动
    // screenDetect().isSmallScreenDevice && (isFourTabMode || isSearchTabMode)
    const autoScroll = computed(() => screenDetect().isSmallScreenDevice && (isFourTabMode || isSearchTabMode));
    // // 是否点击过向前冲按钮
    // const showCloud = ref(false);
    const afterSelect = ref(false);
    const firstSigned = ref(false);
    const mapOffset = computed<number>(() => {
        if (!isFourTabMode && !isSearchTabMode && !isSmallScreen) {
            // 无tab正常尺寸
            return 0;
        } else if (!isFourTabMode && !isSearchTabMode && isSmallScreen) {
            // 无tab 小屏 上移0.3rem / 30px
            return -0.3;
        } else if (isFourTabMode && !isSearchTabMode && isInIOS()) {
            // 4tab & 小屏 下移 0.1rem
            return 0.2;
        } else if (!isFourTabMode && isSearchTabMode) {
            // -1tab & 小屏 下移 0.12rem
            return -0.26;
        }
        return 0;
    });
    // IP 头像显示层级是否要低于中央建筑
    const isIpLowerThanMidIcon = computed(() => {
        // 目前视觉上看 第5、6、7格需要IP低一些
        return [5, 6, 7].includes((currentStep.value ?? 0) % 12);
    });
    const showBubbleAndAniAfterSelect = computed(() => !firstSigned.value && afterSelect.value);
    // 当前站点的key
    const gCurrentStationKey = computed(() => gCurrentStation.value?.stationInfo.uniqueKey ?? undefined);
    const manager: Ref<ChargeForwardManager | null> = ref(null);

    const container = shallowRef<HTMLDivElement>();

    const renderMap: ComputedRef<RenderMapType | null | undefined> = computed(() => manager.value?.renderMap);

    // 当前进度
    const gProgress = computed<ChessProgressView | undefined>(() => homeData.value?.chessboard?.progress);
    // 地图背景信息
    const mapKey = computed(() =>
        (gCurrentStation.value?.stationInfo.stationThemeKey ?? '') in MapConfigData
            ? gCurrentStation.value?.stationInfo.stationThemeKey
            : MapKeyType.Default,
    );
    // 今天
    const todayIndex = computed(() => gCurrentStation.value?.stationInfo.stationDayIndex);
    // 站点标签文案
    const stationTagView = computed<StationTagView | undefined>(
        () => homeData.value?.homeFEConstantsConfig?.stationTagView,
    );
    // 玩家信息
    const gUserInfo = computed<UserBasicView | undefined>(() => homeData.value?.chessboard?.userInfo!);

    // IP头像层级是否要高于中央建筑物
    const isIPHigher = computed(() => manager.value?.isIPHigher ?? true);
    // 是否正在移动
    const moving = computed(() => manager.value?.isMoving.value ?? false);
    // 地图配置选择
    // 直接写死一个配置，mapConfig主要包含背景图片的大小，二级页的背景大小应该不变
    const mapConfig = computed<MapConfig>(() => MapConfigData.city);
    // 格子贴皮 for 商业化
    const gridSkinConfig = computed(() => ({
        gridSkinUrl: gCurrentStation.value?.stationInfo?.gridSkinUrl ?? '',
        gridSkinSponsor: gCurrentStation.value?.stationInfo?.gridSkinSponsor ?? '',
        gridSkinLocation: gCurrentStation.value?.stationInfo?.gridSkinLocation ?? [],
    }));
    // 背景style
    // [废弃] 为了使用高级格式图片，不采用这种方式渲染背景图片了
    // const backgroundImage = computed(() => {
    //     return mapConfig.value?.imgs.url!;
    // });
    const backgroundStyle = computed(() => {
        return {
            backgroundPosition: `center center`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: '100% auto', // 或者 'contain'，根据需求选择
            transform: `translateY(${mapOffset.value}rem)`,
        };
    });
    const haloArrowPicOffsetStyle = computed(() => {
        return {
            transform: `rotate(0deg)`,
        };
    });
    const popoverContainerStyle = computed(() => {
        return {
            transform: `translateY(${(renderMap.value?.popover.offset ?? 0) / 100}rem)`,
        };
    });
    // ip样式
    const ipStyle = computed(() => {
        return {
            transform: `translate(${(renderMap.value?.ip.position.x ?? 0) / 100}rem, ${(renderMap.value?.ip.position.y ?? 0) / 100}rem)`,
        };
    });
    // 地图中央槽位样式
    const middleIconStyle = computed(() => {
        return {
            transform: `translate(${(renderMap.value?.midIcon.position.x ?? 0) / 100}rem, ${(renderMap.value?.midIcon.position.y ?? 0) / 100}rem)`,
        };
    });
    // 返回主会场按钮样式
    const backBtnStyle = computed(() => {
        return {
            transform: `translate(${(renderMap.value?.backBtn.position.x ?? 0) / 100}rem, ${(renderMap.value?.backBtn.position.y ?? 0) / 100}rem)`,
        };
    });
    const ipOnStation = computed(() => renderMap.value?.ip.onStation ?? false);
    const ipOnBirthStation = computed(() => {
        // 如果是第一天，出生地永远不会是站点
        if (todayIndex.value === 1) {
            return false;
        }
        return renderMap.value?.ip.onBirth ?? false;
    });

    const showStationBubble = ref(false);
    // 奖励格子信息
    const gllwardGridLayout = computed(() => {
        const grid = gCurrentStation.value?.llrewdGridLayout ?? {};
        return grid;
    });
    // 站点配置
    const stationConfig = ref<StationConfig>([]);

    // 奖励信息
    const llwardGrids = computed(() => {
        return renderMap.value?.grids;
    });
    // 两侧建筑信息
    const buildings = computed(() => {
        return renderMap.value?.buildings;
    });
    // 站点信息，包含站点渲染位置
    const stations = computed(() => {
        return renderMap.value?.stations;
    });
    // 商业化格子贴皮
    const gridSkins = computed(() => {
        return renderMap.value?.skins;
    });

    // 当前格子信息
    const currentGrid = computed(() => {
        return renderMap.value?.currentGrid;
    });
    // 背景图偏移
    const offset = computed(() => renderMap.value?.background.offset ?? 0);

    // watch([offset, mapKey], ([val, val2]) => {
    //     // 没有点击过向前冲按钮
    //     if (!showCloud.value && val) {
    //         // TODO 处理全局变量
    //         const windowOffset = (window as any).offset;
    //         const windowMapKey = (window as any).mapKey;
    //         const isFirstTimeLoaded = (window as any).isFirstTimeLoaded;
    //         setTimeout(() => {
    //             console.log('出转场==', val, Math.floor(val), windowOffset, val2, windowMapKey, isFirstTimeLoaded);
    //         }, 1500);
    //         // ssg和csr的地图偏移量 diff 绝对值
    //         const absDiff = Math.abs(Math.floor(val) - parseInt(windowOffset ?? '0', 10));
    //         // absDiff>5 或 地图key变了 出卷轴
    //         if ((absDiff > 5 || val2 !== windowMapKey) && !isFirstTimeLoaded) {
    //             nativeCloud.show();
    //             setTimeout(() => {
    //                 nativeCloud.hide();
    //             }, 2000);
    //         }
    //         showCloud.value = true;
    //     }
    // });
    const dayIsToday = (dayIndex: number | null | undefined) => {
        return !!dayIndex && !!todayIndex.value && dayIndex === todayIndex.value;
    };

    const dayIsTomorrow = (dayIndex: number | null | undefined) => {
        return !!dayIndex && !!todayIndex.value && dayIndex === todayIndex.value + 1;
    };

    const dayIsMoreThanToday = (dayIndex: number | null | undefined) => {
        return !!dayIndex && !!todayIndex.value && dayIndex >= todayIndex.value + 1;
    };

    watch([todayIndex], () => {});

    watchDebounced(
        renderMap,
        () => {
            log('render map info', renderMap.value);
        },
        {
            immediate: true,
            deep: true,
            debounce: 3000,
        },
    );
    return {
        moving,
        renderMap,
        manager,
        ipStyle,
        haloArrowPicOffsetStyle,
        autoScroll,
        backBtnStyle,
        middleIconStyle,
        ipOnStation,
        llwardGrids,
        buildings,
        stations,
        gridSkinConfig,
        gridSkins,
        currentGrid,
        mapConfig,
        gllwardGridLayout,
        gProgress,
        gCurrentStation,
        gUserInfo,
        container,
        currentStep,
        isIpLowerThanMidIcon,
        stationConfig,
        backgroundStyle,
        afterSelect,
        firstSigned,
        showBubbleAndAniAfterSelect,
        stationTagView,
        offset,
        mapKey,
        showStationBubble,
        popoverContainerStyle,
        ipOnBirthStation,
        isIPHigher,
        gCurrentStationKey,
        dayIsToday,
        dayIsTomorrow,
        dayIsMoreThanToday,
        isFourTabMode,
        isSearchTabMode,
    };
});
