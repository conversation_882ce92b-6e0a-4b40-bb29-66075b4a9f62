<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { transViewValue } from '@pet/core.mobile';

const draggableContainer = ref<HTMLElement | null>(null);
const containerStyles = ref({ transform: 'translate(0px, 0px)' });
const props = defineProps<{
    bgColor?: string;
}>();

let isDragging = false;
let initialX = 0;
let initialY = 0;

function handleMouseDown(e: MouseEvent) {
    isDragging = true;
    const currentStyles = window.getComputedStyle(draggableContainer.value!);
    const currentTransform = currentStyles.getPropertyValue('transform');
    const transformMatch = currentTransform.match(/translate\((\d+)px, (\d+)px\)/);
    const currentX = parseInt(transformMatch![1]!, 10);
    const currentY = parseInt(transformMatch![2]!, 10);

    initialX = e.clientX - currentX;
    initialY = e.clientY - currentY;
}

function handleMouseMove(e: MouseEvent) {
    if (!isDragging) {
        return;
    }
    const newX = e.clientX - initialX;
    const newY = e.clientY - initialY;
    containerStyles.value = { transform: `translate(${transViewValue(newX)}px, ${transViewValue(newY)}px)` };
}

function handleMouseUp() {
    isDragging = false;
}

// Touch events
function handleTouchStart(e: TouchEvent) {
    isDragging = true;
    const currentStyles = window.getComputedStyle(draggableContainer.value!);
    const currentTransform = currentStyles.getPropertyValue('transform');
    const transformMatch = currentTransform.match(/translate\((\d+)px, (\d+)px\)/);
    const currentX = parseInt(transformMatch![1]!, 10);
    const currentY = parseInt(transformMatch![2]!, 10);

    initialX = e.touches[0]!.clientX - currentX;
    initialY = e.touches[0]!.clientY - currentY;
}

function handleTouchMove(e: TouchEvent) {
    if (!isDragging) {
        return;
    }
    const newX = e.touches[0]!.clientX - initialX;
    const newY = e.touches[0]!.clientY - initialY;
    containerStyles.value = { transform: `translate(${transViewValue(newX)}px, ${transViewValue(newY)}px)` };
}

function handleTouchEnd() {
    isDragging = false;
}

onMounted(() => {
    draggableContainer.value?.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    draggableContainer.value?.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);
});

onUnmounted(() => {
    draggableContainer.value?.removeEventListener('mousedown', handleMouseDown);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    draggableContainer.value?.removeEventListener('touchstart', handleTouchStart);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
});
</script>

<template>
    <div
        ref="draggableContainer"
        :style="{ backgroundColor: props.bgColor, ...containerStyles }"
        class="draggable-container"
    >
        <slot />
    </div>
</template>

<style scoped>
.draggable-container {
    width: 50px;
    height: 50px;
    border: 1px solid black;
    position: absolute;
}
</style>
