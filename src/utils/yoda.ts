import useCaptureDebugLog from '@pet/yau.logger';
import { invoke } from '@yoda/bridge';

import { FOLLOW_ACTIVITY_ID, FOLLOW_REFER } from '@/common/const/follow';
const { log } = useCaptureDebugLog('utils:yoda');
/**
 * 关注
 * @returns
 */
export const followUser = async (props: { userId: string }) => {
    const { userId } = props;
    try {
        // 设置关注栈
        await invoke('social.setFollowActivityId', {
            activityId: FOLLOW_ACTIVITY_ID,
        });
        // 点击关注
        const res = await invoke('social.followUser', {
            following: true,
            userId: `${userId}`,
            followRefer: FOLLOW_REFER,
        });
        return res;
    } catch (error) {
        log('followUser-关注用户调用桥失败', JSON.stringify(error));
        return {
            followState: 0,
        };
    }
};
