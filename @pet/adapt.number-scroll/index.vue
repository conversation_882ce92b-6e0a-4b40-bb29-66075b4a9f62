<script lang="ts">
export default {
    name: 'AdaptNumberScroll',
};
</script>
<script lang="ts" setup>
/**
 * 数值滚动
 */
import { transViewValue } from '@pet/core.mobile';
import { ref, watch, computed, nextTick, onMounted } from 'vue-demi';
import { useDomListener } from '@pet/yau.core/event/useDomListener';
import { debounce } from '@pet/yau.core/lodash/function';

interface Props {
    /**
     * 数值数值
     */
    numberValue?: number | string;
    /**
     * 保留位数
     */
    toFixed?: number;
    /**
     * 小数点取值方式
     */
    math?: 'floor' | 'round';
    /**
     * 文字字号
     */
    fontSize?: number;
    /**
     * 文字行高
     */
    lineHeight?: number;
    /**
     * 单位
     */
    unit?: string;
    /**
     * 动效时长
     */
    animationDuration?: number;
    /**
     * 最大值
     */
    max?: number;
    /**
     * 千分位分隔
     */
    thousandsSeparator?: boolean;
    /**
     * 当前暂无值时显示问号
     */
    notShowValue?: boolean;
    /**
     * 是否初始化就播放动效
     */
    immediateEffect?: boolean;
    /**
     * 每位滚动延迟时间
     */
    itemEffectDelay?: number;
    /**
     * 每位滚动延迟是否从右向左
     */
    itemEffectDelayReverse?: boolean;
    /**
     * 滚动的动效曲线
     */
    timingFunction?: string;
}

const props = withDefaults(defineProps<Props>(), {
    numberValue: 0,
    fontSize: 12,
    lineHeight: 0,
    unit: 'rem',
    animationDuration: 500,
    max: 9999999,
    thousandsSeparator: false,
    math: 'round',
    immediateEffect: true,
    itemEffectDelay: 0,
    timingFunction: 'linear',
});

const emit = defineEmits<{
    /**
     * 动画结束
     * @arg { number } index
     */
    (event: 'transitionend', index: number): void;
}>();

const KEYS = '0123456789?'.split('');

const orderNum = ref<string[]>([]);
const numberItem = ref<HTMLElement[]>([]);
const initFlag = ref(false);

const genNumWithUnit = (num: number, count = 1) => {
    return props.unit === 'rem' ? `${Math.round(transViewValue(num)) * count}px` : `${num * count}${props.unit}`;
};

const fontSize = ref(genNumWithUnit(props.fontSize));
const baseHeightValue = ref(props.lineHeight > 0 ? props.lineHeight : props.fontSize);
const baseHeight = ref(genNumWithUnit(baseHeightValue.value));
const listHeight = ref(genNumWithUnit(baseHeightValue.value, KEYS.length - 1));

/**
 * 处理一下如果屏幕尺寸发生变化重新获取一下大小
 */
useDomListener(
    () => window,
    'resize',
    () => {
        fontSize.value = genNumWithUnit(props.fontSize);
        baseHeight.value = genNumWithUnit(baseHeightValue.value);
        listHeight.value = genNumWithUnit(baseHeightValue.value, KEYS.length - 1);
    },
);

const resolvedBox = computed(() => ({
    fontSize: fontSize.value,
    height: baseHeight.value,
}));

const resolvedNumberStyle = computed(() => ({
    height: baseHeight.value,
}));

const noInitTransition = computed(() => (props.notShowValue || !props.immediateEffect) && !initFlag.value);

const resolvedItemStyle = computed(() => ({
    height: listHeight.value,
    transition: noInitTransition.value ? 'none' : `transform ${props.animationDuration}ms ${props.timingFunction}`,
}));

const setNumNodeStyle = (value: string) => ({
    height: resolvedSpanStyle.value.lineHeight,
    justifyContent: value === '1' ? 'center' : 'start',
});

function getDelayDurationStyle(i: number) {
    const reverseDelay = `${(orderNum.value.length - i) * props.itemEffectDelay}ms`;
    const normalDelay = `${i * props.itemEffectDelay}ms`;
    return {
        transitionDelay: props.itemEffectDelayReverse ? reverseDelay : normalDelay,
    };
}

const resolvedSpanStyle = computed(() => ({
    height: baseHeight.value,
    lineHeight: baseHeight.value,
}));

function setStyle(item: string) {
    return Number.isNaN(Number(item)) ? { width: 'auto' } : {};
}

function sortRefIndex(a: HTMLElement, b: HTMLElement) {
    return +(a.dataset.index ?? 0) - +(b.dataset.index ?? 0);
}

const transformCache = new Map<number, string>();

function getTransformValue(i: number) {
    if (!transformCache.has(i)) {
        transformCache.set(i, `translateY(-${(i * 100) / (KEYS.length - 1)}%)`);
    }
    return transformCache.get(i)!;
}

const numberWrapperStyles = computed(() => {
    return orderNum.value.map((item) => {
        return {
            ...resolvedSpanStyle.value,
            ...setStyle(item),
        };
    });
});

const numberListStyles = computed(() => {
    return orderNum.value.map((item, index) => {
        return {
            ...resolvedItemStyle.value,
            ...getDelayDurationStyle(index),
        };
    });
});

const numNodeStyles = computed(() => {
    return KEYS.map((value) => {
        return {
            ...setNumNodeStyle(value),
        };
    });
});

const resolvedFloat = (num: number, bits?: number, math = props.math) => {
    if (bits !== undefined) {
        const mathFunc = Math[math];
        return mathFunc((num + Number.EPSILON) * 10 ** bits) / 10 ** bits;
    }
    return num;
};

const isNumeric = (val: string) => !Number.isNaN(val);

const toOrderNumString = (value: string) => {
    if (!isNumeric(value)) {
        console.warn('请确认值是个数字');
    }
    let step = value.length - orderNum.value.length;
    const numValue = +value;
    step = numValue > props.max ? step + 1 : step;
    // init
    orderNum.value = [...Array.from({ length: step }, () => '0'), ...orderNum.value];
    nextTick(() => {
        const finalNum = numValue > props.max ? props.max : value;
        orderNum.value = finalNum.toString().split('');

        if (numValue > props.max) {
            orderNum.value = [...orderNum.value, '+'];
        }

        if (props.notShowValue) {
            orderNum.value = Array(props.numberValue.toString().length).fill('?');
        }
    });
};

const toOrderNum = (num: number) => {
    const numRes = resolvedFloat(num, props.toFixed);
    let step = numRes.toString().length - orderNum.value.length;
    step = numRes > props.max ? step + 1 : step;
    // init
    orderNum.value = [...Array.from({ length: step }, () => '0'), ...orderNum.value];

    // setValue
    nextTick(() => {
        const finalNum = numRes > props.max ? props.max : numRes;
        if (props.thousandsSeparator) {
            orderNum.value = resolvedFloat(finalNum, props.toFixed)
                .toLocaleString(undefined, { minimumFractionDigits: props.toFixed })
                .split('');
        } else if (props.toFixed && props.toFixed >= 0) {
            orderNum.value = resolvedFloat(finalNum, props.toFixed).toFixed(props.toFixed).split('');
        } else {
            orderNum.value = resolvedFloat(finalNum, props.toFixed).toString().split('');
        }

        if (numRes > props.max) {
            orderNum.value = [...orderNum.value, '+'];
        }

        if (props.notShowValue) {
            orderNum.value = Array(props.numberValue.toString().length).fill('?');
        }
    });
};

const toOrderValue = (value: number | string) => {
    if (typeof value === 'string') {
        toOrderNumString(value);
    } else {
        toOrderNum(value);
    }
};

const setValues = async () => {
    const numberItemValues = numberItem.value ?? [];
    await nextTick();
    const numberItems = numberItemValues.sort(sortRefIndex);
    const numberArr = orderNum.value;
    return {
        numberItems,
        numberArr,
    };
};

const setNumberTransform = async () => {
    const { numberItems, numberArr } = await setValues();

    // 批量获取
    const updates = numberItems.map((elem, index) => {
        const i = KEYS.indexOf(numberArr[index]!);
        return {
            elem,
            transform:
                Number(numberArr[index]) >= 0 || KEYS.includes(numberArr[index]!)
                    ? getTransformValue(i)
                    : 'translateY(0)',
        };
    });

    // 批量设置
    requestAnimationFrame(() => {
        updates.forEach(({ elem, transform }) => {
            const el = elem;
            el.style.transform = transform;
        });
    });
};

const resolveEffect = () => {
    toOrderValue(props.numberValue);
    nextTick(() => {
        setNumberTransform();
    });
};

watch(
    () => props.notShowValue,
    (val) => {
        if (!initFlag.value) {
            initFlag.value = true;
        }
        resolveEffect();
    },
);

watch(
    () => props.numberValue,
    debounce((newValue, oldValue) => {
        if (!initFlag.value && oldValue !== undefined) {
            initFlag.value = true;
        }
        if (newValue !== oldValue) {
            resolveEffect();
        }
    }, 1000 / 60),
    {
        immediate: true,
    },
);

onMounted(() => {
    resolveEffect();
});

function onTransitionEnd(i: number, e: Event) {
    if (e.target !== numberItem.value[i]) {
        return;
    }
    emit('transitionend', i);
}
</script>
<template>
    <div class="box-item" :style="resolvedBox">
        <div
            v-for="(item, index) in orderNum"
            :key="index"
            v-memo="[item, resolvedNumberStyle]"
            class="number-item"
            :style="resolvedNumberStyle"
        >
            <span class="number-wrapper" :style="numberWrapperStyles?.[index]">
                <div
                    v-if="Number.isNaN(Number(item)) && !KEYS.includes(item)"
                    ref="numberItem"
                    class="number-symbol"
                    :data-tag="item"
                    :data-index="index"
                >
                    <div class="number-node">
                        <span class="number-node-text">{{ item }}</span>
                    </div>
                </div>
                <div
                    v-else
                    ref="numberItem"
                    :data-tag="item"
                    :data-index="index"
                    class="number-list"
                    :style="numberListStyles?.[index]"
                    @transitionend="onTransitionEnd(index, $event)"
                >
                    <div v-for="(n, _i) in KEYS" :key="n" class="number-node" :style="numNodeStyles?.[_i]">
                        <span class="number-node-text">{{ n }}</span>
                    </div>
                </div>
            </span>
        </div>
    </div>
</template>

<style>
:root {
    /* 字重 */
    --adapt-number-scroll-font-weight: 700;
}
</style>

<style lang="scss" scoped>
.box-item {
    display: inline-flex;
    align-items: center;
    transform: translateZ(0);
}
.number-item {
    height: 24px;
    &:last-child {
        margin-right: 0;
    }
}
.number-wrapper {
    position: relative;
    display: inline-block;
    vertical-align: top;
    height: 12px;
    line-height: 12px;
    text-align: left;
    overflow: hidden;
}
.number-list {
    width: 100%;
    font-style: normal;
}
.number-node {
    text-align: center;
    font-weight: var(--adapt-number-scroll-font-weight);
    display: flex;
    align-items: center;
    font-variant-numeric: tabular-nums;
}
</style>
