<script setup lang="ts">
import Btn from '@pet/adapt.button/index.vue';
import type { Button } from '../type';

defineProps<{
    mainButton?: Button;
    subButton?: Button;
}>();

const emits = defineEmits<{
    (event: 'mainBtnClick'): void;
    (event: 'subBtnClick'): void;
}>();

const getGuideId = (key: string) => `${key}-${Date.now()}`;
</script>

<template>
    <template v-if="!subButton">
        {{ subButton }}
        <Btn type="primary-linear" class="single" @click="emits('mainBtnClick')">
            {{ mainButton?.linkText }}
        </Btn>
    </template>
    <div v-else class="double">
        <Btn class="left" @click="emits('subBtnClick')"> {{ subButton.linkText }} </Btn>
        <Btn type="primary-linear" class="right" @click="emits('mainBtnClick')"
            ><template v-if="mainButton?.icon" #icon><img :src="mainButton.icon" /></template>
            {{ mainButton?.linkText }}
        </Btn>
    </div>
</template>

<style lang="scss" scoped>
.single {
    --adapt-button-width: 190px;
    --adapt-button-height: 66px;
    --adapt-button-primary-background-image: var(--cny-base-open-packet-single-btn-bg-img);
    --adapt-button-primary-background-color: transparent;
    --adapt-button-primary-font-linear: var(--cny-base-open-packet-single-btn-font-color);
    --adapt-button-font-size: 20px;
    font-family: KuaiYuanHuiTi, sans-serif;
    margin-bottom: 20px;
    position: relative;
}

.double {
    display: flex;
    flex-direction: row;
    --adapt-button-primary-background-color: transparent;
    margin-bottom: 20px;

    .left {
        border: 1px solid;
        border-radius: 200px;
        box-sizing: border-box;
        overflow: hidden;
        margin-right: 12px;
        --adapt-button-font-weight: bold;
        --adapt-button-height: 60px;
        --adapt-button-font-size: 17px;
        --adapt-button-width: 122px;
        --adapt-button-padding: 4;
        border-color: var(--cny-base-open-packet-left-btn-font-color);
        --adapt-button-primary-font-color: var(--cny-base-open-packet-left-btn-font-color);
    }

    .right {
        position: relative;
        font-family: KuaiYuanHuiTi, sans-serif;
        --adapt-button-height: 60px;
        --adapt-button-font-size: 17px;
        --adapt-button-width: 122px;
        --adapt-button-padding: 4;
        --adapt-button-primary-font-linear: var(--cny-base-open-packet-right-btn-font-color);
        --adapt-button-primary-background-image: var(--cny-base-open-packet-right-btn-bg-img);
    }
}

:deep(.hand) {
    --adapt-guide-hand-box-width: 108px;
    --adapt-guide-hand-box-height: 98px;
}
</style>
