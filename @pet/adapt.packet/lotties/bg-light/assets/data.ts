import Asset_1 from './images/img_10_0.png';
import Asset_2 from './images/img_11_1.png';
import Asset_0 from './images/img_9.png';

export default {
    v: '5.12.2',
    fr: 30,
    ip: 0,
    op: 107,
    w: 828,
    h: 1792,
    nm: 'finl_B-2',
    ddd: 0,
    assets: [
        {
            id: 'image_20',
            w: 828,
            h: 828,
            u: Asset_0,
            p: '',
            e: 0,
        },
        {
            id: 'image_21',
            w: 828,
            h: 828,
            u: Asset_1,
            p: '',
            e: 0,
        },
        {
            id: 'image_22',
            w: 828,
            h: 828,
            u: Asset_2,
            p: '',
            e: 0,
        },
    ],
    layers: [
        {
            ty: 3,
            ks: {
                o: {
                    a: 0,
                    k: 100,
                    ix: 11,
                    x: "var $bm_rt;\nvar $bm_rt;\nvar $bm_rt;\nvar $bm_rt;\n$bm_rt = $bm_rt = $bm_rt = $bm_rt = thisComp.layer('红包-打开-高度366.png').transform.opacity;",
                },
                r: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 51,
                            s: [0],
                        },
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 54,
                            s: [6],
                        },
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 58,
                            s: [-6],
                        },
                        {
                            t: 63,
                            s: [0],
                        },
                    ],
                    ix: 10,
                    x: 'var $bm_rt;\nvar $bm_rt;\nvar $bm_rt;\nvar $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.09;\nfreq = 2;\ndecay = 10;\n$bm_rt = $bm_rt = $bm_rt = $bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = t = 0;\n} else {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = t = sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = add(value, div(mul(mul(v, amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))));\n} else {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = value;\n}',
                },
                p: {
                    a: 0,
                    k: [308.34, 570, 0],
                    ix: 2,
                    l: 2,
                },
                a: {
                    a: 0,
                    k: [188, 66, 0],
                    ix: 1,
                    l: 2,
                },
                s: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [1.083, 1.083, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.167, 0.167, 0],
                            },
                            t: 40,
                            s: [100, 100, 100],
                        },
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.964, 0.964, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.042, 0.042, 0],
                            },
                            t: 43,
                            s: [110, 90, 100],
                        },
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.833, 0.833, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [-0.095, -0.095, 0],
                            },
                            t: 46,
                            s: [90, 110, 100],
                        },
                        {
                            t: 50,
                            s: [100, 100, 100],
                        },
                    ],
                    ix: 6,
                    l: 2,
                },
            },
            ao: 0,
            ddd: 0,
            ind: 11,
            cl: 'png',
            ip: 40,
            op: 64,
            st: 0,
            nm: '按钮-1个.png',
            sr: 1,
            parent: 13,
        },
        {
            ty: 3,
            ks: {
                o: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 12,
                            s: [0],
                        },
                        {
                            t: 16,
                            s: [100],
                        },
                    ],
                    ix: 11,
                },
                r: {
                    a: 0,
                    k: 0,
                    ix: 10,
                },
                p: {
                    a: 0,
                    k: [0, 10.78, 0],
                    ix: 2,
                    l: 2,
                },
                a: {
                    a: 0,
                    k: [308, 375.5, 0],
                    ix: 1,
                    l: 2,
                },
                s: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.833, 0.833, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.167, 0.167, 0],
                            },
                            t: 12,
                            s: [30, 30, 100],
                        },
                        {
                            t: 16,
                            s: [100, 100, 100],
                        },
                    ],
                    ix: 6,
                    l: 2,
                    x: 'var $bm_rt;\nvar $bm_rt;\nvar $bm_rt;\nvar $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.09;\nfreq = 2;\ndecay = 10;\n$bm_rt = $bm_rt = $bm_rt = $bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = t = 0;\n} else {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = t = sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = add(value, div(mul(mul(v, amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))));\n} else {\n    $bm_rt = $bm_rt = $bm_rt = $bm_rt = value;\n}',
                },
            },
            ao: 0,
            ddd: 0,
            ind: 13,
            cl: 'png',
            ip: 0,
            op: 110,
            st: -15,
            nm: '红包-打开-高度366.png',
            sr: 1,
            parent: 17,
        },
        {
            ddd: 0,
            ind: 14,
            ty: 2,
            nm: 'light-1.png',
            cl: 'png',
            parent: 17,
            refId: 'image_20',
            sr: 1,
            ks: {
                o: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 14,
                            s: [0],
                        },
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 19,
                            s: [100],
                        },
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 28,
                            s: [60],
                        },
                        {
                            t: 34,
                            s: [0],
                        },
                    ],
                    ix: 11,
                },
                r: {
                    a: 0,
                    k: 0,
                    ix: 10,
                },
                p: {
                    a: 0,
                    k: [0, 0, 0],
                    ix: 2,
                    l: 2,
                },
                a: {
                    a: 0,
                    k: [414, 414, 0],
                    ix: 1,
                    l: 2,
                },
                s: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.87, 0.87, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.167, 0.167, 0],
                            },
                            t: 14,
                            s: [34.851, 34.851, 100],
                        },
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.833, 0.833, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.184, 0.184, 0],
                            },
                            t: 19,
                            s: [120, 120, 100],
                        },
                        {
                            t: 34,
                            s: [300, 300, 100],
                        },
                    ],
                    ix: 6,
                    l: 2,
                },
            },
            ao: 0,
            ip: 11,
            op: 110,
            st: 11,
            bm: 0,
        },
        {
            ddd: 0,
            ind: 15,
            ty: 2,
            nm: 'light-2.png',
            cl: 'png',
            parent: 17,
            refId: 'image_21',
            sr: 1,
            ks: {
                o: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 30,
                            s: [50],
                        },
                        {
                            t: 40,
                            s: [0],
                        },
                    ],
                    ix: 11,
                },
                r: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 16,
                            s: [-95],
                        },
                        {
                            t: 40,
                            s: [44],
                        },
                    ],
                    ix: 10,
                },
                p: {
                    a: 0,
                    k: [0, 0, 0],
                    ix: 2,
                    l: 2,
                },
                a: {
                    a: 0,
                    k: [414, 414, 0],
                    ix: 1,
                    l: 2,
                },
                s: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.87, 0.87, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.167, 0.167, 0],
                            },
                            t: 16,
                            s: [0, 0, 100],
                        },
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.865, 0.865, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.189, 0.189, 0],
                            },
                            t: 23,
                            s: [60, 60, 100],
                        },
                        {
                            t: 40,
                            s: [160, 160, 100],
                        },
                    ],
                    ix: 6,
                    l: 2,
                },
            },
            ao: 0,
            ip: 0,
            op: 110,
            st: -23,
            bm: 0,
        },
        {
            ddd: 0,
            ind: 16,
            ty: 2,
            nm: 'light-3.png',
            cl: 'png',
            parent: 17,
            refId: 'image_22',
            sr: 1,
            ks: {
                o: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833],
                                y: [0.833],
                            },
                            o: {
                                x: [0.167],
                                y: [0.167],
                            },
                            t: 32,
                            s: [50],
                        },
                        {
                            t: 42,
                            s: [0],
                        },
                    ],
                    ix: 11,
                },
                r: {
                    a: 0,
                    k: 0,
                    ix: 10,
                },
                p: {
                    a: 0,
                    k: [0, 0, 0],
                    ix: 2,
                    l: 2,
                },
                a: {
                    a: 0,
                    k: [414, 414, 0],
                    ix: 1,
                    l: 2,
                },
                s: {
                    a: 1,
                    k: [
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.895, 0.895, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.167, 0.167, 0],
                            },
                            t: 18,
                            s: [0, 0, 100],
                        },
                        {
                            i: {
                                x: [0.833, 0.833, 0.833],
                                y: [0.865, 0.865, 1],
                            },
                            o: {
                                x: [0.167, 0.167, 0.167],
                                y: [0.219, 0.219, 0],
                            },
                            t: 25,
                            s: [120, 120, 100],
                        },
                        {
                            t: 42,
                            s: [260, 260, 100],
                        },
                    ],
                    ix: 6,
                    l: 2,
                },
            },
            ao: 0,
            ip: 9,
            op: 110,
            st: 9,
            bm: 0,
        },
        {
            ty: 3,
            ks: {
                o: {
                    a: 0,
                    k: 0,
                    ix: 11,
                },
                r: {
                    a: 0,
                    k: 0,
                    ix: 10,
                },
                p: {
                    a: 0,
                    k: [414, 894.5, 0],
                    ix: 2,
                    l: 2,
                },
                a: {
                    a: 0,
                    k: [0, 0, 0],
                    ix: 1,
                    l: 2,
                },
                s: {
                    a: 0,
                    k: [100, 100, 100],
                    ix: 6,
                    l: 2,
                },
            },
            ao: 0,
            ddd: 0,
            ind: 17,
            ip: 0,
            op: 110,
            st: 0,
            nm: '空 323',
            sr: 1,
        },
    ],
    fonts: {
        list: [
            {
                origin: 0,
                fPath: '',
                fClass: '',
                fFamily: 'MFYuanHei',
                fWeight: '',
                fStyle: 'Regular',
                fName: 'MFYuanHei-Regular',
                ascent: 84.8373413085938,
                loaded: true,
                monoCase: {
                    node: {},
                    w: 2529,
                    parent: {},
                },
                sansCase: {
                    node: {},
                    w: 2264,
                    parent: {},
                },
                helper: {},
                cache: {},
            },
            {
                origin: 0,
                fPath: '',
                fClass: '',
                fFamily: 'PingFang SC',
                fWeight: '',
                fStyle: 'Semibold',
                fName: 'PingFangSCSemibold-',
                ascent: 72.7996826171875,
                loaded: true,
                monoCase: {
                    node: {},
                    w: 2529,
                    parent: {},
                },
                sansCase: {
                    node: {},
                    w: 2264,
                    parent: {},
                },
                helper: {},
                cache: {},
            },
        ],
    },
};
