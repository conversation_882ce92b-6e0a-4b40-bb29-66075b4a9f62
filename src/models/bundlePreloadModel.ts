import { createUseModel } from '@gundam/model';
import { bundlePreloader } from '@pet/yau.yoda';
import { whenever } from '@vueuse/core';
import { onUnmounted } from 'vue';

import { useHomeModel } from './homeModel';

export interface BundleConfig {
    /** bundle标识 */
    bundleId: string;
    /** 配置字段名（从homeFEConstantsConfig中获取延迟时间） */
    delayConfigKey: string;
    /** 默认延迟时间（毫秒） */
    defaultDelayMs: number;
    /** 是否启用（可选，用于开关控制） */
    enabled?: boolean;
}

/**
 * 通用bundle预加载模型
 * 支持配置多个bundle的预加载策略
 */
export const useBundlePreloadModel = createUseModel(() => {
    // 这里会不会有循环引用的问题？
    const { homeData } = useHomeModel();

    // 预加载配置列表
    const bundleConfigs: BundleConfig[] = [
        {
            bundleId: 'GrowthRecruitSummerVenue',
            delayConfigKey: 'preloadRecruitDelayMs',
            defaultDelayMs: 5000,
            enabled: true,
        },
        // 后续可以在这里添加其他bundle配置
        // {
        //   bundleId: 'OtherBundle',
        //   delayConfigKey: 'preloadOtherDelayMs',
        //   defaultDelayMs: 3000,
        //   enabled: true,
        // },
    ];

    // 监听主接口数据变化，触发预加载
    whenever(
        homeData,
        (newData) => {
            const enabledConfigs = bundleConfigs.filter((config) => config.enabled !== false);

            enabledConfigs.forEach(({ bundleId, delayConfigKey, defaultDelayMs }) => {
                const delayMs = newData?.homeFEConstantsConfig?.[delayConfigKey] ?? defaultDelayMs;
                bundlePreloader.schedulePreload(bundleId, delayMs);
            });
        },
        {
            flush: 'pre',
            once: true,
        },
    );

    // 组件卸载时清理
    onUnmounted(() => {
        bundleConfigs.forEach(({ bundleId }) => {
            bundlePreloader.clearSchedule(bundleId);
            bundlePreloader.removeBundle(bundleId);
        });
    });

    return {
        /**
         * 手动预加载指定bundle
         * @param bundleId bundle标识
         */
        preloadBundle: (bundleId: string) => bundlePreloader.preloadBundle(bundleId),

        /**
         * 手动卸载指定bundle
         * @param bundleId bundle标识
         */
        removeBundle: (bundleId: string) => bundlePreloader.removeBundle(bundleId),

        /**
         * 检查bundle是否已加载
         * @param bundleId bundle标识
         */
        isBundleLoaded: (bundleId: string) => bundlePreloader.isLoaded(bundleId),

        /**
         * 添加新的bundle配置（动态添加）
         * @param config bundle配置
         */
        addBundleConfig: (config: BundleConfig) => {
            bundleConfigs.push(config);
            // 如果主接口数据已存在，立即触发预加载
            if (homeData.value && config.enabled !== false) {
                const delayMs = homeData.value?.homeFEConstantsConfig?.[config.delayConfigKey] ?? config.defaultDelayMs;
                bundlePreloader.schedulePreload(config.bundleId, delayMs);
            }
        },

        /**
         * 获取当前bundle配置列表
         */
        getBundleConfigs: () => [...bundleConfigs],
    };
});
