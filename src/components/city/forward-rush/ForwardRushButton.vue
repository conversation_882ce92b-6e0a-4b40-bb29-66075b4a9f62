<script setup lang="ts">
import { useDowngradeLevel } from '@pet/25cny.downgrade-level';
import { debounce } from '@pet/yau.core';
import { whenever } from '@vueuse/core';

import { useLogger } from '@/init/logger';
import { ForwardRushBtnStatus, useConfigModel } from '@/models/city/configModel';
import { useForwardRushBtnModel } from '@/models/city/forwardRushBtnModel';

import MainBtn, { type AniType } from './main-btn/MainBtn.vue';

const { enableForwardRush, mainButtonStatus, buttonAndBubbleInfo, handleForwardRushBtnClick } =
    useForwardRushBtnModel();
const { effectShowStatus } = useDowngradeLevel();
const { sendShow, sendClick } = useLogger();

// 是否在进行中
const inProcessing = ref(false);
const handleTouchStart = (e: TouchEvent) => {
    e.preventDefault();
    if (!inProcessing.value && enableForwardRush.value) {
        inProcessing.value = true;
        return;
    }
};
// 向前冲的过程中需要将按钮的文案变为“前进中”
const mainText = computed(() => {
    if (inProcessing.value) {
        return mainButtonStatus?.value === ForwardRushBtnStatus.PROCESSING ? '前进中' : '返回中';
    }
    return buttonAndBubbleInfo.value.buttonText;
});

// 走格子流程结束之后需要重置一下processing的值
whenever(enableForwardRush, () => {
    inProcessing.value = false;
});

/**
 * 点击向前冲按钮
 */

const handleClickForwardRush = debounce(() => {
    sendClick('OP_ACTIVITY_JION_BUTTON', {
        title: buttonAndBubbleInfo.value.buttonText,
    });
    handleForwardRushBtnClick();
});

const aniType = computed<AniType>(() => {
    // if (mainButtonStatus.value === ForwardRushBtnStatus.PROCESSING) {
    //     return 'shine-breath';
    // }
    return 'shine';
});

whenever(
    () => buttonAndBubbleInfo.value.buttonText,
    (val) => {
        sendShow('OP_ACTIVITY_JION_BUTTON', {
            title: val,
        });
    },
    { immediate: true },
);
</script>

<template>
    <div class="forward-btn-container" :class="{ scroll: true }">
        <!-- 主按钮主体 -->
        <MainBtn
            id="MAIN_BTN"
            class="forward-btn"
            :in-processing="inProcessing"
            :disabled="!enableForwardRush"
            :ani-type="aniType"
            :pause="!enableForwardRush"
            :effect-status="effectShowStatus.L1"
            :css-status="effectShowStatus.L1"
            @click="handleClickForwardRush"
        >
            <div class="forward-btn-text real-button-area" @touchstart="handleTouchStart">
                <span class="forward-btn-text-main">{{ mainText }}</span>
            </div>
        </MainBtn>
    </div>
</template>

<style lang="scss" scoped>
.forward-btn-container {
    margin-bottom: 31px;
    width: 280px;
    height: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;

    .bottom-mask {
        width: 214px;
        height: 35px;
        background-color: transparent;
        position: absolute;
        bottom: 0;
        z-index: 2;
    }

    .forward-btn {
        position: relative;
        --adapt-button-main-font-family: unset;

        .real-button-area {
            width: 275.5px;
            height: 91px;
            border-radius: 100px;
        }

        .disabled {
            opacity: 0.5;
        }

        &-text {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 2;

            &-main {
                text-align: center;
                width: 100%;
                font-family: KuaiYuanHuiTi;
                font-size: 30px;
                transform: skew(-7deg);
                font-weight: 400;
                height: 33px;
                line-height: 28px;
                letter-spacing: 1px;
                background: linear-gradient(346deg, #fff2c6 6.59%, #fff 67.44%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            &-sub {
                margin-top: 4px;
                height: 18px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                color: #fff;
                font-size: 13px;
                line-height: 18px;

                .left-count-side {
                    margin-right: 3px;
                }
            }
        }
    }
}
</style>
