<pet-info lang="json">
{ "title": "基本使用", "description": "默认参数的展现形式" }
</pet-info>
<script setup lang="ts">
import { vGuide, useGuideState } from '../index';
import Button from '@pet/adapt.button/index.vue';
import AdaptGuide from '@pet/adapt.guide/index.vue';
import AdaptToolTip from '@pet/adapt.tool-tip/index.vue';
import { RawActivityStore } from '@pet/yau.yoda/local-storage';
import { useDateFormat, useNow } from '@vueuse/core';
import { onMounted } from 'vue';
const { initGuideDirective, startGuideShow, showGuideById } = useGuideState();

onMounted(() => {
    // 初始化存储
    const store = new RawActivityStore('test');
    initGuideDirective({ store });
    // 开始引导
    startGuideShow();
});
const now = useDateFormat(useNow(), 'YYYY-MM-DD HH:mm');

const handleClick = () => {
    // 函数式触发引导
    showGuideById('GUIDE_03');
};
</script>
<template>
    <div class="page">
        <div
            v-guide.target="{
                id: `GUIDE_01`,
                priority: 1,
                enabled: true,
                component: AdaptGuide,
                offset: {
                    right: 0,
                },
                duration: 3000,
            }"
            class="guide"
        >
            手势引导
        </div>
        <div
            v-guide="{
                id: `GUIDE_02`,
                priority: 1,
                enabled: true,
                component: AdaptToolTip,
                props: {
                    tip: '现在时间 ' + now,
                    width: 200,
                },
                offset: {
                    top: -50,
                },
                duration: 3000,
            }"
            class="guide-1"
        >
            气泡引导
        </div>
        <div
            v-guide="{
                id: `GUIDE_03`,
                priority: 1,
                enabled: true,
                showByFunc: true,
                component: AdaptToolTip,
                props: {
                    tip: '现在时间 ' + now,
                    width: 200,
                },
                offset: {
                    top: -50,
                },
                duration: 3000,
            }"
            class="guide-2"
        >
            气泡重复引导
        </div>

        <div
            v-guide="{
                id: `GUIDE_04`,
                priority: 1,
                enabled: true,
                everydayLimitCount: 2,
                component: AdaptToolTip,
                props: {
                    tip: '现在时间 ' + now,
                    width: 200,
                },
                offset: {
                    top: -50,
                },
                duration: 3000,
            }"
            class="guide-3"
        >
            每天出两次气泡
        </div>

        <Button class="guide button" @click="handleClick">点击出气泡引导</Button>
    </div>
</template>

<style lang="scss" scoped>
.page {
    font-size: 14px;
    width: 100vw;
    height: 100vh;
    position: relative;

    .guide {
        width: 200px;
        height: 20px;
        position: absolute;
    }

    .guide-1 {
        position: absolute;
        top: 150px;
    }

    .guide-2 {
        position: absolute;
        top: 280px;
        left: 20px;
    }

    .guide-3 {
        position: absolute;
        top: 480px;
        left: 20px;
    }

    .button {
        top: 380px;
        left: 20px;
    }
}
</style>
