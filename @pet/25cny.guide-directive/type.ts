import type { Component } from 'vue';

type MaybePromise<T> = T | Promise<T>;
export type GuidedResult = Record<string, boolean>;

export type GuideType = 'normal' | 'mask' | 'target';
// export enum GuideType {
//     normal = 0,
//     mask = 1,
//     target = 2,
// }

export interface GuideConfigOptions {
    /** 如果传入了时长，那么在这个时间之后会自动销毁。如果没有传入时长，需要在 onMaskClick 里面处理点击（可选）*/
    duration?: number;

    props?: Record<string, unknown>;
    type?: GuideType;

    /** 可以函数式调用（不限次数，不会自动触发） */
    showByFunc?: boolean;
    /** 函数式自动触发 */
    autoShowByFunc?: boolean;
    /** 是否滚动到引导位置 */
    autoScroll?: boolean;
    /** 每日挂载显示频控 */
    everydayLimitCount?: number;
    /** 总频控 */
    totalLimitCount?: number;
    /** 强插 */
    important?: boolean;
    /** 当前id引导队列内唯一 */
    onlyOne?: boolean;
    /** 无操作间隔后再触发 */
    nonInterval?: number;
    /** log 打点 */
    logName?: string;
    /** tips 透传 */
    tipsText?: string;
    enabled?: boolean;
}

export interface GuideShowOptions extends GuideConfigOptions {
    /** 引导的 hook */
    beforeShow?: () => MaybePromise<void>;
    afterHide?: () => MaybePromise<void>;
    overLimit?:()=>MaybePromise<void>;


    /** 在 duration 为 null 的情况下，点击遮罩会先触发这个，返回 true 表示关闭 guide */
    onMaskClick?: ((e: TouchEvent, el: HTMLElement) => MaybePromise<boolean>) | (() => void);
    onTargetClick?: ((e: TouchEvent) => MaybePromise<boolean>) | (() => void);

    // 回调，用来上报log
    onShow?: () => MaybePromise<void>;
    onClick?: () => MaybePromise<void>;
}

export type GuideDirectiveOptions = {
    /** 引导的唯一 ID */
    id: string;
    /** 引导的优先级 */
    priority: number;
    /** 引导是否被启用 */
    enabled: boolean;
    /** 引导相对于目标元素的位置，先只做 absolute 定位 */
    offset: {
        top?: number;
        left?: number;
        right?: number;
        bottom?: number;
    };

    /** 可以传入一个自定义元素来渲染 */
    component: (() => Promise<Component>) | Component;
    /** 消失动画时长(ms)，默认 0ms */
    fadeOutDuration?: number;
} & GuideShowOptions;

export type GuideOptions = GuideDirectiveOptions & {
    el: HTMLElement;
    type: GuideType;
};


export type GuideTaskOption = GuideShowOptions & {
    /** 引导的优先级 */
    priority: number;
    /** 引导相对于目标元素的位置，先只做 absolute 定位 */
    offset: {
        top?: number;
        left?: number;
        right?: number;
        bottom?: number;
    };

    /** 可以传入一个自定义元素来渲染 */
    component: (() => Promise<Component>) | Component;
    /** 消失动画时长(ms)，默认 0ms */
    fadeOutDuration?: number;
}