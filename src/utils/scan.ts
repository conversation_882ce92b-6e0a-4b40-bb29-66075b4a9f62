import { compareVersion, isIOS, uaGetInfo } from '@pet/yau.core/device';
import { invoke } from '@yoda/bridge';

export type OpenTarget = '_self' | '_blank';
export interface ScanConfig {
    isNeedCallBack?: boolean;
    isForceCloseScanPage?: boolean;
    targetHandlerActionList?: string[];
}
export interface ScanResult {
    extendInfo?: {
        kwaiUrl: string;
    };
    originQRCode?: string;
    errorMessage?: any;
}
const errMsg = ref('暂时无法识别哦');
const scene = ref('normal');
const target = ref<OpenTarget>('_self');
const config = ref<ScanConfig>();

function isNeedDefaultSceneApp() {
    const { kwai, ksnebula } = uaGetInfo();
    const appVersion = kwai ?? ksnebula ?? '';
    // 开始支持的最小版本
    // iOS10.10.40开始 安卓10.11.20开始
    const startSupportedApp = isIOS() ? '10.10.40' : '10.11.20';
    const result = compareVersion(appVersion, startSupportedApp);
    return result === 'lt';
}

const scanParams = computed(() => {
    // 默认扫码以及版本兜底
    if (config === undefined || isNeedDefaultSceneApp()) {
        return {
            scene: scene.value,
        };
    }

    const defaultConfig = {
        isNeedCallBack: true,
        isForceCloseScanPage: true,
    };
    return {
        ...defaultConfig,
        ...config,
    };
});

async function scanAction(url?: string) {
    if (url === undefined) {
        console.error('scan:', 'unavailable url');
        return;
    }
    if (target.value === '_self') {
        window.location.replace(url);
    } else if (target.value === '_blank') {
        const startWithHttp = url.startsWith('http');
        if (startWithHttp) {
            await invoke('tool.loadUrlOnNewPage', { url, type: 'back', leftTopBtnType: 'back' });
        } else {
            window.location.href = url;
        }
    }
}

export async function scan() {
    try {
        const res = (await invoke('tool.scanCode', scanParams.value)) as unknown as ScanResult;
        const url = res.extendInfo?.kwaiUrl;
        scanAction(url);
    } catch (error) {
        if (Boolean(errMsg)) {
            await invoke('ui.showToast', {
                type: 'normal',
                text: errMsg.value,
            });
        }
    }
}
