<pet-info lang="json">
{ "title": "关闭按钮位置", "description": "" }
</pet-info>
<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import { ref } from 'vue-demi';

import Layer from '../index.vue';

const show = ref(false);
const createComponent = ref(false);

function confirm() {
    // eslint-disable-next-line no-alert
    alert('confirm');
}
function sunConfirm() {
    // eslint-disable-next-line no-alert
    alert('sub-confirm');
}

function handleClose() {
    console.log('close');
}
</script>

<template>
    <div>
        <Layer
            v-if="createComponent"
            v-model:show="show"
            has-logo
            custom-logo="https://w1-pro.kskwai.com/kos/nlav11066/2022_common_static/business/modal_logo_shanzhashuxia_v1.png"
            title="此处为主标题"
            sub-title="此处为副标题，可用于辅助信息"
            cover="https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2021-12-16/1639634522781.3e4249c181e76b81.png"
            main-btn-text="立即使用"
            close-pos="bottom"
            @confirm="confirm"
            @sub-confirm="sunConfirm"
            @close="handleClose"
            @after-leave="createComponent = false"
        >
        </Layer>
        <Button
            class="item-btn"
            type="primary"
            @click="
                () => {
                    show = true;
                    createComponent = true;
                }
            "
            >打开蒙层</Button
        >
    </div>
</template>

<style scoped>
.item-btn {
    margin: 0 2px;
}
</style>
