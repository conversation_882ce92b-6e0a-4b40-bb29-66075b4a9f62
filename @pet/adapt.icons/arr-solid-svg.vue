<template>
    <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class="icon-arr-solid"
    >
        <circle cx="10" cy="10" r="10" fill="currentColor" />
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8.20041 6.23966C8.48227 5.93613 8.95682 5.91856 9.26035 6.20041L12.7603 9.45048C12.9132 9.59239 13 9.79152 13 10.0001C13 10.2086 12.9132 10.4078 12.7603 10.5497L9.26049 13.7996C8.95696 14.0814 8.48241 14.0639 8.20056 13.7603C7.9187 13.4568 7.93627 12.9823 8.2398 12.7004L11.1478 10.0001L8.23966 7.29959C7.93613 7.01774 7.91856 6.54319 8.20041 6.23966Z"
            class="arrow"
        />
    </svg>
</template>

<style>
:root {
    /* 箭头默认填充颜色 */
    --adapt-icon-arrow-solid-color: #fe3666;
    /* 箭头Icon默认颜色 */
    --adapt-icon-arrow-solid-arr-color: #fff;
}
</style>

<style lang="scss" scoped>
.icon-arr-solid {
    color: var(--adapt-icon-arrow-solid-color);
    .arrow {
        fill: var(--adapt-icon-arrow-solid-arr-color);
    }
}
</style>
