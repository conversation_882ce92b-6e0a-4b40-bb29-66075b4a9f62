<pet-info lang="json">
{ "title": "事件", "description": "气泡动效的事件" }
</pet-info>
<script setup lang="ts">
import type { ArrDirection, ArrAlign } from '@pet/adapt.tool-tip/types';
import { ref, computed } from 'vue-demi';

import Popover from '../index.vue';
import type { AnimationType } from '../types';
const show = ref(false);
const state = ref('');
function handleShow() {
    show.value = !show.value;
}
function popoverClose() {
    console.log('close');
    state.value = 'close';
}

function popoverLeave() {
    console.log('leave');
    state.value = 'leave';
}

const arrDirection: ArrDirection = 'bottom';
const arrAlign: ArrAlign = 'center';
const arrDistance = ref(-1);
const bgColor = ref('');
const color = ref('');
const animationType: AnimationType = 'scale-in-out';
</script>

<template>
    <div class="demo">
        <div class="popover-wrapper">
            <p>state {{ state }}</p>
            popover: {{ show }}
            <Popover
                :show="show"
                :animation-type="animationType"
                :arr-direction="arrDirection"
                :arr-align="arrAlign"
                :arr-distance="+arrDistance"
                :background="bgColor"
                :text-color="color"
                class="popover-main"
                @close="popoverClose"
                @after-leave="popoverLeave"
            >
                显示文案内容
            </Popover>
            <button @click="handleShow">{{ show ? '隐藏' : '显示' }}</button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
    font-size: 16px;
    padding-top: 100px;
}
label {
    display: inline-flex;
    justify-content: center;
}
.popover-wrapper {
    position: relative;
    height: 30px;
}
.popover-main {
    left: 20px;
    top: -40px;
}
.up-arrow {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid transparent;
    border-bottom: 10px solid #ccc;
}
.up-arrow-border {
    position: absolute;
    top: 2.5px;
    left: 50%;
    transform: translate(-50%, 0);
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 9px solid transparent;
    border-bottom: 9px solid #fff;
}
.custom-css-arr {
    position: relative;
    top: -20px;
}
</style>
