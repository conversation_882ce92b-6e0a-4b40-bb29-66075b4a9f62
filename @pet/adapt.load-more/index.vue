<script lang="ts">
export default {
    name: 'AdaptLoadMore',
};
</script>
<script setup lang="ts">
import { useIntersectionObservable } from '@pet/yau.core/event/useIntersectionObservable';
import Loading from '@pet/adapt.loading/infinity-loading.vue';
import { ref } from 'vue-demi';

export interface LoadMoreProps {
    /**
     * intersectionObservable rootMargin参数
     */
    rootMargin?: string;
    /**
     * 是否加载更多
     */
    noMore?: boolean;
    /**
     * 到底提示内容
     */
    message?: string;
}

export interface LoadMoreEmits {
    /**
     * 可见状态
     * @arg { boolean } val
     */
    (event: 'visible', val: boolean): void;
    /**
     * 是否到底部
     */
    (event: 'to-bottom'): void;
}

const props = withDefaults(defineProps<LoadMoreProps>(), {
    rootMargin: '0px 0px 100px 0px',
    noMore: false,
    message: '已经到底了，没有更多内容了~',
});

const emit = defineEmits<LoadMoreEmits>();

const loadRef = ref<HTMLDivElement | null>(null);

useIntersectionObservable(
    loadRef,
    () => {
        emit('to-bottom');
        emit('visible', true);
    },
    () => {
        emit('visible', false);
    },
    {
        rootMargin: props.rootMargin,
    },
);
</script>
<template>
    <div ref="loadRef">
        <div style="text-align: center">
            <!--slot 插槽-->
            <slot v-if="$slots.info" name="info" />
            <!--默认展示-->
            <div v-else style="text-align: center">
                <p v-if="noMore">{{ message }}</p>
                <Loading v-else />
            </div>
        </div>
    </div>
</template>
