<script setup lang="ts">
import useCaptureDebugLog from '@pet/yau.logger';

import RewardPointImg from './RewardPointImg.vue';
import type { RewardPointItem } from './types';

const props = withDefaults(defineProps<RewardPointItem & { logName: string }>(), {
    isLastPoint: false,
    opened: false,
});

const { logName } = toRefs(props);
const { log } = useCaptureDebugLog(logName.value);
</script>
<template>
    <div :id="`point_icon_${step}`" :class="{ point_wrap: true, opened: opened, last_point: isLastPoint }">
        <div class="icon_wrap">
            <RewardPointImg
                class="icon_bg"
                :icon="icon"
                :opened="opened"
                :is-last-point="isLastPoint"
                :log-name="logName"
            />

            <template v-if="!isLastPoint">
                <span class="icon_text">{{ llrewdView }}</span>
            </template>
            <template v-else>
                <div class="btn"></div>
            </template>
        </div>

        <span class="point_desc"> {{ text }} </span>
    </div>
</template>
<style scoped lang="scss">
.point_wrap {
    position: relative;

    .icon_wrap {
        background-color: #fff;

        .icon_bg {
            width: 44px;
            height: 43px;
        }

        .icon_text {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 20.5px;

            height: 17.24px;
            line-height: 17.24px;
            font-family: 'MiSans-Heavy';
            letter-spacing: 0;
            text-align: center;
            color: #00d164;
            font-size: 13px;
        }
    }

    .point_desc {
        color: #9c9c9c;
        text-align: center;
        font-family: 'PingFang SC', sans-serif;
        letter-spacing: 0;
        font-size: 11px;
        font-weight: 400;

        position: absolute;
        top: 47px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
    }

    &.opened {
        .icon_wrap {
            .icon_text {
                opacity: 0.5;
            }
        }
    }

    &.last_point {
        transform: translateY(-13px);

        .icon_wrap {
            background-color: transparent;

            width: 50px;
            height: 58px;
            position: relative;

            .icon_text {
                opacity: 1;
            }

            .icon_bg {
                width: 50px;
                height: 50px;
            }

            .btn {
                width: 48px;
                height: 22px;
                @include bg('./assets/btn_last.png');
                background-size: 100% 100%;
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
            }
        }

        .point_desc {
            top: 60px;
        }
    }
}
</style>
