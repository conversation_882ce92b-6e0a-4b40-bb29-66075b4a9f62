<pet-info lang="json">
{ "title": "用户列表", "priority": 2 }
</pet-info>
<script lang="ts" setup>
import Card from '../index.vue';
import '@pet/adapt.reset/reset.css';

const userInfo = {
    avatar: '//p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    title: '快手',
    desc: '7月23日 15:56',
    attachedText: '6.99元',
};
</script>

<template>
    <div class="card-wrapper">
        <div class="user-list">
            <Card v-bind="userInfo">
                <template #widget>
                    <span class="rank">1</span>
                </template>
            </Card>
            <Card v-bind="userInfo">
                <template #widget>
                    <span class="rank">2</span>
                </template>
            </Card>
            <Card v-bind="userInfo">
                <template #widget>
                    <span class="rank">3</span>
                </template>
            </Card>
        </div>
    </div>
</template>

<style scoped>
.card-wrapper {
    background: #f5f5f5;
    padding: 10px;
}

.rank {
    font-family: 'Avenir';
    font-style: italic;
    font-weight: 900;
    font-size: 16px;
    line-height: 18px;
    display: block;
    width: 28px;
    text-align: center;
    margin-left: -4px;
    margin-right: 12px;
}

.user-list {
    background-color: #fff;
    border-radius: 16px;
}
</style>
