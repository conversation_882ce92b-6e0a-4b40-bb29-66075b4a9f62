<script lang="ts">
export default {
    name: 'AdaptMarqueeSlide',
};
</script>

<script lang="ts" setup generic="T extends any">
import { ref, onBeforeUnmount, watch, computed, onMounted, nextTick } from 'vue-demi';
import type { Ref } from 'vue-demi';
import { px2rem } from '@pet/core.mobile';

import type { SlidePosition, SlideDirection } from './types';

const props = withDefaults(
    defineProps<{
        /**
         * list列表
         */
        list: T[];
        /**
         * 滑动时间
         */
        animationDuration?: number;
        /**
         * 停留时间
         */
        stayDuration?: number;
        /**
         * 对齐位置 'start' | 'center' | 'end'
         */
        position?: SlidePosition;
        /**
         * 方向 'row' | 'col'
         */
        direction?: SlideDirection;
        /**
         * 边缘淡出遮罩
         */
        mask?: boolean;
        /**
         * 单个slide的高度
         */
        slideHeight?: number;
    }>(),
    {
        animationDuration: 1500,
        stayDuration: 2000,
        position: 'start',
        direction: 'col',
    },
);

const emit = defineEmits<{
    /**
     * 每次暂停的索引
     * @arg { number } index
     */
    (event: 'stop', index: number): void;
}>();

const currentItem = ref(props.list[0]) as Ref<T | undefined>;
const nextItem = ref(props.list[1]) as Ref<T | undefined>;
const beforeAnimate = ref(false);
const animating = ref(false);
const animateEnd = ref(false);
const nextIndex = ref(1);
const nextShowIndex = ref(1);
const indexCur = ref(0);
const indexNext = ref(1);

const changeOrder = ref(false);
let timer: ReturnType<typeof setTimeout> | null;
const withMask = computed(() => props.mask === true && 'with-mask');
const marqueeClass = computed(() => [`marquee-${props.position}`, `marquee-${props.direction}`, withMask.value]);
const marqueeItemClass = computed(() => {
    return {
        animate: beforeAnimate.value,
        animating: animating.value,
        'animate-end': animateEnd.value,
        'change-oreder': changeOrder.value,
    };
});
const itemRef = ref<HTMLElement | null>(null);

const unwatchItem = watch([currentItem, nextItem], ([cur, next]) => {
    if (JSON.stringify(cur) === JSON.stringify(next) && props.list.length === 2) {
        if (timer) {
            clearTimeout(timer);
        }
    }
});

async function animate() {
    animateEnd.value = false;
    beforeAnimate.value = true;

    requestAnimationFrame(() => {
        animating.value = true;
    });
}

/**
 * 由于iOS重新渲染DOM的时候，img会出现闪的问题，所以要保证当前展示的item dom不能更新，所以每次更新的内容都是窗口外的dom
 * 通过change flex order的方式保证每次都translateY(-100%)完成向上滚动
 */
let evenShow = true;
watch(nextShowIndex, (val) => {
    if (evenShow) {
        indexCur.value = val;
        currentItem.value = props.list[val];
        changeOrder.value = true;
    } else {
        indexNext.value = val;
        nextItem.value = props.list[val];
        changeOrder.value = false;
    }
    evenShow = !evenShow;
});

const dataChange = ref(false);

function stringfiyData(arr: any[]) {
    return JSON.stringify(arr).replace(/\[|]/g, '');
}

function arrayIncludeChecker(arr: any[], target: any[]) {
    return stringfiyData(arr).includes(stringfiyData(target)) || stringfiyData(target).includes(stringfiyData(arr));
}

const unwatchList = watch(
    () => props.list,
    (val, oldValue) => {
        if (oldValue.length < 2 && val.length > 1) {
            currentItem.value = props.list[0];
            nextItem.value = props.list[1];
            animate();
        }

        const isDataRenew = !arrayIncludeChecker(val, oldValue);
        if (isDataRenew) {
            dataChange.value = true;
        }
    },
);

watch([dataChange, animateEnd], ([change, end]) => {
    if (change && end) {
        if (evenShow) {
            nextItem.value = props.list[0];
        } else {
            currentItem.value = props.list[0];
        }
        nextIndex.value = 0;
        dataChange.value = false;
    }
});

function stop(e: Event) {
    if (e.target !== itemRef.value) {
        return;
    }
    emit('stop', nextShowIndex.value);
    animateEnd.value = true;
    beforeAnimate.value = false;
    animating.value = false;
    if (props.list?.length === 1) {
        if (timer) {
            clearTimeout(timer);
        }
        return;
    }
    nextShowIndex.value = nextIndex.value >= props.list.length - 1 ? 0 : nextIndex.value + 1;
    nextIndex.value = nextShowIndex.value;

    timer = setTimeout(() => {
        animate();
    }, props.stayDuration);
}

const marqueeHeight = ref(0);

const unwatchHeight = watch(
    [itemRef, () => props.slideHeight],
    ([val, height]) => {
        if (val) {
            marqueeHeight.value = height ?? val.offsetHeight;
            unwatchHeight();
        }
    },
    {
        flush: 'post',
    },
);

const marqueeHeightStyle = computed(() => {
    return marqueeHeight.value > 0
        ? {
              height: px2rem(marqueeHeight.value),
          }
        : {};
});

const animateDuration = computed(() => {
    return (
        animating.value &&
        !animateEnd.value && {
            transitionDuration: `${props.animationDuration}ms`,
        }
    );
});

onMounted(() => {
    if (props.list.length > 1) {
        const t = setTimeout(() => {
            animate();
            clearTimeout(t);
        }, props.stayDuration);
    }
});

onBeforeUnmount(() => {
    if (timer) {
        clearTimeout(timer);
    }
    unwatchItem();
    unwatchList();
});
</script>

<template>
    <div v-if="list.length" class="marquee-wrapper" :style="marqueeHeightStyle">
        <!-- 仅有一条时直接切 -->
        <div v-if="list.length === 1" class="marquee-list" :class="marqueeClass">
            <div class="marquee-item">
                <slot name="item" :data="list[0]" :index="0" />
            </div>
        </div>
        <!-- 轮播内容 -->
        <div v-show="list.length > 1" class="marquee-list" :class="marqueeClass">
            <div
                ref="itemRef"
                class="marquee-item"
                :class="marqueeItemClass"
                :style="animateDuration"
                :data-index="indexCur"
                @transitionend="stop"
            >
                <slot name="item" :data="currentItem" :index="indexCur" />
            </div>
            <div
                v-if="nextItem"
                ref="itemRefNext"
                class="marquee-item"
                :class="marqueeItemClass"
                :style="animateDuration"
                :data-index="indexNext"
            >
                <slot name="item" :data="nextItem" :index="indexNext" />
            </div>
        </div>
    </div>
</template>

<style>
:root {
    /* 字号 */
    --adapt-marquee-slide-font-size: 1em;
    /* 切换时候的缓动时间函数 */
    --adapt-marquee-slide-transition-timing-function: linear;
    /* 两边遮罩的样式 纵向 */
    --adapt-marquee-slide-vartical-mask-image: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0),
        #000 20%,
        #000 80%,
        rgba(0, 0, 0, 0) 100%
    );
    /* 两边遮罩的样式 横向 */
    --adapt-marquee-slide-horizontal-mask-image: linear-gradient(
        to right,
        rgba(0, 0, 0, 0),
        #000 10%,
        #000 90%,
        rgba(0, 0, 0, 0) 100%
    );
}
</style>

<style lang="scss" scoped>
.marquee-wrapper {
    overflow: hidden;
}

.marquee-list {
    display: flex;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    font-size: var(--adapt-marquee-slide-font-size);
    min-height: 1em;
}

.marquee-item {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 0;
    flex-basis: 100%;
    flex-shrink: 0;
    order: 1;

    &.animate {
        transition-property: transform;
        transition-timing-function: var(--adapt-marquee-slide-transition-timing-function);
    }

    &.animating {
        transform: translateY(-100%);
    }

    &.animate-end {
        transform: translateY(0);
    }

    &.change-oreder:first-child {
        order: 2;
    }
}

.marquee-col {
    flex-direction: column;
    &.with-mask {
        -webkit-mask-image: var(--adapt-marquee-slide-vartical-mask-image);
    }
    &.marquee-center {
        align-items: center;
    }
    &.marquee-end {
        align-items: flex-end;
    }
}
.marquee-row {
    &.with-mask {
        -webkit-mask-image: var(--adapt-marquee-slide-horizontal-mask-image);
    }
    .marquee-item {
        width: 100%;
        flex: 0 0 100%;
        &.animating {
            transform: translate(-100%, 0);
        }
    }
    &.marquee-center {
        .marquee-item {
            justify-content: center;
        }
    }
    &.marquee-end {
        .marquee-item {
            justify-content: flex-end;
        }
    }
}
</style>
