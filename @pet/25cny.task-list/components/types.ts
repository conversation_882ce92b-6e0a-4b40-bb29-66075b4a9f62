import type Avatar from '@pet/adapt.avatar/index.vue';
import type Button from '@pet/adapt.button/index.vue';
import type Card from '@pet/adapt.card/index.vue';
import type { CardSize } from '@pet/adapt.card/types';
import type EmptyStatus from '@pet/adapt.empty-status/index.vue';
// import type { GuideDirectiveOptions } from '@pet/adapt.guide-directive/directive';
import type { UseTaskOptions } from '@pet/work.task-list-core/useTask';
import type { TaskInfo } from '@pet/work.task-list-core/utils/framework/DefaultImpl/types';

type UiPassThroughConfigEntry<T> = {
    override?: T;
    base?: T;
};

export type EmptyStatusProps = InstanceType<typeof EmptyStatus>['$props'];
export type CardProps = InstanceType<typeof Card>['$props'];
export type AvatarProps = InstanceType<typeof Avatar>['$props'];
export type ButtonProps = InstanceType<typeof Button>['$props'];

export type TaskListOptions = UseTaskOptions & {
    readonly displayTaskFilter?: (task: TaskInfo) => boolean;
    // 是否展示按钮上的loading效果
    readonly hideButtonLoading?: boolean;
    readonly guideTaskId?: number;
    // readonly guideOptions?: GuideDirectiveOptions;
    readonly size?: CardSize;
    readonly rearrangeTaskList?: (originalTaskList: Readonly<TaskInfo>[]) => TaskInfo[];
    readonly getTaskItemClass?: (task: TaskInfo, index: number) => string;
    readonly uiPassThroughConfig?: {
        emptyStatus?: UiPassThroughConfigEntry<EmptyStatusProps>;
        taskCard?: UiPassThroughConfigEntry<CardProps>;
        relationChainTask?: {
            taskCard?: UiPassThroughConfigEntry<CardProps>;
            avatar?: UiPassThroughConfigEntry<AvatarProps>;
        };
        button?: UiPassThroughConfigEntry<ButtonProps>;
    };
};
