<script lang="ts">
export default {
    name: 'AdaptPopover',
};
</script>

<script lang="ts" setup>
import { vClickOutside } from '@pet/adapt.directives/click-outside';
import ToolTip from '@pet/adapt.tool-tip/index.vue';
import type { ArrDirection, ArrAlign, Position, ToolTipSize } from '@pet/adapt.tool-tip/types';
import AdaptTransition from '@pet/adapt.transition/index.vue';
import { px2rem } from '@pet/core.mobile';
import { ref, computed } from 'vue-demi';

import type { AnimationType, ArrowInfo } from './types';

export interface PopoverProps {
    /**
     * 是否显示
     */
    show?: boolean;
    /**
     * 动画效果
     * @values scale-in-out-debounce, scale-in-out, fade
     */
    animationType?: AnimationType;
    /**
     * 文字内容
     */
    tip?: string;
    /**
     * 箭头所在位置
     * @values bottom, top, right, left
     */
    arrDirection?: ArrDirection;
    /**
     * 箭头对齐方式
     * @values start, center, end
     */
    arrAlign?: ArrAlign;
    /**
     * 箭头距离
     */
    arrDistance?: number;
    /**
     * 箭头背景
     */
    background?: string;
    /**
     * 箭头距离
     */
    textColor?: string;
    /**
     * 气泡位置
     */
    position?: Position;
    /**
     * 气泡尺寸
     * @values normal, small
     */
    size?: ToolTipSize;
    /**
     * 进场时长
     */
    enterDuration?: number;
    /**
     * 出场时长
     */
    leaveDuration?: number;
    /**
     * 箭头信息
     */
    arrowInfo?: ArrowInfo;
    /**
     * 箭头类型
     */
    arrJoinType?: 'miter' | 'round';
}

const props = withDefaults(defineProps<PopoverProps>(), {
    show: false,
    animationType: 'scale-in-out-debounce',
    arrDirection: 'top',
    arrAlign: 'end',
    arrDistance: -1,
    background: '',
    textColor: '',
    size: 'normal',
    enterDuration: 333,
    leaveDuration: 233,
    arrowInfo: () => ({
        width: 20,
        height: 6,
    }),
});

const emit = defineEmits<{
    /**
     * 更新show状态
     * @arg { boolean } show
     */
    (event: 'update:show', show: boolean): void;
    /**
     * 气泡离开
     */
    (event: 'after-leave'): void;
    /**
     * 点击气泡外关闭
     */
    (event: 'close'): void;
}>();

const isEnterIn = ref(false);
function enterIn() {
    isEnterIn.value = true;
}

function leaveOut() {
    emit('after-leave');
    isEnterIn.value = false;
}
function handleOutsideClick() {
    if (isEnterIn.value) {
        emit('close');
        emit('update:show', false);
        isEnterIn.value = false;
    }
}

const tipRef = ref<InstanceType<typeof ToolTip>>();

function getOffsetWithArrDirection(direction: ArrDirection, arrHeight: number) {
    switch (direction) {
        case 'bottom':
            return `calc(100% + ${px2rem(arrHeight)})`;
        case 'left':
            return `-${px2rem(arrHeight)}`;
        case 'right':
            return `calc(100% + ${px2rem(arrHeight)})`;
        default:
            return `-${px2rem(arrHeight)}`;
    }
}

function getOffsetWithArrAlign(align: ArrAlign, middlePointPercent: number) {
    switch (align) {
        case 'start':
            return `${middlePointPercent}%`;
        case 'center':
            return 'center';
        case 'end':
            return `${100 - middlePointPercent}%`;
        default:
            // align satisfies never;
            return 'center';
    }
}

function getArrMiddlePointPercent(arrWidth: number, arrDistance: number, whole: number) {
    const arrPosition = arrDistance + arrWidth / 2;
    return whole > 0 ? Math.floor((arrPosition * 100) / whole) : 0;
}

function getArrIsAtXAxis(arrDirection: ArrDirection) {
    return ['top', 'bottom'].includes(arrDirection);
}

const transformOrigin = computed(() => {
    const arrWidth = props.arrowInfo.width;
    const arrHeight = props.arrowInfo.height;
    const xAxis = getArrIsAtXAxis(props.arrDirection);
    const tipWidth = tipRef.value?.$el?.clientWidth ?? 0;
    const tipHeight = tipRef.value?.$el?.clientHeight ?? 0;
    const whole = xAxis ? tipWidth : tipHeight;
    // 默认如果不设置arrDistance，取最小值，见tool-tip的css
    const arrDistance = props.arrDistance < 0 ? 20 : props.arrDistance;
    const middlePointPercent = getArrMiddlePointPercent(arrWidth, arrDistance, whole);
    const align = getOffsetWithArrAlign(props.arrAlign, middlePointPercent);
    const direction = getOffsetWithArrDirection(props.arrDirection, arrHeight);
    return {
        transformOrigin: xAxis ? `${align} ${direction}` : `${direction} ${align}`,
    };
});
</script>

<template>
    <AdaptTransition
        :name="animationType"
        :duration="{ enter: enterDuration, leave: leaveDuration }"
        @after-enter="enterIn"
        @after-leave="leaveOut"
    >
        <ToolTip
            v-if="show"
            ref="tipRef"
            v-click-outside="handleOutsideClick"
            :arr-align="arrAlign"
            :arr-direction="arrDirection"
            :arr-distance="arrDistance"
            :position="position"
            :background="background"
            :tip="tip"
            :text-color="textColor"
            :arr-join-type="arrJoinType"
            :strict-display="isEnterIn"
            class="popover"
            :style="transformOrigin"
        >
            <template v-if="$slots.default" #default>
                <!--@slot 默认插槽 -->
                <slot />
            </template>
            <template v-if="$slots.arrow" #arrow>
                <!--@slot 箭头插槽 -->
                <slot name="arrow" />
            </template>
        </ToolTip>
    </AdaptTransition>
</template>

<!-- 先去掉 -->
<!-- <style scoped>
.popover {
    /* 解决抖动问题 */
    will-change: transform;
}
</style> -->

<style lang="scss" scoped>
.popover {
    --adapt-tool-tip-radius: 16px;
}
</style>
