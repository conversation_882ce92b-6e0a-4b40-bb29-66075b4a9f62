<pet-info lang="json">
{ "title": "控制播放次数", "description": "控制点击次数" }
</pet-info>
<script lang="ts" setup>
import { ref } from 'vue-demi';

import Guide from '../index.vue';

const repeatTime = ref(10);
const showGuide = ref(true);

function endPlay() {
    showGuide.value = false;
}
</script>

<template>
    <div class="frame-box">
        <Guide v-if="showGuide" :repeat="repeatTime" class="guide-hand-position" @end="endPlay" />
    </div>
</template>

<style lang="css" scoped>
.frame-box {
    position: relative;
    width: 200px;
    height: 200px;
    background: rgba(0, 0, 0, 0.5);
    padding: 45px 0 0 10px;
    box-sizing: border-box;
    color: #fff;
    font-size: 12px;
}

.frame-text {
    position: absolute;
    top: 30px;
    left: 10px;
}

.guide-hand-position {
    left: 50px;
    top: 50px;
}
</style>
