<pet-info lang="json">
{ "title": "辅助文案", "description": "搭配文案字段", "priority": 77 }
</pet-info>

<script setup lang="ts">
import { ref } from 'vue-demi';

import CheckBox from '../index.vue';
const input1 = ref(true);
</script>

<template>
    <div class="demo">
        <CheckBox v-model="input1" type="flat"> 我同意<a href="#">《快手打款协议》</a> </CheckBox>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
.demo {
    padding: 10px;
}

.label {
    display: inline-flex;
    align-items: center;
    height: 18px;
    a {
        color: #385080;
    }
}
.label-text {
    margin-left: 4px;
    font-size: 13px;
    font-weight: 400;
    font-size: 13px;
    text-align: center;
    color: #10011080;
}
</style>
