import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

import { POPUP_ACTION, type LogTransformFn } from './type';
import { CityModalTypes } from '../popupTransform/cityFullModalTransform';
import { isPopup, type SummerPopup } from '../popupTransform/types';

export interface CityPopupLogValue {
    encourage_type: string;
    button_name?: string;
    prize_name?: string;
}

export const cityPopupLogTransform: LogTransformFn<CityPopupLogValue> = (popupData: SummerPopup) => {
    if (isPopup(popupData, [...CityModalTypes, PopupType.LS_CITY_PAGE_BLESS])) {
        return {
            show: POPUP_ACTION.CITY_REWARD_POPUP,
            click: POPUP_ACTION.CITY_REWARD_POPUP,
            close: POPUP_ACTION.CITY_REWARD_POPUP,
            logValue: {
                encourage_type: popupData.popupType,
                // todo 奖品id字段确认
                // coupon_id: '',
                button_name: popupData.mainButton?.linkText ?? '',
                prize_name: popupData.subTitle ?? popupData.title ?? '',
            },
        };
    }
};
