<script lang="ts">
import { px2rem } from '@pet/core.mobile';
import { computed, defineComponent, h, inject } from 'vue-demi';
import type { PropType } from 'vue-demi';

import { BoxInfoProvider } from './types';

const COMPONENT_ITEM_NAME = 'g-box-item-flex';
const COMPONENT_ITEM_NAME_CLASSIC = 'g-box-item-inline-block';

export default defineComponent({
    name: 'BaseBoxItem',
    props: {
        /**
         * 渲染的html tag
         */
        tag: {
            type: String as PropType<keyof HTMLElementTagNameMap>,
            default: 'div',
            required: false,
        },
        /**
         * 渲染的flex order
         */
        order: {
            type: Number,
            required: false,
        },
        /**
         * flex-basis
         */
        basis: {
            type: [Number, String],
            required: false,
        },
        /**
         * flex-grow
         */
        grow: {
            type: Number,
            required: false,
        },
        /**
         * flex-shrink
         */
        shrink: {
            type: Number,
            required: false,
        },
        /**
         * align-self
         */
        align: {
            type: String,
            required: false,
        },
    },
    setup(props, { slots }) {
        const gapsInfo = inject(BoxInfoProvider);
        const boxItemStyles = computed(() => {
            const gapRowType = gapsInfo?.value.direction.includes('row') === true ? 'padding-top' : 'padding-left';
            const gapColType = gapsInfo?.value.direction.includes('row') === true ? 'padding-left' : 'padding-top';
            const flexStyles = {
                order: props.order,
                flexGrow: props.grow,
                flexShrink: props.shrink,
                flexBasis: props.basis,
                alignSelf: props.align,
            };
            const normalStyles = {
                width: props.basis,
            };
            const setStyle = gapsInfo?.value.classic === true ? normalStyles : flexStyles;
            return {
                [gapRowType]: gapsInfo?.value.row != null ? px2rem(gapsInfo.value.row) : 0,
                [gapColType]: gapsInfo?.value.col != null ? px2rem(gapsInfo.value.col) : 0,
                ...setStyle,
            };
        });
        const boxItemClasses = computed(() =>
            gapsInfo?.value.classic === true ? COMPONENT_ITEM_NAME_CLASSIC : COMPONENT_ITEM_NAME,
        );
        return () =>
            h(
                props.tag,
                {
                    class: boxItemClasses.value,
                    style: boxItemStyles.value,
                },
                slots.default?.(),
            );
    },
});
</script>

<style lang="scss" scoped>
.g-box-item-flex {
    flex: 1 1 0%;
    align-self: stretch;
    box-sizing: border-box;
}
.g-box-item-inline-block {
    display: inline-block;
    box-sizing: border-box;
    vertical-align: top;
}
</style>
