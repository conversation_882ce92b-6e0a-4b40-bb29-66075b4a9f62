import { createModel, useModel } from '@gundam/model';
import { computed, ref } from 'vue';

import { PopupType } from '@/services/open-api-docs/home/<USER>/schemas';

import { useHomeModel } from './homeModel';
import { useLocalCalendarModel } from './localCalendarModel';
import { usePopupModel } from './popup.model';
import { useTaskModel } from './taskModel';

export const abTestModel = createModel(() => {
    const { openPopup } = usePopupModel();
    const { homeData, abTestConfigView } = useHomeModel();
    const { isChecked, clickedFlag } = useLocalCalendarModel();
    const { inpushTaskInfo } = useTaskModel();

    // 次日奖励气泡信息
    const experimentBubbleInfo = computed(() => abTestConfigView.value?.doubledDetailView);
    const isLoin = computed(() => experimentBubbleInfo.value?.displayOriginalLlrewdUnit === '金币');
    const exp0714MainButtonTitle = computed(() => experimentBubbleInfo.value?.mainButtonTitle ?? '');
    const exp0714MainButtonSubTitle = computed(() => experimentBubbleInfo.value?.mainButtonSubTitle ?? '');

    // 是否有新手引导弹窗
    const hasBeginnerGuide = computed(() => homeData.value?.popList?.[0]?.popupType === PopupType.BEGINNER_GUIDE);
    // 是否完成了首次选品
    const hasFirstSelectedProduct = ref(false);

    const isCalendarTask = computed(() => !isChecked.value);
    const isInpushTask = computed(
        () =>
            !isCalendarTask.value &&
            !clickedFlag.value &&
            !!inpushTaskInfo.value?.taskInfo &&
            !inpushTaskInfo.value?.finished,
    );

    /**
     * 命中实验3或4的情况下，需要
     * @param data
     * @returns
     */
    async function showFirstDayExperimentPop(data: Array<any>) {
        const task = openPopup({
            component: () => import('@/components/popups/flip-popup/FlipPopup.vue'),
            data: {
                ...(data[0] ?? {}),
                prizeDetail:
                    data[0]?.llpeDetail?.map((item: any) => ({
                        ...item,
                        amount: item.displayAmount,
                        prizeType: item.llpeType,
                        unit: item.displayUnit,
                        descIcon: item.icon,
                    })) ?? [],
                bottomInfo: {
                    bottomDesc: data[0]?.bottomButton?.linkSubText ?? '',
                    bottomButton: data[0]?.bottomButton,
                },
                backPop: data[1],
            },
        });
        return task.end;
    }

    return {
        showFirstDayExperimentPop,
        experimentBubbleInfo,
        isLoin,
        hasBeginnerGuide,
        hasFirstSelectedProduct,
        exp0714MainButtonSubTitle,
        exp0714MainButtonTitle,
        isCalendarTask,
        isInpushTask,
    };
});

export const useABTestModel = () => useModel(abTestModel);
