<script lang="ts">
export default {
    name: 'AdaptFrame',
};
</script>
<script lang="ts" setup>
import { computed, useSlots, ref } from 'vue-demi';
import { useBarInfo, isAtSearchTab, isAtFourTab } from '@pet/yau.core/device';
import { px2rem } from '@pet/core.mobile';
import { useDomListener } from '@pet/yau.core';

interface Props {
    /**
     * 是否为单屏应用
     */
    fullScreen?: boolean;
    /**
     * 容器内是否有status-bar组件
     */
    hasStatusBar?: boolean;
    /**
     * 底部的 fixedFooter 是否自己处理安全区
     */
    selfCtrlBottom?: boolean;
    /**
     * atSearchTab
     */
    atSearchTab?: boolean;
    /**
     * 背景图浮层切图
     */
    backGroundImage?: string;
    /**
     * 背景图浮层切图降级渐变色
     */
    gradientColor?: string;
    /**
     * 顶导纯色填充
     */
    barFillColor?: string;
    /**
     * 4tab填充色
     */
    tabOverLayBackground?: string;
    /**
     * for ssg
     */
    inHeadless?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    fullScreen: true,
    hasStatusBar: true,
    selfCtrlBottom: false,
    atSearchTab: false,
    backGroundImage: '',
    gradientColor: '',
    inHeadless: false,
});

const { statusBarHeight, toolBarHeight } = useBarInfo();
const wrapperStyle = computed(() => props.fullScreen && 'is-full-screen');
const mayAtSearchTab = isAtSearchTab();
const atAtFourTab = isAtFourTab();

const webviewType = computed(() => {
    if (atAtFourTab) {
        return 'is-at-four-tab';
    } else if (mayAtSearchTab) {
        return 'is-at-search-tab';
    } else {
        return 'is-at-normal-tab';
    }
});

const overLayBackGround = computed(() =>
    props.backGroundImage ? `url(${props.backGroundImage}) no-repeat` : props.gradientColor || 'none',
);

const computedStatusBarHeight = computed(() => (props.atSearchTab || mayAtSearchTab ? 0 : statusBarHeight));

const fillTop = computed(() => {
    return !props.hasStatusBar ? { paddingTop: `${computedStatusBarHeight.value}px` } : {};
});

const slots = useSlots();

const fillBottom = computed(() => {
    if (slots.fixedFooter || props.selfCtrlBottom) {
        return {
            paddingBottom: `${toolBarHeight}px`,
        };
    }
    return {
        paddingBottom: `calc(${toolBarHeight}px + env(safe-area-inset-bottom))`,
    };
});

// 设计给的以顶导44dp为标准的设计稿
const DESIGN_STATUSBAR_HEIGHT = 44;
const BACKGROUND_TOP_OFFSET_HEIGHT = atAtFourTab ? 40 : 30;

const innerGap = px2rem(DESIGN_STATUSBAR_HEIGHT - BACKGROUND_TOP_OFFSET_HEIGHT);
const innerIconWidgetGap = px2rem(DESIGN_STATUSBAR_HEIGHT - BACKGROUND_TOP_OFFSET_HEIGHT + 3);
const atSpecialTab = computed(() => atAtFourTab || mayAtSearchTab);
const specialTabOffset = computed(() =>
    atSpecialTab.value ? `${DESIGN_STATUSBAR_HEIGHT}px - ${px2rem(BACKGROUND_TOP_OFFSET_HEIGHT)}` : '0px',
);
// 设置的页面开始位置与系统栏的间距
const setTopGap = computed(() => {
    return atSpecialTab.value
        ? {
              '--adapt-status-bar-main-title-top-gap': px2rem(DESIGN_STATUSBAR_HEIGHT - BACKGROUND_TOP_OFFSET_HEIGHT),
          }
        : {};
});
/**
 * calc(${DESIGN_STATUSBAR_HEIGHT}px - ${px2rem(DESIGN_STATUSBAR_HEIGHT)} 背景图是基于缩放适配，但是顶导是绝对像素，这里做修正适配
 * ${computedStatusBarHeight.value - DESIGN_STATUSBAR_HEIGHT}px 实际顶导的高度于设计稿的导航栏差
 * specialTabOffset.value 如果是在4tab或者搜索tab，因为主内容距离native导航栏间距为14px, 所以上移了30px，这里修正
 */
const topOffest = computed(
    () =>
        `calc(${DESIGN_STATUSBAR_HEIGHT}px - ${px2rem(DESIGN_STATUSBAR_HEIGHT)} + ${computedStatusBarHeight.value - DESIGN_STATUSBAR_HEIGHT}px + ${specialTabOffset.value})`,
);
const backgroundInfo = computed(() => {
    return {
        '--adapt-frame-background-position': `0 ${topOffest.value}`,
        '--adapt-frame-background-fill-size': `${computedStatusBarHeight.value}px`,
    };
});

const barFillBackgroundColor = computed(() => {
    return props.barFillColor
        ? {
              '--adapt-frame-bar-fill-color': `linear-gradient(${props.barFillColor}, ${props.barFillColor})`,
          }
        : {};
});

const overlayStyle = computed(() => {
    return {
        background: overLayBackGround.value,
        backgroundSize: '100% 100%',
        top: topOffest.value,
    };
});

const tabBarOverlayStyle = computed(() => {
    return {
        height: `${computedStatusBarHeight.value}px`,
    };
});

const titleGapSync = computed(() => !props.inHeadless && 'page-rendered');

const showBar = ref(false);
function getScrollTop() {
    return window.scrollY || document.documentElement.scrollTop;
}
function handleScroll() {
    showBar.value = getScrollTop() > 1;
}

useDomListener(() => window, 'scroll', handleScroll, {
    passive: true,
});
</script>
<template>
    <div
        class="app-wrapper"
        :class="[wrapperStyle, webviewType, titleGapSync]"
        :style="[fillTop, fillBottom, barFillBackgroundColor, setTopGap, !props.inHeadless && backgroundInfo]"
    >
        <div v-if="atAtFourTab" v-show="showBar" class="tab-bar-overlay" :style="tabBarOverlayStyle"></div>
        <div :class="{ 'app-wrapper-overlay': overLayBackGround }" :style="overlayStyle"></div>
        <slot />
        <footer v-if="$slots.fixedFooter" class="app-footer">
            <slot name="fixedFooter" />
        </footer>
    </div>
</template>
<style>
:root {
    /* 容器宽度 */
    --adapt-frame-width: 414px;
    /* 容器下边距 */
    --adapt-frame-padding-bottom: env(safe-area-inset-bottom);
    /* 底导下边距 */
    --adapt-frame-app-footer-padding-bottom: env(safe-area-inset-bottom);
    /* 遮罩高度 */
    --adapt-frame-overlay-height: 229px;
}
</style>
<style lang="scss" scoped>
.app-wrapper {
    width: var(--adapt-frame-width);
    min-height: 100%;
    padding-bottom: var(--adapt-frame-padding-bottom);
    background-image: var(--adapt-frame-background-image, none), var(--adapt-frame-bar-fill-color, none);
    background-size:
        100% auto,
        100% var(--adapt-frame-background-fill-size);
    background-position:
        var(--adapt-frame-background-position),
        0 0;
    background-repeat: no-repeat, no-repeat;
    &.is-full-screen {
        box-sizing: border-box;
        height: 100vh;
        overflow: hidden;
    }

    .tab-bar-overlay {
        width: inherit;
        left: 0;
        right: 0;
        margin: auto;
        position: fixed;
        z-index: 200;
        background: v-bind(tabOverLayBackground);
        /* 测试4tab滑动后下面内容闪的问题 */
        transform: translateZ(0);
    }

    .app-wrapper-overlay {
        position: absolute;
        height: var(--adapt-frame-overlay-height);
        width: inherit;
        pointer-events: none;
    }
    .app-footer {
        position: fixed;
        z-index: 997;
        width: inherit;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        padding-bottom: inherit;
        > :deep(div),
        > :deep(section) {
            padding-bottom: var(--adapt-frame-app-footer-padding-bottom);
        }
    }
}

.page-rendered {
    /* 这里为了同步status-bar里面的间距，防止漏改 */
    :deep(.global-at-four-tab),
    :deep(.global-at-search-tab) {
        --adapt-status-bar-main-title-top-gap: v-bind(innerGap);
        --adapt-status-bar-expand-vertical-gap: v-bind(innerIconWidgetGap);
    }
}
</style>
