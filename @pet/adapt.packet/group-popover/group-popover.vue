<script setup lang="ts">
import Popover from '@pet/adapt.popover/index.vue';
import Avatar from '@pet/adapt.avatar/index.vue';
import type { UserInfo } from '../types';
import { ref, onBeforeMount } from 'vue-demi';
import { transViewValue } from '@pet/core.mobile';

const props = defineProps<{
    show: boolean;
    info: {
        totalCount?: number;
        userList: UserInfo[];
    };
}>();

const emits = defineEmits<{
    (event: 'update:show', show: boolean): void;
}>();

const popoverLeft = ref(0);
const popoverTop = ref(0);

onBeforeMount(() => {
    const targetElement = document.getElementById('group_check');
    const targetRect = targetElement?.getBoundingClientRect();
    popoverLeft.value = targetRect?.left ?? 0;
    popoverTop.value = targetRect?.top ?? 0;
});
</script>

<template>
    <Popover
        v-if="info.totalCount"
        :show="show"
        class="popover"
        :arr-distance="112"
        :style="{ left: `${transViewValue(popoverLeft) + 25}px`, top: `${transViewValue(popoverTop) + 26}px` }"
    >
        <div class="wrapper">
            <div class="back-color"></div>
            <div class="title">
                已邀请{{ info.userList.length }}位好友<span class="close" @click="emits('update:show', false)"></span>
            </div>
            <div
                :class="[
                    'content',
                    info.userList.length > 4 && 'flex-start',
                    info.userList.length > 8 && 'scroll-type',
                ]"
            >
                <div
                    v-for="(item, index) in info.userList"
                    :key="index"
                    :class="['item', (index + 1) % 4 !== 0 && 'gap-6']"
                >
                    <Avatar :width="38" :src="item.avatar" />
                    <div class="name">{{ item.name }}</div>
                </div>
            </div>
        </div>
    </Popover>
</template>

<style lang="scss" scoped>
@import '../common.scss';
.popover {
    position: fixed;
    transform: translateX(-50%);
    --adapt-tool-tip-inner-padding: 0;
    --adapt-tool-tip-radius: 36px;

    > .wrapper {
        position: relative;
        box-sizing: border-box;
        width: 236px;
        max-height: 221px;
        border-radius: 36px;
        padding: 20px;
        overflow: hidden;

        .back-color {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(131.09deg, rgba(198, 255, 230, 0.39) 13.92%, rgba(255, 255, 203, 0) 64.88%);
        }

        .title {
            text-align: center;
            position: relative;
            width: 100%;
            font-weight: bold;
            font-size: 15px;
            color: #222222;
            margin-bottom: 12px;

            .close {
                display: inline-block;
                position: absolute;
                right: 0;
                top: 3px;
                width: 16px;
                height: 16px;
                background: url('../../assets/close-icon.png') no-repeat center/100%;
            }
        }

        .content {
            position: relative;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            width: 100%;
            max-height: 148px;
            overflow: auto;
            -webkit-overflow-scrolling: touch;
            &::-webkit-scrollbar {
                display: none;
            }

            .item {
                margin-bottom: 8px;
                width: 44px;

                .name {
                    @include ellipse;
                    margin-top: 5px;
                    color: #9c9c9c;
                    font-size: 11px;
                }
            }

            .gap-6 {
                margin-right: 6px;
            }
        }

        .scroll-type::after {
            content: '';
            width: 216px;
            position: fixed;
            left: 10px;
            right: 0;
            pointer-events: none; /* 使伪元素不影响鼠标事件 */
            bottom: 14px;
            height: 30px; /* 设置渐变区域的高度 */
            background: linear-gradient(to bottom, transparent 0%, white 100%);
        }

        .flex-start {
            justify-content: flex-start;
        }
    }
}
</style>
