<pet-info lang="json">
{ "title": "基础效果", "description": "" }
</pet-info>
<script setup lang="ts">
import Badge from '../base.vue';
</script>

<template>
    <div>
        <div class="badge-dot-box">
            <Badge type="dot" class="badge-dot" />
        </div>
        <div class="badge-dot-box">
            <Badge type="round" :value="9" class="badge-item" />
            <Badge type="round" :value="99" class="badge-item" />
            <Badge type="round" :value="200" class="badge-item" :max="99" />
            <Badge type="round" value="徽标文案" class="badge-item" />
        </div>
        <div class="badge-dot-box">
            <Badge type="sharp" value="徽" class="badge-item" />
            <Badge type="sharp" value="徽标" class="badge-item" />
            <Badge type="sharp" value="此处为徽标文案" class="badge-item" />
        </div>
        <div class="badge-dot-box">
            <Badge type="round" :value="9" class="badge-item" large />
            <Badge type="round" :value="99" class="badge-item" large />
            <Badge type="round" :value="200" class="badge-item" :max="99" large />
            <Badge type="round" value="徽标文案" class="badge-item" large />
        </div>
        <div class="badge-dot-box">
            <Badge type="sharp" value="徽" class="badge-item" large />
            <Badge type="sharp" value="徽标" class="badge-item" large />
            <Badge type="sharp" value="此处为徽标文案" class="badge-item" large />
        </div>
        <div class="badge-dot-box">
            <Badge type="dark" :value="9" class="badge-item" />
            <Badge type="dark" :value="99" class="badge-item" />
            <Badge type="dark" :value="200" :max="99" class="badge-item" />
            <Badge type="dark" value="徽标文案" class="badge-item" />
        </div>
        <div class="badge-dot-box">
            <Badge type="dark" :value="9" class="badge-item" large />
            <Badge type="dark" :value="99" class="badge-item" large />
            <Badge type="dark" :value="200" :max="99" class="badge-item" large />
            <Badge type="dark" value="徽标文案" class="badge-item" large />
        </div>
    </div>
</template>
<style src="@pet/adapt.reset/reset.css"></style>
<style lang="scss" scoped>
.badge-dot-box {
    background: #f8f8f8;
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.badge-item {
    margin: 0 4px;
}
</style>
