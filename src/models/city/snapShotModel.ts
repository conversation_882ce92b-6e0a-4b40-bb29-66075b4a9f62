import { createUseModel } from '@gundam/model';
import { whenever } from '@vueuse/core';

// todo 切换新homeModel
import { useCityHomeModel } from './homeModel';

/**
 * 存放一些需要手动更新的数据
 */
export const useSnapShotModel = createUseModel(({ getModelInstance }) => {
    const { homeData, chessboard, popList } = useCityHomeModel();
    /** 是否已经完成一圈跳格子 */
    const signed = ref(false);
    /** 当前第几步，0有实际意义用 */
    const currentStep = ref(-1);
    /** 目标站点 */
    const expectTotalStep = ref(0);
    // 账户钱包信息
    const accountModel = ref();
    /** 是否最后一个格子 */
    // const isLastStation = ref(false);

    whenever(
        chessboard,
        (val) => {
            signed.value = chessboard.value?.progress?.signed ?? false;
            currentStep.value = chessboard.value?.progress?.currentStep ?? -1;
            expectTotalStep.value = chessboard.value?.progress?.expectTotalStep ?? 0;
            accountModel.value = homeData.value?.accountModel ?? undefined;
        },
        { immediate: true, deep: true },
    );

    return {
        signed,
        currentStep,
        expectTotalStep,
        accountModel,
    };
});
