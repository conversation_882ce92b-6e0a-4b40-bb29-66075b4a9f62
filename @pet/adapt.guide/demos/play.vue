<pet-info lang="json">
{ "title": "播放控制", "description": "控制动效是否播放" }
</pet-info>
<script lang="ts" setup>
import Guide from '../index.vue';
</script>

<template>
    <div class="frame-box">
        <div class="frame-text">不展示播放效果</div>
        <Guide :play="false" class="guide-hand-position" />
    </div>
</template>

<style lang="css" scoped>
.frame-box {
    position: relative;
    width: 200px;
    height: 200px;
    background: rgba(0, 0, 0, 0.5);
    padding: 45px 0 0 10px;
    box-sizing: border-box;
    color: #fff;
    font-size: 12px;
}

.frame-text {
    position: absolute;
    top: 30px;
    left: 10px;
}

.guide-hand-position {
    left: 50px;
    top: 50px;
}
</style>
