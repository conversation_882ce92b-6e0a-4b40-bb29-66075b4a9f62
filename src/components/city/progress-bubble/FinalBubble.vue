<script setup lang="ts">
import AdaptTransition from '@pet/adapt.transition/index.vue';

import { useBubbleModel } from '@/models/city/bubbleModel';
import { useConfigModel } from '@/models/city/configModel';

const { showBubbleType } = useBubbleModel();
const { kconfConfig, defaultKconfConfig } = useConfigModel();

const finalBubbleText = computed(() => kconfConfig.value?.finalBubble ?? defaultKconfConfig?.finalBubble);
</script>

<template>
    <AdaptTransition name="scale-in-out-debounce" :duration="{ enter: 500, leave: 200 }">
        <div v-show="showBubbleType === 'final'" class="final_bubble">
            <p v-for="item in finalBubbleText" :key="item">{{ item }}</p>
        </div>
    </AdaptTransition>
</template>

<style scoped lang="scss">
.final_bubble {
    width: 204px;
    height: 96px;
    @include bg('./assets/bg_bubble.png');
    background-size: 100% 100%;
    transform-origin: bottom center;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    p {
        color: #000000;
        font-size: 18px;
        font-weight: 600;
        font-family: PingFang SC;
        letter-spacing: 0;
        line-height: 25.2px;
        text-align: center;
        vertical-align: top;
        margin: 0;

        transform: translateY(-5px);
    }
}
</style>
