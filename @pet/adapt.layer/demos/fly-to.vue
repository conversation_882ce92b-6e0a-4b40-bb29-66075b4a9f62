<pet-info lang="json">
{ "title": "飞入效果", "description": "" }
</pet-info>
<script lang="ts" setup>
import Button from '@pet/adapt.button/index.vue';
import { ref } from 'vue-demi';

import Layer from '../index.vue';

const show = ref(false);

const layerRef = ref<InstanceType<typeof Layer> | null>(null);

function go() {
    show.value = false;
}
</script>

<template>
    <div>
        <Layer
            ref="layerRef"
            v-model:show="show"
            has-logo
            custom-logo="https://w1-pro.kskwai.com/kos/nlav11066/2022_common_static/business/modal_logo_shanzhashuxia_v1.png"
            title="此处为主标题"
            sub-title="此处为副标题，可用于辅助信息"
            cover="https://static.yximgs.com/udata/pkg/ks-ad-fe/chrome-plugin-upload/2021-12-16/1639634522781.3e4249c181e76b81.png"
            main-btn-text="立即使用"
            fly-to-target="target"
            :auto-fly="true"
            @confirm="go"
        >
            <template #buttonBottom>
                <div class="bottom-fonts">按钮下方文案</div>
            </template>
        </Layer>
        <div class="target-wrap">
            <div id="target" class="target">目标元素</div>
        </div>
        <Button class="item-btn" @click="show = true">打开蒙层</Button>
    </div>
</template>

<style scoped>
.item-btn {
    margin: 0 2px;
}

.target-wrap {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.target {
    display: inline-flex;
    width: 40px;
    height: 40px;
    background: #f00;
    color: #fff;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    text-align: center;
    position: absolute;
    bottom: 100px;
}

.bottom-fonts {
    font-size: 16px;
    color: #fff;
    text-align: center;
}
</style>
