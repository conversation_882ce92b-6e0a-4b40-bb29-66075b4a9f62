<script setup lang="ts">
import Avatar from '@pet/adapt.avatar/index.vue';
import type { InsertUserInfo } from '@pet/quantum.share/extendNormalShare';
import { computed, ref } from 'vue';

import CommonModal from '@/components/common-modals/CommonModal.vue';
import { useLogger } from '@/init/logger';
import { useTeamActionModel } from '@/models/team/teamAction.model';
import { useTeamDataModel } from '@/models/team/teamData.model';
import { PopupType, type FriendCardView } from '@/services/open-api-docs/home/<USER>/schemas';
import type { ClickPosition } from '@/utils/popupTransform/types';

import CircularSwiper from './CircularSwiper.vue';

interface Props {
    friendCards: FriendCardView[];
    popupType?: PopupType;
    show?: boolean;
    showClose?: boolean;
    sponsorLogo?: string;
    title?: string;
    subTitle?: string;
    mainButtonText?: string;
}
const { sendClick } = useLogger();
const { teamUser: teamMembers, teamTotalMemberNum: totalTeamAmount } = useTeamDataModel();
const { shareCardInvite } = useTeamActionModel();
const props = withDefaults(defineProps<Props>(), {
    showClose: true,
});

// 当前选中的卡片信息和按顺序排列的所有卡片
const currentCard = ref<FriendCardView | null>(null);
const allCardsInOrder = ref<FriendCardView[]>([]);

// 处理当前卡片变化
const handleCurrentCardChange = (data: { currentCard: FriendCardView | null; allCardsInOrder: FriendCardView[] }) => {
    currentCard.value = data.currentCard;
    allCardsInOrder.value = data.allCardsInOrder;
};

// 计算显示的头像列表（包含空头像）
const displayAvatars = computed(() => {
    const members = teamMembers.value;
    const total = totalTeamAmount.value || 3;
    const avatars = [];
    // 添加真实的队友头像
    members.forEach((member) => {
        avatars.push({
            type: 'member',
            userId: member.userId,
            userAvatar: member.userAvatar,
            nickName: member.nickName,
        });
    });

    // 添加空头像填充剩余位置
    const remainingSlots = total - members.length;
    for (let i = 0; i < remainingSlots; i++) {
        avatars.push({
            type: 'empty',
            userId: `empty-${i}`,
            userAvatar: '',
            nickName: '',
        });
    }

    return avatars;
});

const emits = defineEmits<{
    (event: 'close'): void;
    (event: 'end'): void;
}>();

// 处理弹窗关闭
const handleClose = () => {
    emits('close');
};

// 处理弹窗动画结束
const handleEnd = () => {
    // 埋点上报：关闭按钮点击
    sendClick('OP_ACTIVITY_MAKE_TEAM_CARD', {
        friend_id: currentCard.value?.userId || '',
        click_area: 'close',
    });
    emits('end');
};
const mainBtnClick = (params: { position: ClickPosition; destroy: () => void }) => {
    if (params.position === 'mainClick') {
        sendClick('OP_ACTIVITY_MAKE_TEAM_CARD', {
            friend_id: currentCard.value?.userId || '',
            click_area: 'invite',
        });
        // 使用从子组件传来的按当前顺序排列的卡片数据
        if (allCardsInOrder.value.length > 0) {
            // 构造所有用户信息，按当前展示的顺序排列
            const userInfoList: InsertUserInfo[] = [];
            // 直接使用已经按正确顺序排列的卡片数据
            allCardsInOrder.value.forEach((card) => {
                if (card.avatar && card.nickName && card.userId) {
                    userInfoList.push({
                        user_id: card.userId,
                        user_name: card.nickName,
                        headurl: card.avatar,
                    });
                }
            });

            // 调用分享组队卡片邀请
            if (userInfoList.length > 0) {
                shareCardInvite(userInfoList);
                console.log('分享组队卡片邀请，用户列表:', userInfoList);
                console.log(`当前展示卡片: ${currentCard.value?.nickName ?? ''}，总共${userInfoList.length}个用户`);
                console.log(
                    '卡片顺序:',
                    allCardsInOrder.value.map((card) => card.nickName),
                );
            } else {
                console.log('没有有效的用户信息可以分享');
            }
        } else {
            console.log('没有可用的卡片信息');
        }
    }
};
</script>

<template>
    <div class="friends-team-popup">
        <CommonModal
            fly-to-target="team-widget"
            type="layer"
            :show="show"
            :title="title"
            :sub-title="subTitle"
            :show-close="showClose"
            :hide-mask="false"
            :main-button="mainButtonText"
            :sponsor-logo="sponsorLogo"
            light-type="none"
            :btn-click="mainBtnClick"
            @close="handleClose"
            @end="handleEnd"
        >
            <template #default>
                <div class="friends-team-popup-content">
                    <CircularSwiper :friend-cards="friendCards" @current-card-change="handleCurrentCardChange" />
                </div>
            </template>
            <template #buttonBottom>
                <div v-if="teamMembers.length > 0" class="friends-team-members">
                    <div class="team-title">
                        <span class="team-title-icon team-title-icon-left"></span>
                        <span class="team-title-text">我的队伍{{ teamMembers.length }}/{{ totalTeamAmount || 3 }}</span>
                        <span class="team-title-icon team-title-icon-right"></span>
                    </div>
                    <div class="team-avatars">
                        <Avatar
                            v-for="avatar in displayAvatars"
                            :key="avatar.userId"
                            :src="avatar.userAvatar"
                            :width="38"
                            :class="['team-avatar', { 'empty-avatar': avatar.type === 'empty' }]"
                        />
                    </div>
                </div>
            </template>
        </CommonModal>
    </div>
</template>

<style lang="scss" scoped>
.friends-team-popup {
    --adapt-layer-main-margin-bottom: 23px;
    --adapt-layer-button-margin-bottom: 20px;

    :deep(.popup-addons) {
        &.addons-top {
            top: 16px;
        }
    }
    :deep(.layer .layer-ctrl .layer-buttons .main-btn) {
        --adapt-button-primary-font-linear: #fff;
        --adapt-button-primary-background-image: linear-gradient(
            100deg,
            #ff7001 -1.65%,
            #fc2d39 47.88%,
            #f31906 98.68%
        );
        --adapt-button-font-size: 20px;
        --adapt-button-line-height: normal;
        --adapt-button-plain-border-width: 1.6px;
        --adapt-button-height: 66px;
        --adapt-button-width: 230px;
    }
}
.friends-team-popup-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.friends-team-members {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    .team-title {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 13px;
        &-icon {
            width: 30px;
            height: 2px;
            background: url('./assets/icon-line.png') center no-repeat;
            &-left {
                margin-right: 9px;
            }
            &-right {
                margin-left: 9px;
                transform: rotate(180deg);
            }
        }
    }
    .team-avatars {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 14px;
        .team-avatar {
            margin: 0 10px;

            &.empty-avatar {
                background: url('./assets/empty-avatar.png') center no-repeat;
                background-size: 38px 38px;
            }
        }
    }
}
</style>
