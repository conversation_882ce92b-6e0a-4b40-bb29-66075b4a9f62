<pet-info lang="json">
{ "title": "通知卡片", "description": "" }
</pet-info>
<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
import { ref, nextTick } from 'vue-demi';

import NoticeCard from '../index.vue';

const show = ref(false);
const createNotice = ref(false);

function noticeLeave() {
    createNotice.value = false;
}

const eventNotice = {
    avatar: [],
    figure: 'https://s3.kskwai.com/udata/pkg/nebula/fission/static-picture-manual-upload/male-avatar.cb4e7b5edc82c7a6.png',
    title: '摇一摇PK赢红包',
    desc: '和附近朋友一起摇一摇赢红包',
    buttonText: '去赢钱',
    attachedText: '',
    activeTitle: '',
};

const userNotice = {
    avatar: [
        'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
        'https://p2.a.yximgs.com/uhead/AB/2020/09/03/12/BMjAyMDA5MDMxMjUyMzFfOTAwNDFfMl9oZDI1OF83NzE=_s.jpg',
    ],
    figure: '',
    title: '已领取<b>5元</b>优惠券',
    desc: '为你助力成功快去看看吧',
    attachedText: '刚刚',
    buttonText: '',
    activeTitle: '5元',
};

const noticeInfo = ref(userNotice);

let i = 0;

function handleClick() {
    i++;
    noticeInfo.value = i % 2 === 1 ? userNotice : eventNotice;
    createNotice.value = true;
    nextTick(() => {
        show.value = true;
    });
}
</script>

<template>
    <div>
        <NoticeCard
            v-if="createNotice"
            v-model:show="show"
            :avatar="noticeInfo.avatar"
            :figure="noticeInfo.figure"
            :title="noticeInfo.title"
            :desc="noticeInfo.desc"
            :button-text="noticeInfo.buttonText"
            :attached-text="noticeInfo.attachedText"
            @after-leave="noticeLeave"
        >
        </NoticeCard>
        <Button class="button" @click="handleClick">发送通知</Button>
    </div>
</template>

<style lang="scss">
@import '@pet/adapt.reset/reset.css';
div {
    --adapt-card-radius: 28px;
    --adapt-card-title-color: black;
    --adapt-card-desc-color: black;
    --adapt-card-inner-gap: 12px 16px;
    --adapt-card-ctrl-color: #ccc;
}
</style>
