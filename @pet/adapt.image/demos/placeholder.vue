<script setup lang="ts">
import { ref, computed } from 'vue-demi';

import XImage from '../index.vue';

const imgSrc = ref(
    'https://svs.gsfc.nasa.gov/vis/a030000/a030800/a030877/frames/5760x3240_16x9_01p/BlackMarble_2016_928m_india_labeled.png',
);

const holderImage = 'https://miro.medium.com/max/720/1*W35QUSvGpcLuxPo3SRTH4w.png';
</script>

<template>
    <div class="demo">
        <h5>:placeholder="yourPlaceHolderImage"</h5>
        <div class="container">
            <XImage :src="imgSrc" class="image-item" :placeholder="holderImage" object-fit="cover" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    text-align: center;
}
.image-item {
    width: 160px;
    height: 160px;
}
div {
    font-size: 16px;
}
</style>
