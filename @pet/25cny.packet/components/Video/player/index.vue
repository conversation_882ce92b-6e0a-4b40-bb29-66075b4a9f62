<script setup lang="ts">
import { KPlayer, LogLevel, LogType, PlayerType, STATES } from '@ks-kplayer/vue';
import '@ks-kplayer/vue/style.css';
import { computed, onBeforeUnmount, onUnmounted, ref, shallowRef, watch } from 'vue';
import { useIntersectionObserver, type UseIntersectionObserverReturn } from '@vueuse/core';
import { radar } from '@gundam/weblogger';
import { usePlayerModel } from './playerModel';
import { popupMuted, PopupMutedDefault } from '../muted';
import useCaptureDebugLog from '@pet/yau.logger';
import CdnImg from '@pet/25cny.cdn-image/index.vue';

export interface PlayerProps {
    /** 视频类型 */
    type: PlayerType;
    /** 视频源 */
    source: string | Record<string, any>;
    /** fallbackSource */
    fallbackSource?: string;
    /** 视频封面 */
    poster?: string;
    /** 视频降级封面图 */
    fallbackPoster?: string;
    /** 视频暂停 */
    paused?: boolean;
    /** 是否静音 */
    muted?: boolean;
    /** 可见状态变化自动起播暂停 */
    observe?: boolean;
    /** 可见阈值 */
    observeThreshold?: number;
    /** 自动播放 */
    autoPlay?: boolean;
    /** 降级 */
    downgrade?: boolean;
    /** 画面填充方式 */
    objectFit?: 'contain' | 'cover';
    /** 用于标识上报的信息 */
    activityTag?: string;
}

const props = withDefaults(defineProps<PlayerProps>(), {
    muted: true,
    autoPlay: true,
    paused: false,
    observe: false,
    observeThreshold: 0,
    downgrade: false,
    objectFit: 'cover',
});

const emit = defineEmits<{
    (e: 'playing'): void;
    (e: 'ended'): void;
    (e: 'error', err: Event | ErrorEvent): void;
    (e: 'debug-log', payload: any): void;
}>();

const { log, error } = useCaptureDebugLog(`Player-${props.activityTag || ''}`);

const playerWrapper = ref<HTMLDivElement | null>(null);
const player = shallowRef<InstanceType<typeof KPlayer> | null>(null);
const { pushPlayer, popPlayer } = usePlayerModel();
const isMountPlayer = pushPlayer();

const onPlaying = () => {
    log('真正开始播放');
    emit('playing');
};

const onEnded = () => {
    log('播放完成');
    emit('ended');
};

const onError = (e: Event | ErrorEvent) => {
    error('播放错误｜直播关播', JSON.stringify(e));
    emit('error', e);
};

const onDebugLog = (payload: any) => {
    try {
        log('[Player] debug-log', JSON.stringify(payload));
    } catch (e) {
        error('[Player] debug-log stringity error', e);
    }
    emit('debug-log', payload);
};

const play = () => {
    log('播放', player.value?.videoInstance);
    player.value?.videoInstance?.play();
};

const pause = () => {
    log('暂停', player.value?.videoInstance);
    player.value?.videoInstance?.pause();
};

// 这里对 src 为空字符串做下特殊处理
// 如果直接将空字符串赋值给播放器，则播放器不会有变化
// 所以这里将播放器暂停并抛出一个错误
watch(
    () => props.source,
    (src) => {
        if (!Boolean(src) || (typeof src === 'object' && Object.keys(props.source).length === 0)) {
            pause();
            log('source 解析失败', src);
            onError(new ErrorEvent('source 解析失败'));
        }
    },
    { immediate: true },
);

watch(
    () => props.muted,
    (muted) => {
        log('静音状态改变', muted);
        if (player.value?.videoInstance) {
            player.value.videoInstance.muted = muted;
        }
    },
);

watch(
    () => props.paused,
    (paused) => {
        log('暂停状态改变', paused);
        if (paused) {
            pause();
        } else {
            play();
        }
    },
);

const state = computed(() => player.value?.videoInstance?.state.value);
watch(state, (state) => {
    log('KPlayer 状态改变', state);
    // 只有 observe 时才需要自动起播/暂停
    if (props.observe && state === STATES.INIT) {
        if (props.autoPlay) {
            play();
        } else {
            pause();
        }
    }
});

let stopIntersectionObserver: UseIntersectionObserverReturn['stop'] | null = null;
if (props.observe) {
    const { stop } = useIntersectionObserver(
        playerWrapper,
        ([entry]) => {
            if (entry?.isIntersecting) {
                play();
                log('[observer] 播放');
            } else {
                pause();
                log('[observer] 暂停');
            }
        },
        {
            threshold: props.observeThreshold,
        },
    );

    stopIntersectionObserver = stop;
}

const radarConfig = computed(() => {
    if (radar) {
        log('雷达实例已初始化');
        return {
            radar,
            reportQos: true,
        };
    }

    log('雷达实例未初始化', radar);
    return undefined;
});

const debugConfig = computed(() => {
    if (radar) {
        return {
            level: LogLevel.ERROR,
            type: LogType.ONLINE,
        };
    }

    return {
        level: LogLevel.ERROR,
        type: LogType.LOCAL,
    };
});

const showFallback = computed(() => {
    const videoInstance = player.value?.videoInstance;
    const unmounted = !isMountPlayer.value;
    const videoState = videoInstance?.state.value ?? STATES.UNINIT;
    log('videoState', videoState);
    return (
        // 视频/直播降级
        props.downgrade ||
        // source 不为空
        !Boolean(props.source) ||
        unmounted ||
        [STATES.UNINIT, STATES.INIT, STATES.ERROR, STATES.PAUSED, STATES.WAITING].includes(videoState)
    );
});

onBeforeUnmount(() => {
    stopIntersectionObserver?.();
    popPlayer(isMountPlayer);
});

onUnmounted(() => {
    popupMuted.value = PopupMutedDefault;
});
</script>

<template>
    <div ref="playerWrapper" class="player-wrapper">
        <KPlayer
            v-if="isMountPlayer && !downgrade"
            ref="player"
            class="player"
            :type="props.type"
            :config="{
                enableMixAudio: false,
                src: props.source,
                fallbackSource: props.fallbackSource,
                autoplay: autoPlay,
                loop: true,
                muted: props.muted,
                // objectFit: props.objectFit,
                activityTag,
            }"
            :radar-config="radarConfig"
            :debug-config="debugConfig"
            @playing="onPlaying"
            @ended="onEnded"
            @error="onError"
            @debugLog="onDebugLog"
        >
            <slot name="controls"></slot>
        </KPlayer>
        <div v-if="poster" v-show="showFallback" class="player-fallback">
            <CdnImg class="poster" cdn-level="P1" :src="props.poster" :empty-image="props.fallbackPoster" />
            <div class="fallback-controls-wrapper">
                <slot name="fallback-controls"></slot>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.player-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.poster {
    width: 100%;
    height: 100%;
}

.player {
    width: 100%;
    height: 100%;
}

.player-fallback {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;

    .fallback-controls-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}
</style>
