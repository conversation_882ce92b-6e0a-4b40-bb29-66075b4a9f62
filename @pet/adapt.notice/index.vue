<script lang="ts">
export default {
    name: 'AdaptNotice',
};
</script>
<script setup lang="ts">
import Card from '@pet/adapt.card/index.vue';
import AvatarGroup from '@pet/adapt.avatar/avatar-group.vue';
import Popup from '@pet/adapt.popup/index.vue';
import type { PositionType } from '@pet/adapt.popup/types';
import AdaptTransition from '@pet/adapt.transition/index.vue';
import type { AdaptTransitionName as AnimationType } from '@pet/adapt.transition/types';
import { useBarInfo, isAtFourTab, isAtSearchTab } from '@pet/yau.core/device/appInfo';
import { useEventListener } from '@vueuse/core';
import { onMounted, onUpdated, onBeforeUnmount, ref, computed, watch } from 'vue-demi';

export interface NoticeProps {
    /**
     * 是否显示
     */
    show?: boolean;
    /**
     * 执行时长
     */
    duration?: number;
    /**
     * 图标
     */
    figure?: string;
    /**
     * 头像
     */
    avatar?: string | string[];
    /**
     * 标题
     */
    title?: string;
    /**
     * 描述
     */
    desc?: string;
    /**
     * 按钮文案
     */
    buttonText?: string;
    /**
     * 按钮占位文案
     */
    attachedText?: string;
    /**
     * 动效形式
     */
    aniType?: AnimationType;
    /**
     * 出现的位置
     */
    position?: PositionType;
    /**
     * 滑动消除当前通知
     */
    slideRemove?: boolean;
    /**
     *
     */
    fallbackIcon?: string;
}

export interface NoticeEmits {
    /**
     * 更新show状态
     * @arg { boolean } show
     */
    (event: 'update:show', show: boolean): void;
    /**
     * 隐藏后
     */
    (event: 'hide'): void;
    /**
     * 入场前
     */
    (event: 'before-enter'): void;
    /**
     * 入场
     */
    (event: 'enter'): void;
    /**
     * 入场后
     */
    (event: 'after-enter'): void;
    /**
     * 离场前
     */
    (event: 'before-leave'): void;
    /**
     * 离场
     */
    (event: 'leave'): void;
    /**
     * 离场后
     */
    (event: 'after-leave'): void;
    /**
     * 按钮点击
     */
    (event: 'button-click'): void;
}

const emit = defineEmits<NoticeEmits>();

const props = withDefaults(defineProps<NoticeProps>(), {
    show: false,
    duration: 2000,
    aniType: 'drawer-top',
    position: 'top',
    slideRemove: true,
});

let timer: number | null = null;
const inScreen = ref(props.show);
const { statusBarHeight } = useBarInfo();
const placeholder = computed(() => {
    return {
        paddingTop: `${statusBarHeight}px`,
    };
});
const atBarClass = computed(() => [isAtFourTab() && 'at-four-tab', isAtSearchTab() && 'at-search-tab']);
const singleAvatar = computed(() => {
    if (typeof props.avatar === 'string') {
        return props.avatar;
    } else if (props.avatar?.length === 1) {
        return props.avatar[0];
    }
});
const multiAvatars = computed(() => {
    const hasMultiItems = Array.isArray(props.avatar) && props.avatar.length > 1;
    if (hasMultiItems) {
        return props.avatar.slice(0, 2);
    }
});

watch(
    () => props.show,
    (val) => {
        if (val) {
            inScreen.value = true;
        }
    },
);

function hide() {
    emit('update:show', false);
    emit('hide');
}

function update() {
    if (timer != null) {
        clearTimeout(timer);
    }
    if (props.duration > 0) {
        timer = window.setTimeout(() => {
            hide();
        }, props.duration);
    }
}

onMounted(() => {
    update();
});

onUpdated(() => {
    update();
});

const noticeMain = ref<HTMLElement | null>(null);

let startX = 0;
let startY = 0;

function handleTouchStart(evt: TouchEvent) {
    const beginStep = evt.touches[0];
    startX = beginStep?.pageX ?? 0;
    startY = beginStep?.pageY ?? 0;
}

/**
 * 交互订的上滑操作只是视觉上的，实际上notice都是按照duration真实隐藏
 */
function handleTouchMove(evt: TouchEvent) {
    evt.preventDefault();
    if (!Boolean(startX) || (!Boolean(startY) && !props.slideRemove)) {
        return;
    }
    const moveStep = evt.touches[0];
    const endX = moveStep?.pageX ?? 0;
    const endY = moveStep?.pageY ?? 0;
    const deltaX = startX - endX;
    const deltaY = startY - endY;
    const slideUp = Math.abs(deltaX) < Math.abs(deltaY) && deltaY > 5;
    const slideDown = Math.abs(deltaX) < Math.abs(deltaY) && deltaY < -5;
    if ((props.position === 'top' && slideUp) || (props.position === 'bottom' && slideDown)) {
        inScreen.value = false;
    }
    startX = 0;
    startY = 0;
}

useEventListener(noticeMain, 'touchmove', handleTouchMove, { passive: false });
useEventListener(noticeMain, 'touchstart', handleTouchStart, { passive: true });

function beforeEnter() {
    emit('before-enter');
}

function enter() {
    emit('enter');
}

function afterEnter() {
    emit('after-enter');
}

function beforeLeave() {
    emit('before-leave');
}

function leave() {
    emit('leave');
}

function afterLeave() {
    emit('after-leave');
}

function buttonClick() {
    emit('button-click');
}

onBeforeUnmount(() => {
    if (timer != null) {
        window.clearTimeout(timer);
    }
});
</script>

<template>
    <Popup
        :show="show"
        :ani-type="aniType"
        :position="position"
        :mask-closeable="false"
        :need-tab-space="false"
        :show-close="false"
        :inner-scroll="false"
        hide-mask
        transparent
        class="notice-wrapper"
        @before-enter="beforeEnter"
        @enter="enter"
        @after-enter="afterEnter"
        @before-leave="beforeLeave"
        @leave="leave"
        @after-leave="afterLeave"
    >
        <div v-if="position === 'top'" class="notice-top-gap" :class="atBarClass" :style="placeholder" />
        <AdaptTransition :name="aniType">
            <div v-if="inScreen" ref="noticeMain" class="notice-main">
                <Card
                    v-if="!$slots.default"
                    class="notice-card"
                    :avatar="singleAvatar"
                    :figure="figure"
                    :desc="desc"
                    :button-text="buttonText"
                    :attached-text="attachedText"
                    :fallback-figure="fallbackIcon"
                    @button-click="buttonClick"
                >
                    <template v-if="multiAvatars" #figure>
                        <AvatarGroup class="notice-avatars" :width="38" :gap="13" :srcs="multiAvatars" />
                    </template>
                    <template #title>
                        <!-- eslint-disable-next-line vue/no-v-html -->
                        <div class="notice-title" v-html="title"></div>
                    </template>
                </Card>
                <slot />
            </div>
        </AdaptTransition>
        <div v-if="position === 'bottom'" class="notice-bottom-gap" />
    </Popup>
</template>
<style>
:root {
    /* inpush的层级 */
    --adapt-inpush-z-index: 99999;
    /* 卡片宽度 */
    --adapt-notice-card-width: 382px;
    /* 卡片背景色 */
    --adapt-notice-card-background: #fff;
    /* 卡片距离浏览器顶部上间距 */
    --adapt-notice-card-edge-gap: 12px;
    /* 主标题特殊色 */
    --adapt-notice-card-active-title-color: #fa2b40;
    /* 卡片阴影 */
    --adapt-notice-card-box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    /* 卡片阴影 */
    --adapt-notice-card-bottom-gap-padding-bottom: 12px;
    /* 卡片圆角 */
    --adapt-notice-card-radius: 28px;
    /* 卡片内间距 */
    --adapt-notice-card-inner-gap: 12px 16px;
    /* 卡片最右侧文案内间距 */
    --adapt-notice-card-attached-text-padding: 0 8px 0 0;
    /* 描述文字颜色 */
    --adapt-notice-card-desc-color: rgba(0, 0, 0, 0.4);
    /* 辅助文字颜色 */
    --adapt-notice-card-ctrl-color: rgba(0, 0, 0, 0.4);
    /* 标题颜色 */
    --adapt-notice-card-title-color: #222;
}
</style>
<style lang="scss" scoped>
.notice-wrapper.popup {
    z-index: var(--adapt-inpush-z-index);
}

/* stylelint-disable-next-line plugin/no-unsupported-browser-features */
.notice-main {
    pointer-events: auto;

    .notice-card {
        --adapt-card-radius: var(--adapt-notice-card-radius);
        --adapt-card-desc-color: var(--adapt-notice-card-desc-color);
        --adapt-card-inner-gap: var(--adapt-notice-card-inner-gap);
        --adapt-card-is-filled-card-background: var(--adapt-notice-card-background);
        --adapt-card-figure-size: 52px;
        --adapt-card-padding: 14px 16px;
        --adapt-card-title-desc-gap: 4px;
        --adapt-card-ctrl-color: var(--adapt-notice-card-ctrl-color);
        --adapt-card-title-color: var(--adapt-notice-card-title-color);
        --adapt-card-attached-text-padding: var(--adapt-notice-card-attached-text-padding);
        width: var(--adapt-notice-card-width);
        background: var(--adapt-notice-card-background);
        box-shadow: var(--adapt-notice-card-box-shadow);
        box-sizing: border-box;
    }

    .notice-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        :deep(b),
        :deep(.card-title-sp) {
            font-weight: inherit;
            color: var(--adapt-notice-card-active-title-color);
        }
    }
}

.notice-top-gap {
    height: var(--adapt-notice-card-edge-gap);

    &.at-four-tab,
    &.at-search-tab {
        --adapt-notice-card-edge-gap: 0;
    }
}

.notice-bottom-gap {
    box-sizing: content-box;
    height: var(--adapt-notice-card-edge-gap);
    padding-bottom: var(--adapt-notice-card-bottom-gap-padding-bottom);
}

.notice-avatars {
    --adapt-avatar-border-color: #fff;
    --adapt-avatar-border-width: 2px;
    display: flex;

    :deep(.avatar-item:first-child) {
        align-self: flex-start;
    }

    :deep(.avatar-item:nth-child(2)) {
        align-self: flex-end;
    }
}
</style>
