# @pet/adapt.layer

黑色遮罩的提示弹层，活动中经常使用的一种模态效果，使用 adapt.logo、adapt.heading、adapt.button 组件进行组合。一般包含顶部标题区，中间内容区，底部操作区

## 属性

| 属性名              | 类型                       | 默认值     | 可选值            | 说明                                   |
| ------------------- | -------------------------- | ---------- | ----------------- | -------------------------------------- |
| show                | boolean                    | false      | -                 | 是否显示                               |
| aniType             | AdaptTransitionName        | 'pop'      | -                 | 动画类型                               |
| title               | string \| [string, string] | ''         | -                 | 标题                                   |
| subTitle            | string                     | ''         | -                 | 副标题                                 |
| cover               | string                     | ''         | -                 | 中间图片                               |
| hasLogo             | boolean                    | false      | -                 | 是否有 logo                            |
| customLogo          | string                     | ''         | -                 | 自定义 logo                            |
| hideMask            | boolean                    | false      | -                 | 是否隐藏遮罩                           |
| maskCloseable       | boolean                    | false      | -                 | 遮罩可否关闭                           |
| showClose           | boolean                    | true       | -                 | 显示关闭                               |
| closePos            | ClosePosType               | 'top'      | 'top' \| 'bottom' | 关闭按钮位置                           |
| showMainBtn         | boolean                    | true       | -                 | 显示主按钮                             |
| mainBtnText         | string                     | '愉快收下' | -                 | 主按钮文案                             |
| mainBtnTextDesc     | string                     | ''         | -                 | 主按钮解释文案（plugin）               |
| mainBtnDisabled     | boolean                    | false      | -                 | 主按钮禁用                             |
| mainBtnLoading      | boolean                    | false      | -                 | 主按钮加载中                           |
| mainBtnLikeDisabled | boolean                    | false      | -                 | 主按钮不可用但响应点击                 |
| showSecondBtn       | boolean                    | false      | -                 | 显示次级按钮                           |
| secondBtnText       | string                     | ''         | -                 | 次级按钮文案                           |
| bottomText          | string                     | ''         | -                 | 主按钮下文字                           |
| innerScroll         | boolean                    | false      | -                 | 弹层内滚动                             |
| autoFly             | boolean                    | true       | -                 | 是否执行自动飞入                       |
| flyToTarget         | string                     | ''         | -                 | 关闭飞入 DomID                         |
| minFlyDuration      | number                     | 667        | -                 | 最小飞离时长                           |
| enterDelay          | number                     | 0          | -                 | 主要内容入场延迟时间                   |
| enterDuration       | number                     | 300        | -                 | 主要内容入场持续时间                   |
| addonEnterDelay     | number                     | 0          | -                 | 非主要内容入场延迟时间                 |
| addonEnterDuration  | number                     | 167        | -                 | 非主要内容入场持续时间                 |
| leaveDelay          | number                     | 0          | -                 | 主要内容离场延迟时间（非主要内容复用） |
| leaveDuration       | number                     | 233        | -                 | 主要内容离场持续时间（非主要内容复用） |
| maskEnterDelay      | number                     | 0          | -                 | 遮罩入场延迟时间                       |
| maskEnterDuration   | number                     | 233        | -                 | 遮罩入场持续时间                       |
| maskLeaveDealy      | number                     | 67         | -                 | 遮罩离场延迟时间                       |
| maskLeaveDuration   | number                     | 233        | -                 | 遮罩离场持续时间                       |

## 事件

| 事件名         | 载荷               | 说明           |
| -------------- | ------------------ | -------------- |
| update:show    | **show** `boolean` | 更新 show 状态 |
| close          |                    | 关闭按钮       |
| mask-click     |                    | 遮罩点击       |
| confirm        |                    | 主按钮点击     |
| second-click   |                    | 次按钮点击     |
| bottom-confirm |                    | 下方文字点击   |
| before-enter   |                    | 入场前         |
| enter          |                    | 入场           |
| after-enter    |                    | 入场后         |
| before-leave   |                    | 出场前         |
| leave          |                    | 出场           |
| after-leave    |                    | 出场后         |

## 插槽

| 名称         | 说明                       | 绑定 |
| ------------ | -------------------------- | ---- |
| title        | 标题区                     | -    |
| default      |                            | -    |
| button       | 按钮操作区域               | -    |
| buttonInset  | 主按钮内部                 | -    |
| buttonBottom | 按钮操作区域下方           | -    |
| bgLight      | 背光区域（可插入lottie等） | -    |

## Expose

### boot

> 控制飞入效果
