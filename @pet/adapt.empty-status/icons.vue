<script lang="ts" setup>
import ActivityHot from '@pet/adapt.icons/activityhot-svg.vue';
import Anomaly from '@pet/adapt.icons/anomaly-svg.vue';
import Band from '@pet/adapt.icons/band-svg.vue';
import Download from '@pet/adapt.icons/download-svg.vue';
import Empty from '@pet/adapt.icons/empty-svg.vue';
import End from '@pet/adapt.icons/end-svg.vue';
import Network from '@pet/adapt.icons/network-svg.vue';
import NoBody from '@pet/adapt.icons/nobody-svg.vue';
import NoGift from '@pet/adapt.icons/nogift-svg.vue';
import NoMessage from '@pet/adapt.icons/nomessage-svg.vue';
import NotStart from '@pet/adapt.icons/notstart-svg.vue';
import Protect from '@pet/adapt.icons/protect-svg.vue';
import type { Component as VueComponent } from 'vue-demi';
import { computed } from 'vue-demi';
import type { IconName } from './types';

type Icons<T extends PropertyKey> = {
    [code in T]: VueComponent;
};

const iconComponents: Icons<IconName> = {
    network: Network,
    protect: Protect,
    band: Band,
    empty: Empty,
    anomaly: Anomaly,
    notStart: NotStart,
    end: End,
    download: Download,
    noBody: NoBody,
    noGift: NoGift,
    noMessage: NoMessage,
    activityHot: ActivityHot,
    user: NoBody,
};

interface IconProps {
    /**
     * icon 名称
     */
    name?: IconName;
}
const props = withDefaults(defineProps<IconProps>(), {
    name: 'network',
});

const icon = computed(() => iconComponents[props.name]);
</script>

<template>
    <component :is="icon" />
</template>

<style lang="scss" scoped></style>
