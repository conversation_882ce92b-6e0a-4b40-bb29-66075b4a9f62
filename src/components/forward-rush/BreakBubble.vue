<script setup lang="ts">
defineProps<{ mainText: string; subText: string }>();
</script>
<template>
    <div class="break-bubble common-bg flex-column-center">
        <span class="bubble-text bubble-text-highlight">{{ mainText }}</span>
        <span class="bubble-text bubble-text-space">{{ subText }}</span>
    </div>
</template>
<style lang="scss" scoped>
@use './bubble.scss';
.break-bubble {
    width: 172px;
    height: 96px;
    @include bg('@/assets/forward-rush/bubble-bg-warn.png');
}
</style>
