import { directShare, immersiveShare, isShareError, share, ShareErrorCode } from '@ks-share/share';
import <PERSON><PERSON> from 'js-cookie';

import type { channelShareType, commonShareType } from './types';
export async function commonShare({ subBiz, type, placeholder, callback, logExt, downgrade }: commonShareType) {
    console.log('分享', subBiz, type, placeholder, callback, logExt);
    switch (type) {
        case 'normal':
            return share(
                {
                    placeholder: {
                        ...placeholder,
                    },
                    logExt: {
                        ...logExt,
                    },
                    shareObjectId: Cookie.get('ud') ?? '',
                    subBiz,
                },
                {
                    downgrade,
                    ...callback,
                },
            );
        case 'immersive':
            return immersiveShare(
                {
                    placeholder: {
                        ...placeholder,
                    },
                    logExt: {
                        ...logExt,
                    },
                    shareObjectId: Cookie.get('ud') ?? '',
                    subBiz,
                },
                {
                    downgrade,
                    ...callback,
                },
            );
        default:
            return;
    }
}

export async function channelShare({ subBiz, placeholder, channel, logExt }: channelShareType) {
    try {
        return await directShare({
            subBiz,
            shareObjectId: Cookie.get('ud') ?? '',
            placeholder: {
                ...placeholder,
            },
            channel,
            logExt: {
                ...logExt,
            },
        });
    } catch (error) {
        if (isShareError(error)) {
            const { code } = error;
            switch (code) {
                case ShareErrorCode.WECHAT_NOT_INSTALLED:
                    return {
                        quickMsg: '分享失败，未安装微信',
                        code,
                    };
                case ShareErrorCode.QQ_NOT_INSTALLED:
                    return {
                        quickMsg: '分享失败，未安装QQ',
                        code,
                    };
                case ShareErrorCode.QR_NUMBER_ERROR:
                    return {
                        quickMsg: '分享失败，二维码参数取值错误',
                        code,
                    };
                case ShareErrorCode.BRIDGE_ERROR:
                    return {
                        quickMsg: '分享失败，分享Bridge错误',
                        code,
                    };
                case ShareErrorCode.YODA_ERROR:
                    return {
                        quickMsg: '分享失败，Yoda环境错误',
                        code,
                    };
                default:
                    return {};
            }
        }
    }
}
